---
# Gitlab CI config
#
# For more options, see http://doc.gitlab.com/ce/ci/yaml/README.html

stages:
  - build

before_script:

pc_build:
  variables:
    CI_DEBUG_TRACE: "false"
  stage: build
  script:
    - nvm use 16.16.0
    - npm config set ELECTRON_MIRROR http://repo-dev.htsc/artifactory/public-npm-binary-virtual/binaries/electron/
    - npm config ls
    - yarn
    - whoami
    - if ( "$WEB_SCRIPT" -eq "build" ) { yarn make:lite:win } elseif ( "$WEB_SCRIPT" -eq "build:sit" ) { yarn make:uat:win } else { yarn make:sit:win }
    - compress-archive out/make/squirrel.windows/x64/*.exe out/lighten_exe.zip
    - compress-archive out/make/squirrel.windows/x64/RELEASES out/releases.zip
    - compress-archive out/make/squirrel.windows/x64/*.nupkg out/nupkg.zip
    - New-Item -Path . -Name "${EMAS_BUILD_ID}" -ItemType "directory"
    - Copy-Item out/lighten_exe.zip,out/releases.zip,out/nupkg.zip -Destination .\"${EMAS_BUILD_ID}"\ -PassThru
    - Dir "${EMAS_BUILD_ID}"
    - scp -pr "${EMAS_BUILD_ID}" appadmin@*************:/app/repo/release/MERCURY/pc/prd/
  tags:
    - electron_builder
  only:
    variables:
      - $EMAP_DESKTOP_TARGET_PLATFORM == "Windows"
    refs:
      - triggers

macOS_build:
  stage: build
  script:
    - npm config set registry http://npm.htsc
    - npm config set ELECTRON_MIRROR http://repo-dev.htsc/artifactory/public-npm-binary-virtual/binaries/electron/
    - npm config set npm_config_disturl http://npm.htsc:8080/dist/atom-shell
    - npm config set SASS_BINARY_SITE http://*************:7001/node-sass
    - npm config set disturl http://repo-dev.htsc/artifactory/public-npm-binary-virtual/binaries/node/
    - npm config delete arch
    - npm config ls
    - rm -rf /usr/local/lib/node_modules/create-dmg
    - rm -rf node_modules
    - yarn
    - npm install -g create-dmg
    - export CSC_IDENTITY_AUTO_DISCOVERY=true
    - export CSC_LINK=sign/lighten-mac.p12
    - export CSC_KEY_PASSWORD='htsc'
    - PYTHON_PATH=$(which python3)
    - if [ "${WEB_SCRIPT}" == "build" ]; then yarn make:prod:mac; elif [ "${WEB_SCRIPT}" == "build:sit" ]; then yarn make:uat:mac; else yarn make:sit:mac;fi
    - zip lighten_mac_dmg.zip out/make/lighten.dmg
    - zip -r lighten_mac_zip.zip out/make/zip
    - mkdir -p ${EMAS_BUILD_ID} && mv lighten_mac_dmg.zip ${EMAS_BUILD_ID} && mv lighten_mac_zip.zip ${EMAS_BUILD_ID}
    - scp -pr ${EMAS_BUILD_ID} appadmin@*************:/app/repo/release/MERCURY/pc/prd/
  tags:
    - ione_mac_runner
  only:
    variables:
      - $EMAP_DESKTOP_TARGET_PLATFORM == "macOS"
    refs:
      - triggers
