import path from 'path';
import type { Configuration } from 'webpack';
import { DefinePlugin } from 'webpack';

import { rules } from './webpack.rules';

const resolve = (dir: string) => path.resolve(__dirname, dir);

export const mainConfig: Configuration = {
  /**
   * This is the main entry point for your application, it's the first file
   * that runs in the main process.
   */
  entry: './src/main.ts',
  // Put your normal webpack config below here
  module: {
    rules,
  },
  resolve: {
    extensions: ['.js', '.ts', '.jsx', '.tsx', '.css', '.json'],
    alias: {
      '@common': resolve('../../src/common'),
      '@utils': resolve('../../src/utils'),
      '@globalState': resolve('../../src/globalState'),
      '@lightenModule': resolve('../../src/lightenModule'),
      '@htElectronSDK': resolve('../../src/htElectronSDK'),
      '@lightenSDK': resolve('../../src/lightenSDK'),
    },
  },
  plugins: [
    new DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify(
        process.env.NODE_ENV || 'development'
      ),
      'process.env.SERVER': JSON.stringify(process.env.SERVER),
    }),
  ],
};
