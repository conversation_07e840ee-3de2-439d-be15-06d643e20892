// 编译主进程的ts配置，exclude src/renderer
{
  "compilerOptions": {
    "jsx": "react",
    "target": "es6",
    "moduleResolution": "node",
    "module": "commonjs",
    "lib": [
      "dom",
      "es2015",
      "es2016",
      "es2017"
    ],
    "noImplicitAny": false,
    "sourceMap": true,
    "outDir": ".webpack",
    "baseUrl": "./",
    "allowJs": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "strictPropertyInitialization": false,
    "skipLibCheck": true,
    "allowSyntheticDefaultImports": true,
    "noEmit": false,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "paths": {
      "*": [
        "node_modules/*"
      ],
      "@common/*": [
        "src/common/*"
      ],
      "@utils/*": [
        "src/utils/*"
      ],
      "@config/*": [
        "src/config/*"
      ],
      "@os/*": [
        "src/os/*"
      ],
      "@globalState/*": [
        "src/globalState/*"
      ],
      "@lightenModule/*": [
        "src/lightenModule/*"
      ],
      "@htElectronSDK/*": [
        "src/htElectronSDK/*"
      ],
      "@lightenSDK/*": [
        "src/lightenSDK/*"
      ],
    },
    "typeRoots": [
      "src/typings",
      "src/htElectronSDK/typings",
      "node_modules/@types"
    ]
  },
  "include": [
    "src",
    "stable"
  ],
  "exclude": [
    "node_modules"
  ],
}