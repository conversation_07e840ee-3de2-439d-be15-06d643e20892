import type { ForgeConfig } from '@electron-forge/shared-types';
import { MakerSquirrel } from '@electron-forge/maker-squirrel';
import { MakerWix } from '@electron-forge/maker-wix';
import { MakerZIP } from '@electron-forge/maker-zip';
import { MakerDMG } from '@electron-forge/maker-dmg';
import { WebpackPlugin } from '@electron-forge/plugin-webpack';

import { mainConfig } from './stable/webpack/webpack.main.config';
import { rendererConfig } from './stable/webpack/webpack.renderer.config';
import path from 'path';

const config: ForgeConfig = {
  // electron-packager的配置项，全部配置项说明见官方文档：https://electron.github.io/electron-packager/main/interfaces/electronpackager.options.html#download
  // 主要是配置打包时的一些选项
  packagerConfig: {
    icon: path.join(__dirname, './src/icon/logo'),
    appBundleId: 'com.htsc.linkFlow.pc',
    // osxSign:{},
    // osxNotarize:{
    //   tool:"notarytool",
    //   appleId:"<EMAIL>",
    //   appleIdPassword:"pryn-roxm-drpw-yruc",
    //   teamId:"LN3GEFF72B"
    // },
    extraResource: [path.join(__dirname, 'src/icon/logo.ico'), path.join(__dirname, 'src/icon/logo.png'),path.join(__dirname, 'node_modules/@ht/openim-electron-client-sdk/'), path.join(__dirname, 'capture/')],
    executableName: 'LinkFlow',
    name: 'LinkFlow'
  }, //

  // electron-rebuild的配置项，https://github.com/electron/electron-rebuild
  // 一般不需要配置啥
  rebuildConfig: {},

  // 不同平台的打包选项
  makers: [
    new MakerSquirrel({
      setupExe: 'LinkFlow.exe',
      setupIcon: './src/icon/logo.ico',
      loadingGif: './src/icon/setup.gif',
      skipUpdateIcon: true,
      // setupMsi: 'LinkFlow.msi',
      // noMsi: false,
    }),
  //  new MakerWix({
  //   exe:'linkFlowMSI',
  //   language:0x0804,//zh-cn
  //   name:'linkFlowMSI',


  //  }),
    // 加上macUpdateManifestBaseUrl，才会生成升级mac的RELEASES.json
    new MakerZIP({ macUpdateManifestBaseUrl: 'https://lighten.lhzq.com:14444/lightenupdate' }, ['darwin']),
    new MakerDMG({icon: './src/icon/logo.icns', name: 'lighten'}, ['darwin']),
  ],
  plugins: [
    new WebpackPlugin({
      mainConfig,
      renderer: {
        config: rendererConfig,
        entryPoints: [
          {
            html: './stable/renderer/index.html',
            js: './stable/renderer/renderer.ts',
            name: 'linkFlow',
            preload: {
              js: './src/preload.js',
            },
          },
        ],
      },
    }),
  ],
};

export default config;
