# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.2.0":
  version "2.2.1"
  resolved "http://npm.htsc/@ampproject/remapping/download/@ampproject/remapping-2.2.1.tgz#99e8e11851128b8702cd57c33684f1d0f260b630"
  integrity sha1-mejhGFESi4cCzVfDNoTx0PJgtjA=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@babel/code-frame@7.12.11":
  version "7.12.11"
  resolved "http://npm.htsc/@babel/code-frame/download/@babel/code-frame-7.12.11.tgz#f4ad435aa263db935b8f10f2c552d23fb716a63f"
  integrity sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8=
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.16.7":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/code-frame/download/@babel/code-frame-7.22.5.tgz#234d98e1551960604f1246e6475891a570ad5658"
  integrity sha1-I02Y4VUZYGBPEkbmR1iRpXCtVlg=
  dependencies:
    "@babel/highlight" "^7.22.5"

"@babel/code-frame@^7.22.13":
  version "7.22.13"
  resolved "http://npm.htsc/@babel/code-frame/download/@babel/code-frame-7.22.13.tgz#e3c1c099402598483b7a8c46a721d1038803755e"
  integrity sha1-48HAmUAlmEg7eoxGpyHRA4gDdV4=
  dependencies:
    "@babel/highlight" "^7.22.13"
    chalk "^2.4.2"

"@babel/compat-data@^7.22.6", "@babel/compat-data@^7.22.9", "@babel/compat-data@^7.23.2":
  version "7.23.2"
  resolved "http://npm.htsc/@babel/compat-data/download/@babel/compat-data-7.23.2.tgz#6a12ced93455827037bfb5ed8492820d60fc32cc"
  integrity sha1-ahLO2TRVgnA3v7XthJKCDWD8Msw=

"@babel/core@^7.18.0":
  version "7.23.2"
  resolved "http://npm.htsc/@babel/core/download/@babel/core-7.23.2.tgz#ed10df0d580fff67c5f3ee70fd22e2e4c90a9f94"
  integrity sha1-7RDfDVgP/2fF8+5w/SLi5MkKn5Q=
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.22.13"
    "@babel/generator" "^7.23.0"
    "@babel/helper-compilation-targets" "^7.22.15"
    "@babel/helper-module-transforms" "^7.23.0"
    "@babel/helpers" "^7.23.2"
    "@babel/parser" "^7.23.0"
    "@babel/template" "^7.22.15"
    "@babel/traverse" "^7.23.2"
    "@babel/types" "^7.23.0"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/eslint-parser@^7.18.9":
  version "7.22.9"
  resolved "http://npm.htsc/@babel/eslint-parser/download/@babel/eslint-parser-7.22.9.tgz#75f8aa978d1e76c87cc6f26c1ea16ae58804d390"
  integrity sha1-dfiql40edsh8xvJsHqFq5YgE05A=
  dependencies:
    "@nicolo-ribaudo/eslint-scope-5-internals" "5.1.1-v1"
    eslint-visitor-keys "^2.1.0"
    semver "^6.3.1"

"@babel/eslint-plugin@^7.13.10":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/eslint-plugin/download/@babel/eslint-plugin-7.22.5.tgz#47407d8c9e527b62ff75ee11e4baa6de3da7cf0e"
  integrity sha1-R0B9jJ5Se2L/de4R5Lqm3j2nzw4=
  dependencies:
    eslint-rule-composer "^0.3.0"

"@babel/generator@^7.23.0":
  version "7.23.0"
  resolved "http://npm.htsc/@babel/generator/download/@babel/generator-7.23.0.tgz#df5c386e2218be505b34837acbcb874d7a983420"
  integrity sha1-31w4biIYvlBbNIN6y8uHTXqYNCA=
  dependencies:
    "@babel/types" "^7.23.0"
    "@jridgewell/gen-mapping" "^0.3.2"
    "@jridgewell/trace-mapping" "^0.3.17"
    jsesc "^2.5.1"

"@babel/helper-annotate-as-pure@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.22.5.tgz#e7f06737b197d580a01edf75d97e2c8be99d3882"
  integrity sha1-5/BnN7GX1YCgHt912X4si+mdOII=
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.22.5":
  version "7.22.15"
  resolved "http://npm.htsc/@babel/helper-builder-binary-assignment-operator-visitor/download/@babel/helper-builder-binary-assignment-operator-visitor-7.22.15.tgz#5426b109cf3ad47b91120f8328d8ab1be8b0b956"
  integrity sha1-VCaxCc861HuREg+DKNirG+iwuVY=
  dependencies:
    "@babel/types" "^7.22.15"

"@babel/helper-compilation-targets@^7.22.15", "@babel/helper-compilation-targets@^7.22.5", "@babel/helper-compilation-targets@^7.22.6":
  version "7.22.15"
  resolved "http://npm.htsc/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.22.15.tgz#0698fc44551a26cf29f18d4662d5bf545a6cfc52"
  integrity sha1-Bpj8RFUaJs8p8Y1GYtW/VFps/FI=
  dependencies:
    "@babel/compat-data" "^7.22.9"
    "@babel/helper-validator-option" "^7.22.15"
    browserslist "^4.21.9"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.22.11", "@babel/helper-create-class-features-plugin@^7.22.15", "@babel/helper-create-class-features-plugin@^7.22.5":
  version "7.22.15"
  resolved "http://npm.htsc/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.22.15.tgz#97a61b385e57fe458496fad19f8e63b63c867de4"
  integrity sha1-l6YbOF5X/kWElvrRn45jtjyGfeQ=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-environment-visitor" "^7.22.5"
    "@babel/helper-function-name" "^7.22.5"
    "@babel/helper-member-expression-to-functions" "^7.22.15"
    "@babel/helper-optimise-call-expression" "^7.22.5"
    "@babel/helper-replace-supers" "^7.22.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.22.6"
    semver "^6.3.1"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.22.5":
  version "7.22.15"
  resolved "http://npm.htsc/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.22.15.tgz#5ee90093914ea09639b01c711db0d6775e558be1"
  integrity sha1-XukAk5FOoJY5sBxxHbDWd15Vi+E=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    regexpu-core "^5.3.1"
    semver "^6.3.1"

"@babel/helper-define-polyfill-provider@^0.4.3":
  version "0.4.3"
  resolved "http://npm.htsc/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.4.3.tgz#a71c10f7146d809f4a256c373f462d9bba8cf6ba"
  integrity sha1-pxwQ9xRtgJ9KJWw3P0Ytm7qM9ro=
  dependencies:
    "@babel/helper-compilation-targets" "^7.22.6"
    "@babel/helper-plugin-utils" "^7.22.5"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"

"@babel/helper-environment-visitor@^7.22.20", "@babel/helper-environment-visitor@^7.22.5":
  version "7.22.20"
  resolved "http://npm.htsc/@babel/helper-environment-visitor/download/@babel/helper-environment-visitor-7.22.20.tgz#96159db61d34a29dba454c959f5ae4a649ba9167"
  integrity sha1-lhWdth00op26RUyVn1rkpkm6kWc=

"@babel/helper-function-name@^7.22.5", "@babel/helper-function-name@^7.23.0":
  version "7.23.0"
  resolved "http://npm.htsc/@babel/helper-function-name/download/@babel/helper-function-name-7.23.0.tgz#1f9a3cdbd5b2698a670c30d2735f9af95ed52759"
  integrity sha1-H5o829WyaYpnDDDSc1+a+V7VJ1k=
  dependencies:
    "@babel/template" "^7.22.15"
    "@babel/types" "^7.23.0"

"@babel/helper-hoist-variables@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.22.5.tgz#c01a007dac05c085914e8fb652b339db50d823bb"
  integrity sha1-wBoAfawFwIWRTo+2UrM521DYI7s=
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-member-expression-to-functions@^7.22.15":
  version "7.23.0"
  resolved "http://npm.htsc/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.23.0.tgz#9263e88cc5e41d39ec18c9a3e0eced59a3e7d366"
  integrity sha1-kmPojMXkHTnsGMmj4OztWaPn02Y=
  dependencies:
    "@babel/types" "^7.23.0"

"@babel/helper-module-imports@^7.22.15", "@babel/helper-module-imports@^7.22.5":
  version "7.22.15"
  resolved "http://npm.htsc/@babel/helper-module-imports/download/@babel/helper-module-imports-7.22.15.tgz#16146307acdc40cc00c3b2c647713076464bdbf0"
  integrity sha1-FhRjB6zcQMwAw7LGR3EwdkZL2/A=
  dependencies:
    "@babel/types" "^7.22.15"

"@babel/helper-module-transforms@^7.22.5", "@babel/helper-module-transforms@^7.23.0":
  version "7.23.0"
  resolved "http://npm.htsc/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.23.0.tgz#3ec246457f6c842c0aee62a01f60739906f7047e"
  integrity sha1-PsJGRX9shCwK7mKgH2BzmQb3BH4=
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-module-imports" "^7.22.15"
    "@babel/helper-simple-access" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.22.6"
    "@babel/helper-validator-identifier" "^7.22.20"

"@babel/helper-optimise-call-expression@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.22.5.tgz#f21531a9ccbff644fdd156b4077c16ff0c3f609e"
  integrity sha1-8hUxqcy/9kT90Va0B3wW/ww/YJ4=
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.22.5", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.22.5.tgz#dd7ee3735e8a313b9f7b05a773d892e88e6d7295"
  integrity sha1-3X7jc16KMTufewWnc9iS6I5tcpU=

"@babel/helper-remap-async-to-generator@^7.22.20", "@babel/helper-remap-async-to-generator@^7.22.5":
  version "7.22.20"
  resolved "http://npm.htsc/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.22.20.tgz#7b68e1cb4fa964d2996fd063723fb48eca8498e0"
  integrity sha1-e2jhy0+pZNKZb9Bjcj+0jsqEmOA=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-wrap-function" "^7.22.20"

"@babel/helper-replace-supers@^7.22.5", "@babel/helper-replace-supers@^7.22.9":
  version "7.22.20"
  resolved "http://npm.htsc/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.22.20.tgz#e37d367123ca98fe455a9887734ed2e16eb7a793"
  integrity sha1-4302cSPKmP5FWpiHc07S4W63p5M=
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-member-expression-to-functions" "^7.22.15"
    "@babel/helper-optimise-call-expression" "^7.22.5"

"@babel/helper-simple-access@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/helper-simple-access/download/@babel/helper-simple-access-7.22.5.tgz#4938357dc7d782b80ed6dbb03a0fba3d22b1d5de"
  integrity sha1-STg1fcfXgrgO1tuwOg+6PSKx1d4=
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-skip-transparent-expression-wrappers@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.22.5.tgz#007f15240b5751c537c40e77abb4e89eeaaa8847"
  integrity sha1-AH8VJAtXUcU3xA53q7TonuqqiEc=
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-split-export-declaration@^7.22.6":
  version "7.22.6"
  resolved "http://npm.htsc/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.22.6.tgz#322c61b7310c0997fe4c323955667f18fcefb91c"
  integrity sha1-MixhtzEMCZf+TDI5VWZ/GPzvuRw=
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-string-parser@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/helper-string-parser/download/@babel/helper-string-parser-7.22.5.tgz#533f36457a25814cf1df6488523ad547d784a99f"
  integrity sha1-Uz82RXolgUzx32SIUjrVR9eEqZ8=

"@babel/helper-validator-identifier@^7.22.20":
  version "7.22.20"
  resolved "http://npm.htsc/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.22.20.tgz#c4ae002c61d2879e724581d96665583dbc1dc0e0"
  integrity sha1-xK4ALGHSh55yRYHZZmVYPbwdwOA=

"@babel/helper-validator-identifier@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.22.5.tgz#9544ef6a33999343c8740fa51350f30eeaaaf193"
  integrity sha1-lUTvajOZk0PIdA+lE1DzDuqq8ZM=

"@babel/helper-validator-option@^7.22.15":
  version "7.22.15"
  resolved "http://npm.htsc/@babel/helper-validator-option/download/@babel/helper-validator-option-7.22.15.tgz#694c30dfa1d09a6534cdfcafbe56789d36aba040"
  integrity sha1-aUww36HQmmU0zfyvvlZ4nTaroEA=

"@babel/helper-wrap-function@^7.22.20":
  version "7.22.20"
  resolved "http://npm.htsc/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.22.20.tgz#15352b0b9bfb10fc9c76f79f6342c00e3411a569"
  integrity sha1-FTUrC5v7EPycdvefY0LADjQRpWk=
  dependencies:
    "@babel/helper-function-name" "^7.22.5"
    "@babel/template" "^7.22.15"
    "@babel/types" "^7.22.19"

"@babel/helpers@^7.23.2":
  version "7.23.2"
  resolved "http://npm.htsc/@babel/helpers/download/@babel/helpers-7.23.2.tgz#2832549a6e37d484286e15ba36a5330483cac767"
  integrity sha1-KDJUmm431IQobhW6NqUzBIPKx2c=
  dependencies:
    "@babel/template" "^7.22.15"
    "@babel/traverse" "^7.23.2"
    "@babel/types" "^7.23.0"

"@babel/highlight@^7.10.4", "@babel/highlight@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/highlight/download/@babel/highlight-7.22.5.tgz#aa6c05c5407a67ebce408162b7ede789b4d22031"
  integrity sha1-qmwFxUB6Z+vOQIFit+3nibTSIDE=
  dependencies:
    "@babel/helper-validator-identifier" "^7.22.5"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/highlight@^7.22.13":
  version "7.22.20"
  resolved "http://npm.htsc/@babel/highlight/download/@babel/highlight-7.22.20.tgz#4ca92b71d80554b01427815e06f2df965b9c1f54"
  integrity sha1-TKkrcdgFVLAUJ4FeBvLfllucH1Q=
  dependencies:
    "@babel/helper-validator-identifier" "^7.22.20"
    chalk "^2.4.2"
    js-tokens "^4.0.0"

"@babel/parser@^7.1.0", "@babel/parser@^7.20.7", "@babel/parser@^7.22.15", "@babel/parser@^7.23.0":
  version "7.23.0"
  resolved "http://npm.htsc/@babel/parser/download/@babel/parser-7.23.0.tgz#da950e622420bf96ca0d0f2909cdddac3acd8719"
  integrity sha1-2pUOYiQgv5bKDQ8pCc3drDrNhxk=

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.22.15":
  version "7.22.15"
  resolved "http://npm.htsc/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/download/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.22.15.tgz#02dc8a03f613ed5fdc29fb2f728397c78146c962"
  integrity sha1-AtyKA/YT7V/cKfsvcoOXx4FGyWI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.22.15":
  version "7.22.15"
  resolved "http://npm.htsc/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/download/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.22.15.tgz#2aeb91d337d4e1a1e7ce85b76a37f5301781200f"
  integrity sha1-KuuR0zfU4aHnzoW3ajf1MBeBIA8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"
    "@babel/plugin-transform-optional-chaining" "^7.22.15"

"@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2":
  version "7.21.0-placeholder-for-preset-env.2"
  resolved "http://registry.npm.htsc/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz#7844f9289546efa9febac2de4cfe358a050bd703"
  integrity sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "http://npm.htsc/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz#a983fb1aeb2ec3f6ed042a210f640e90e786fe0d"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  version "7.12.13"
  resolved "http://npm.htsc/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.13.tgz#b5c987274c4a3a82b89714796931a6b53544ae10"
  integrity sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  version "7.14.5"
  resolved "http://npm.htsc/@babel/plugin-syntax-class-static-block/download/@babel/plugin-syntax-class-static-block-7.14.5.tgz#195df89b146b4b78b3bf897fd7a257c84659d406"
  integrity sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  version "7.8.3"
  resolved "http://npm.htsc/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz#62bf98b2da3cd21d626154fc96ee5b3cb68eacb3"
  integrity sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  version "7.8.3"
  resolved "http://npm.htsc/@babel/plugin-syntax-export-namespace-from/download/@babel/plugin-syntax-export-namespace-from-7.8.3.tgz#028964a9ba80dbc094c915c487ad7c4e7a66465a"
  integrity sha1-AolkqbqA28CUyRXEh618TnpmRlo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-import-assertions@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-syntax-import-assertions/download/@babel/plugin-syntax-import-assertions-7.22.5.tgz#07d252e2aa0bc6125567f742cd58619cb14dce98"
  integrity sha1-B9JS4qoLxhJVZ/dCzVhhnLFNzpg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-syntax-import-attributes@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-syntax-import-attributes/download/@babel/plugin-syntax-import-attributes-7.22.5.tgz#ab840248d834410b829f569f5262b9e517555ecb"
  integrity sha1-q4QCSNg0QQuCn1afUmK55RdVXss=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-syntax-import-meta@^7.10.4":
  version "7.10.4"
  resolved "http://npm.htsc/@babel/plugin-syntax-import-meta/download/@babel/plugin-syntax-import-meta-7.10.4.tgz#ee601348c370fa334d2207be158777496521fd51"
  integrity sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "http://npm.htsc/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz#01ca21b668cd8218c9e640cb6dd88c5412b2c96a"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.22.5.tgz#a6b68e84fb76e759fc3b93e901876ffabbe1d918"
  integrity sha1-praOhPt251n8O5PpAYdv+rvh2Rg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  version "7.10.4"
  resolved "http://npm.htsc/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz#ca91ef46303530448b906652bac2e9fe9941f699"
  integrity sha1-ypHvRjA1MESLkGZSusLp/plB9pk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "http://npm.htsc/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz#167ed70368886081f74b5c36c65a88c03b66d1a9"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  version "7.10.4"
  resolved "http://npm.htsc/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz#b9b070b3e33570cd9fd07ba7fa91c0dd37b9af97"
  integrity sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "http://npm.htsc/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz#60e225edcbd98a640332a2e72dd3e66f1af55871"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "http://npm.htsc/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz#6111a265bcfb020eb9efd0fdfd7d26402b9ed6c1"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "http://npm.htsc/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz#4f69c2ab95167e0180cd5336613f8c5788f7d48a"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  version "7.14.5"
  resolved "http://npm.htsc/@babel/plugin-syntax-private-property-in-object/download/@babel/plugin-syntax-private-property-in-object-7.14.5.tgz#0dc6671ec0ea22b6e94a1114f857970cd39de1ad"
  integrity sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  version "7.14.5"
  resolved "http://npm.htsc/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.14.5.tgz#c1cfdadc35a646240001f06138247b741c34d94c"
  integrity sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-syntax-typescript/download/@babel/plugin-syntax-typescript-7.22.5.tgz#aac8d383b062c5072c647a31ef990c1d0af90272"
  integrity sha1-qsjTg7BixQcsZHox75kMHQr5AnI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-syntax-unicode-sets-regex@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-syntax-unicode-sets-regex/download/@babel/plugin-syntax-unicode-sets-regex-7.18.6.tgz#d49a3b3e6b52e5be6740022317580234a6a47357"
  integrity sha1-1Jo7PmtS5b5nQAIjF1gCNKakc1c=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-arrow-functions@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.22.5.tgz#e5ba566d0c58a5b2ba2a8b795450641950b71958"
  integrity sha1-5bpWbQxYpbK6Kot5VFBkGVC3GVg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-async-generator-functions@^7.23.2":
  version "7.23.2"
  resolved "http://npm.htsc/@babel/plugin-transform-async-generator-functions/download/@babel/plugin-transform-async-generator-functions-7.23.2.tgz#054afe290d64c6f576f371ccc321772c8ea87ebb"
  integrity sha1-BUr+KQ1kxvV283HMwyF3LI6ofrs=
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-remap-async-to-generator" "^7.22.20"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-transform-async-to-generator@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.22.5.tgz#c7a85f44e46f8952f6d27fe57c2ed3cc084c3775"
  integrity sha1-x6hfRORviVL20n/lfC7TzAhMN3U=
  dependencies:
    "@babel/helper-module-imports" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-remap-async-to-generator" "^7.22.5"

"@babel/plugin-transform-block-scoped-functions@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.22.5.tgz#27978075bfaeb9fa586d3cb63a3d30c1de580024"
  integrity sha1-J5eAdb+uufpYbTy2Oj0wwd5YACQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-block-scoping@^7.23.0":
  version "7.23.0"
  resolved "http://npm.htsc/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.23.0.tgz#8744d02c6c264d82e1a4bc5d2d501fd8aff6f022"
  integrity sha1-h0TQLGwmTYLhpLxdLVAf2K/28CI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-class-properties@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-class-properties/download/@babel/plugin-transform-class-properties-7.22.5.tgz#97a56e31ad8c9dc06a0b3710ce7803d5a48cca77"
  integrity sha1-l6VuMa2MncBqCzcQzngD1aSMync=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-class-static-block@^7.22.11":
  version "7.22.11"
  resolved "http://npm.htsc/@babel/plugin-transform-class-static-block/download/@babel/plugin-transform-class-static-block-7.22.11.tgz#dc8cc6e498f55692ac6b4b89e56d87cec766c974"
  integrity sha1-3IzG5Jj1VpKsa0uJ5W2HzsdmyXQ=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.22.11"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-transform-classes@^7.22.15":
  version "7.22.15"
  resolved "http://npm.htsc/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.22.15.tgz#aaf4753aee262a232bbc95451b4bdf9599c65a0b"
  integrity sha1-qvR1Ou4mKiMrvJVFG0vflZnGWgs=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-compilation-targets" "^7.22.15"
    "@babel/helper-environment-visitor" "^7.22.5"
    "@babel/helper-function-name" "^7.22.5"
    "@babel/helper-optimise-call-expression" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-replace-supers" "^7.22.9"
    "@babel/helper-split-export-declaration" "^7.22.6"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.22.5.tgz#cd1e994bf9f316bd1c2dafcd02063ec261bb3869"
  integrity sha1-zR6ZS/nzFr0cLa/NAgY+wmG7OGk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/template" "^7.22.5"

"@babel/plugin-transform-destructuring@^7.18.0", "@babel/plugin-transform-destructuring@^7.23.0":
  version "7.23.0"
  resolved "http://npm.htsc/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.23.0.tgz#6447aa686be48b32eaf65a73e0e2c0bd010a266c"
  integrity sha1-ZEeqaGvkizLq9lpz4OLAvQEKJmw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-dotall-regex@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.22.5.tgz#dbb4f0e45766eb544e193fb00e65a1dd3b2a4165"
  integrity sha1-27Tw5Fdm61ROGT+wDmWh3TsqQWU=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-duplicate-keys@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.22.5.tgz#b6e6428d9416f5f0bba19c70d1e6e7e0b88ab285"
  integrity sha1-tuZCjZQW9fC7oZxw0ebn4LiKsoU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-dynamic-import@^7.22.11":
  version "7.22.11"
  resolved "http://npm.htsc/@babel/plugin-transform-dynamic-import/download/@babel/plugin-transform-dynamic-import-7.22.11.tgz#2c7722d2a5c01839eaf31518c6ff96d408e447aa"
  integrity sha1-LHci0qXAGDnq8xUYxv+W1AjkR6o=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-transform-exponentiation-operator@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.22.5.tgz#402432ad544a1f9a480da865fda26be653e48f6a"
  integrity sha1-QCQyrVRKH5pIDahl/aJr5lPkj2o=
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-export-namespace-from@^7.22.11":
  version "7.22.11"
  resolved "http://npm.htsc/@babel/plugin-transform-export-namespace-from/download/@babel/plugin-transform-export-namespace-from-7.22.11.tgz#b3c84c8f19880b6c7440108f8929caf6056db26c"
  integrity sha1-s8hMjxmIC2x0QBCPiSnK9gVtsmw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-transform-for-of@^7.22.15":
  version "7.22.15"
  resolved "http://npm.htsc/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.22.15.tgz#f64b4ccc3a4f131a996388fae7680b472b306b29"
  integrity sha1-9ktMzDpPExqZY4j652gLRyswayk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-function-name@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.22.5.tgz#935189af68b01898e0d6d99658db6b164205c143"
  integrity sha1-k1GJr2iwGJjg1tmWWNtrFkIFwUM=
  dependencies:
    "@babel/helper-compilation-targets" "^7.22.5"
    "@babel/helper-function-name" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-json-strings@^7.22.11":
  version "7.22.11"
  resolved "http://npm.htsc/@babel/plugin-transform-json-strings/download/@babel/plugin-transform-json-strings-7.22.11.tgz#689a34e1eed1928a40954e37f74509f48af67835"
  integrity sha1-aJo04e7RkopAlU4390UJ9Ir2eDU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-transform-literals@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.22.5.tgz#e9341f4b5a167952576e23db8d435849b1dd7920"
  integrity sha1-6TQfS1oWeVJXbiPbjUNYSbHdeSA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-logical-assignment-operators@^7.22.11":
  version "7.22.11"
  resolved "http://npm.htsc/@babel/plugin-transform-logical-assignment-operators/download/@babel/plugin-transform-logical-assignment-operators-7.22.11.tgz#24c522a61688bde045b7d9bc3c2597a4d948fc9c"
  integrity sha1-JMUiphaIveBFt9m8PCWXpNlI/Jw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-transform-member-expression-literals@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.22.5.tgz#4fcc9050eded981a468347dd374539ed3e058def"
  integrity sha1-T8yQUO3tmBpGg0fdN0U57T4Fje8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-modules-amd@^7.23.0":
  version "7.23.0"
  resolved "http://npm.htsc/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.23.0.tgz#05b2bc43373faa6d30ca89214731f76f966f3b88"
  integrity sha1-BbK8Qzc/qm0wyokhRzH3b5ZvO4g=
  dependencies:
    "@babel/helper-module-transforms" "^7.23.0"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-modules-commonjs@^7.23.0":
  version "7.23.0"
  resolved "http://npm.htsc/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.23.0.tgz#b3dba4757133b2762c00f4f94590cf6d52602481"
  integrity sha1-s9ukdXEzsnYsAPT5RZDPbVJgJIE=
  dependencies:
    "@babel/helper-module-transforms" "^7.23.0"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-simple-access" "^7.22.5"

"@babel/plugin-transform-modules-systemjs@^7.23.0":
  version "7.23.0"
  resolved "http://npm.htsc/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.23.0.tgz#77591e126f3ff4132a40595a6cccd00a6b60d160"
  integrity sha1-d1keEm8/9BMqQFlabMzQCmtg0WA=
  dependencies:
    "@babel/helper-hoist-variables" "^7.22.5"
    "@babel/helper-module-transforms" "^7.23.0"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-validator-identifier" "^7.22.20"

"@babel/plugin-transform-modules-umd@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.22.5.tgz#4694ae40a87b1745e3775b6a7fe96400315d4f98"
  integrity sha1-RpSuQKh7F0Xjd1tqf+lkADFdT5g=
  dependencies:
    "@babel/helper-module-transforms" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-named-capturing-groups-regex@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.22.5.tgz#67fe18ee8ce02d57c855185e27e3dc959b2e991f"
  integrity sha1-Z/4Y7ozgLVfIVRheJ+PclZsumR8=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-new-target@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.22.5.tgz#1b248acea54ce44ea06dfd37247ba089fcf9758d"
  integrity sha1-GySKzqVM5E6gbf03JHugifz5dY0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-nullish-coalescing-operator@^7.22.11":
  version "7.22.11"
  resolved "http://npm.htsc/@babel/plugin-transform-nullish-coalescing-operator/download/@babel/plugin-transform-nullish-coalescing-operator-7.22.11.tgz#debef6c8ba795f5ac67cd861a81b744c5d38d9fc"
  integrity sha1-3r72yLp5X1rGfNhhqBt0TF042fw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-transform-numeric-separator@^7.22.11":
  version "7.22.11"
  resolved "http://npm.htsc/@babel/plugin-transform-numeric-separator/download/@babel/plugin-transform-numeric-separator-7.22.11.tgz#498d77dc45a6c6db74bb829c02a01c1d719cbfbd"
  integrity sha1-SY133EWmxtt0u4KcAqAcHXGcv70=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-transform-object-rest-spread@^7.22.15":
  version "7.22.15"
  resolved "http://npm.htsc/@babel/plugin-transform-object-rest-spread/download/@babel/plugin-transform-object-rest-spread-7.22.15.tgz#21a95db166be59b91cde48775310c0df6e1da56f"
  integrity sha1-IaldsWa+Wbkc3kh3UxDA324dpW8=
  dependencies:
    "@babel/compat-data" "^7.22.9"
    "@babel/helper-compilation-targets" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.22.15"

"@babel/plugin-transform-object-super@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.22.5.tgz#794a8d2fcb5d0835af722173c1a9d704f44e218c"
  integrity sha1-eUqNL8tdCDWvciFzwanXBPROIYw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-replace-supers" "^7.22.5"

"@babel/plugin-transform-optional-catch-binding@^7.22.11":
  version "7.22.11"
  resolved "http://npm.htsc/@babel/plugin-transform-optional-catch-binding/download/@babel/plugin-transform-optional-catch-binding-7.22.11.tgz#461cc4f578a127bb055527b3e77404cad38c08e0"
  integrity sha1-RhzE9XihJ7sFVSez53QEytOMCOA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-transform-optional-chaining@^7.22.15", "@babel/plugin-transform-optional-chaining@^7.23.0":
  version "7.23.0"
  resolved "http://npm.htsc/@babel/plugin-transform-optional-chaining/download/@babel/plugin-transform-optional-chaining-7.23.0.tgz#73ff5fc1cf98f542f09f29c0631647d8ad0be158"
  integrity sha1-c/9fwc+Y9ULwnynAYxZH2K0L4Vg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-transform-parameters@^7.22.15":
  version "7.22.15"
  resolved "http://npm.htsc/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.22.15.tgz#719ca82a01d177af358df64a514d64c2e3edb114"
  integrity sha1-cZyoKgHRd681jfZKUU1kwuPtsRQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-private-methods@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-private-methods/download/@babel/plugin-transform-private-methods-7.22.5.tgz#21c8af791f76674420a147ae62e9935d790f8722"
  integrity sha1-IciveR92Z0QgoUeuYumTXXkPhyI=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-private-property-in-object@^7.22.11":
  version "7.22.11"
  resolved "http://npm.htsc/@babel/plugin-transform-private-property-in-object/download/@babel/plugin-transform-private-property-in-object-7.22.11.tgz#ad45c4fc440e9cb84c718ed0906d96cf40f9a4e1"
  integrity sha1-rUXE/EQOnLhMcY7QkG2Wz0D5pOE=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-create-class-features-plugin" "^7.22.11"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-transform-property-literals@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.22.5.tgz#b5ddabd73a4f7f26cd0e20f5db48290b88732766"
  integrity sha1-td2r1zpPfybNDiD120gpC4hzJ2Y=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-react-display-name@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-react-display-name/download/@babel/plugin-transform-react-display-name-7.22.5.tgz#3c4326f9fce31c7968d6cb9debcaf32d9e279a2b"
  integrity sha1-PEMm+fzjHHlo1sud68rzLZ4nmis=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-react-jsx-development@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-react-jsx-development/download/@babel/plugin-transform-react-jsx-development-7.22.5.tgz#e716b6edbef972a92165cd69d92f1255f7e73e87"
  integrity sha1-5xa27b75cqkhZc1p2S8SVffnPoc=
  dependencies:
    "@babel/plugin-transform-react-jsx" "^7.22.5"

"@babel/plugin-transform-react-jsx@^7.22.15", "@babel/plugin-transform-react-jsx@^7.22.5":
  version "7.22.15"
  resolved "http://npm.htsc/@babel/plugin-transform-react-jsx/download/@babel/plugin-transform-react-jsx-7.22.15.tgz#7e6266d88705d7c49f11c98db8b9464531289cd6"
  integrity sha1-fmJm2IcF18SfEcmNuLlGRTEonNY=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-module-imports" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-jsx" "^7.22.5"
    "@babel/types" "^7.22.15"

"@babel/plugin-transform-react-pure-annotations@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-react-pure-annotations/download/@babel/plugin-transform-react-pure-annotations-7.22.5.tgz#1f58363eef6626d6fa517b95ac66fe94685e32c0"
  integrity sha1-H1g2Pu9mJtb6UXuVrGb+lGheMsA=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-regenerator@^7.22.10":
  version "7.22.10"
  resolved "http://npm.htsc/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.22.10.tgz#8ceef3bd7375c4db7652878b0241b2be5d0c3cca"
  integrity sha1-jO7zvXN1xNt2UoeLAkGyvl0MPMo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    regenerator-transform "^0.15.2"

"@babel/plugin-transform-reserved-words@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.22.5.tgz#832cd35b81c287c4bcd09ce03e22199641f964fb"
  integrity sha1-gyzTW4HCh8S80JzgPiIZlkH5ZPs=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-runtime@^7.18.0":
  version "7.23.2"
  resolved "http://npm.htsc/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.23.2.tgz#c956a3f8d1aa50816ff6c30c6288d66635c12990"
  integrity sha1-yVaj+NGqUIFv9sMMYojWZjXBKZA=
  dependencies:
    "@babel/helper-module-imports" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.22.5"
    babel-plugin-polyfill-corejs2 "^0.4.6"
    babel-plugin-polyfill-corejs3 "^0.8.5"
    babel-plugin-polyfill-regenerator "^0.5.3"
    semver "^6.3.1"

"@babel/plugin-transform-shorthand-properties@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.22.5.tgz#6e277654be82b5559fc4b9f58088507c24f0c624"
  integrity sha1-bid2VL6CtVWfxLn1gIhQfCTwxiQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-spread@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.22.5.tgz#6487fd29f229c95e284ba6c98d65eafb893fea6b"
  integrity sha1-ZIf9KfIpyV4oS6bJjWXq+4k/6ms=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"

"@babel/plugin-transform-sticky-regex@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.22.5.tgz#295aba1595bfc8197abd02eae5fc288c0deb26aa"
  integrity sha1-KVq6FZW/yBl6vQLq5fwojA3rJqo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-template-literals@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.22.5.tgz#8f38cf291e5f7a8e60e9f733193f0bcc10909bff"
  integrity sha1-jzjPKR5feo5g6fczGT8LzBCQm/8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-typeof-symbol@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.22.5.tgz#5e2ba478da4b603af8673ff7c54f75a97b716b34"
  integrity sha1-XiukeNpLYDr4Zz/3xU91qXtxazQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-typescript@^7.18.0", "@babel/plugin-transform-typescript@^7.22.15":
  version "7.22.15"
  resolved "http://npm.htsc/@babel/plugin-transform-typescript/download/@babel/plugin-transform-typescript-7.22.15.tgz#15adef906451d86349eb4b8764865c960eb54127"
  integrity sha1-Fa3vkGRR2GNJ60uHZIZclg61QSc=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-create-class-features-plugin" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-typescript" "^7.22.5"

"@babel/plugin-transform-unicode-escapes@^7.22.10":
  version "7.22.10"
  resolved "http://npm.htsc/@babel/plugin-transform-unicode-escapes/download/@babel/plugin-transform-unicode-escapes-7.22.10.tgz#c723f380f40a2b2f57a62df24c9005834c8616d9"
  integrity sha1-xyPzgPQKKy9Xpi3yTJAFg0yGFtk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-unicode-property-regex@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-unicode-property-regex/download/@babel/plugin-transform-unicode-property-regex-7.22.5.tgz#098898f74d5c1e86660dc112057b2d11227f1c81"
  integrity sha1-CYiY901cHoZmDcESBXstESJ/HIE=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-unicode-regex@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.22.5.tgz#ce7e7bb3ef208c4ff67e02a22816656256d7a183"
  integrity sha1-zn57s+8gjE/2fgKiKBZlYlbXoYM=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-unicode-sets-regex@^7.22.5":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/plugin-transform-unicode-sets-regex/download/@babel/plugin-transform-unicode-sets-regex-7.22.5.tgz#77788060e511b708ffc7d42fdfbc5b37c3004e91"
  integrity sha1-d3iAYOURtwj/x9Qv37xbN8MATpE=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/preset-env@^7.18.0":
  version "7.23.2"
  resolved "http://npm.htsc/@babel/preset-env/download/@babel/preset-env-7.23.2.tgz#1f22be0ff0e121113260337dbc3e58fafce8d059"
  integrity sha1-HyK+D/DhIREyYDN9vD5Y+vzo0Fk=
  dependencies:
    "@babel/compat-data" "^7.23.2"
    "@babel/helper-compilation-targets" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-validator-option" "^7.22.15"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.22.15"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.22.15"
    "@babel/plugin-proposal-private-property-in-object" "7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-import-assertions" "^7.22.5"
    "@babel/plugin-syntax-import-attributes" "^7.22.5"
    "@babel/plugin-syntax-import-meta" "^7.10.4"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-syntax-unicode-sets-regex" "^7.18.6"
    "@babel/plugin-transform-arrow-functions" "^7.22.5"
    "@babel/plugin-transform-async-generator-functions" "^7.23.2"
    "@babel/plugin-transform-async-to-generator" "^7.22.5"
    "@babel/plugin-transform-block-scoped-functions" "^7.22.5"
    "@babel/plugin-transform-block-scoping" "^7.23.0"
    "@babel/plugin-transform-class-properties" "^7.22.5"
    "@babel/plugin-transform-class-static-block" "^7.22.11"
    "@babel/plugin-transform-classes" "^7.22.15"
    "@babel/plugin-transform-computed-properties" "^7.22.5"
    "@babel/plugin-transform-destructuring" "^7.23.0"
    "@babel/plugin-transform-dotall-regex" "^7.22.5"
    "@babel/plugin-transform-duplicate-keys" "^7.22.5"
    "@babel/plugin-transform-dynamic-import" "^7.22.11"
    "@babel/plugin-transform-exponentiation-operator" "^7.22.5"
    "@babel/plugin-transform-export-namespace-from" "^7.22.11"
    "@babel/plugin-transform-for-of" "^7.22.15"
    "@babel/plugin-transform-function-name" "^7.22.5"
    "@babel/plugin-transform-json-strings" "^7.22.11"
    "@babel/plugin-transform-literals" "^7.22.5"
    "@babel/plugin-transform-logical-assignment-operators" "^7.22.11"
    "@babel/plugin-transform-member-expression-literals" "^7.22.5"
    "@babel/plugin-transform-modules-amd" "^7.23.0"
    "@babel/plugin-transform-modules-commonjs" "^7.23.0"
    "@babel/plugin-transform-modules-systemjs" "^7.23.0"
    "@babel/plugin-transform-modules-umd" "^7.22.5"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.22.5"
    "@babel/plugin-transform-new-target" "^7.22.5"
    "@babel/plugin-transform-nullish-coalescing-operator" "^7.22.11"
    "@babel/plugin-transform-numeric-separator" "^7.22.11"
    "@babel/plugin-transform-object-rest-spread" "^7.22.15"
    "@babel/plugin-transform-object-super" "^7.22.5"
    "@babel/plugin-transform-optional-catch-binding" "^7.22.11"
    "@babel/plugin-transform-optional-chaining" "^7.23.0"
    "@babel/plugin-transform-parameters" "^7.22.15"
    "@babel/plugin-transform-private-methods" "^7.22.5"
    "@babel/plugin-transform-private-property-in-object" "^7.22.11"
    "@babel/plugin-transform-property-literals" "^7.22.5"
    "@babel/plugin-transform-regenerator" "^7.22.10"
    "@babel/plugin-transform-reserved-words" "^7.22.5"
    "@babel/plugin-transform-shorthand-properties" "^7.22.5"
    "@babel/plugin-transform-spread" "^7.22.5"
    "@babel/plugin-transform-sticky-regex" "^7.22.5"
    "@babel/plugin-transform-template-literals" "^7.22.5"
    "@babel/plugin-transform-typeof-symbol" "^7.22.5"
    "@babel/plugin-transform-unicode-escapes" "^7.22.10"
    "@babel/plugin-transform-unicode-property-regex" "^7.22.5"
    "@babel/plugin-transform-unicode-regex" "^7.22.5"
    "@babel/plugin-transform-unicode-sets-regex" "^7.22.5"
    "@babel/preset-modules" "0.1.6-no-external-plugins"
    "@babel/types" "^7.23.0"
    babel-plugin-polyfill-corejs2 "^0.4.6"
    babel-plugin-polyfill-corejs3 "^0.8.5"
    babel-plugin-polyfill-regenerator "^0.5.3"
    core-js-compat "^3.31.0"
    semver "^6.3.1"

"@babel/preset-modules@0.1.6-no-external-plugins":
  version "0.1.6-no-external-plugins"
  resolved "http://npm.htsc/@babel/preset-modules/download/@babel/preset-modules-0.1.6-no-external-plugins.tgz#ccb88a2c49c817236861fee7826080573b8a923a"
  integrity sha1-zLiKLEnIFyNoYf7ngmCAVzuKkjo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/preset-react@^7.17.12":
  version "7.22.15"
  resolved "http://npm.htsc/@babel/preset-react/download/@babel/preset-react-7.22.15.tgz#9a776892b648e13cc8ca2edf5ed1264eea6b6afc"
  integrity sha1-mndokrZI4TzIyi7fXtEmTupravw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-validator-option" "^7.22.15"
    "@babel/plugin-transform-react-display-name" "^7.22.5"
    "@babel/plugin-transform-react-jsx" "^7.22.15"
    "@babel/plugin-transform-react-jsx-development" "^7.22.5"
    "@babel/plugin-transform-react-pure-annotations" "^7.22.5"

"@babel/preset-typescript@^7.17.12":
  version "7.23.2"
  resolved "http://npm.htsc/@babel/preset-typescript/download/@babel/preset-typescript-7.23.2.tgz#c8de488130b7081f7e1482936ad3de5b018beef4"
  integrity sha1-yN5IgTC3CB9+FIKTatPeWwGL7vQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-validator-option" "^7.22.15"
    "@babel/plugin-syntax-jsx" "^7.22.5"
    "@babel/plugin-transform-modules-commonjs" "^7.23.0"
    "@babel/plugin-transform-typescript" "^7.22.15"

"@babel/regjsgen@^0.8.0":
  version "0.8.0"
  resolved "http://npm.htsc/@babel/regjsgen/download/@babel/regjsgen-0.8.0.tgz#f0ba69b075e1f05fb2825b7fad991e7adbb18310"
  integrity sha1-8LppsHXh8F+yglt/rZkeetuxgxA=

"@babel/runtime@^7.18.0", "@babel/runtime@^7.8.4":
  version "7.23.2"
  resolved "http://npm.htsc/@babel/runtime/download/@babel/runtime-7.23.2.tgz#062b0ac103261d68a966c4c7baf2ae3e62ec3885"
  integrity sha1-BisKwQMmHWipZsTHuvKuPmLsOIU=
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/runtime@^7.20.7", "@babel/runtime@^7.21.0":
  version "7.22.5"
  resolved "http://npm.htsc/@babel/runtime/download/@babel/runtime-7.22.5.tgz#8564dd588182ce0047d55d7a75e93921107b57ec"
  integrity sha1-hWTdWIGCzgBH1V16dek5IRB7V+w=
  dependencies:
    regenerator-runtime "^0.13.11"

"@babel/template@^7.22.15", "@babel/template@^7.22.5":
  version "7.22.15"
  resolved "http://npm.htsc/@babel/template/download/@babel/template-7.22.15.tgz#09576efc3830f0430f4548ef971dde1350ef2f38"
  integrity sha1-CVdu/Dgw8EMPRUjvlx3eE1DvLzg=
  dependencies:
    "@babel/code-frame" "^7.22.13"
    "@babel/parser" "^7.22.15"
    "@babel/types" "^7.22.15"

"@babel/traverse@^7.23.2":
  version "7.23.2"
  resolved "http://npm.htsc/@babel/traverse/download/@babel/traverse-7.23.2.tgz#329c7a06735e144a506bdb2cad0268b7f46f4ad8"
  integrity sha1-Mpx6BnNeFEpQa9ssrQJot/RvStg=
  dependencies:
    "@babel/code-frame" "^7.22.13"
    "@babel/generator" "^7.23.0"
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-function-name" "^7.23.0"
    "@babel/helper-hoist-variables" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.22.6"
    "@babel/parser" "^7.23.0"
    "@babel/types" "^7.23.0"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.18.0", "@babel/types@^7.20.7", "@babel/types@^7.22.15", "@babel/types@^7.22.19", "@babel/types@^7.22.5", "@babel/types@^7.23.0", "@babel/types@^7.4.4":
  version "7.23.0"
  resolved "http://npm.htsc/@babel/types/download/@babel/types-7.23.0.tgz#8c1f020c9df0e737e4e247c0619f58c68458aaeb"
  integrity sha1-jB8CDJ3w5zfk4kfAYZ9YxoRYqus=
  dependencies:
    "@babel/helper-string-parser" "^7.22.5"
    "@babel/helper-validator-identifier" "^7.22.20"
    to-fast-properties "^2.0.0"

"@bitdisaster/exe-icon-extractor@^1.0.10":
  version "1.0.10"
  resolved "http://registry.npm.htsc/@bitdisaster/exe-icon-extractor/-/exe-icon-extractor-1.0.10.tgz#3f5107864254c351db1db5c5922452d9d4154e8f"
  integrity sha512-iTZ8cVGZ5dglNRyFdSj8U60mHIrC8XNIuOHN/NkM5/dQP4nsmpyqeQTAADLLQgoFCNJD+DiwQCv8dR2cCeWP4g==

"@cspotcode/source-map-support@^0.8.0":
  version "0.8.1"
  resolved "http://npm.htsc/@cspotcode/source-map-support/download/@cspotcode/source-map-support-0.8.1.tgz#00629c35a688e05a88b1cda684fb9d5e73f000a1"
  integrity sha1-AGKcNaaI4FqIsc2mhPudXnPwAKE=
  dependencies:
    "@jridgewell/trace-mapping" "0.3.9"

"@csstools/selector-specificity@^2.0.2":
  version "2.2.0"
  resolved "http://npm.htsc/@csstools/selector-specificity/download/@csstools/selector-specificity-2.2.0.tgz#2cbcf822bf3764c9658c4d2e568bd0c0cb748016"
  integrity sha1-LLz4Ir83ZMlljE0uVovQwMt0gBY=

"@electron-forge/async-ora@6.0.0-beta.74":
  version "6.0.0-beta.74"
  resolved "http://registry.npm.htsc/@electron-forge/async-ora/-/async-ora-6.0.0-beta.74.tgz#ff0631ee8e43f7f6bed3e8c79fab31220982aad9"
  integrity sha512-3DLfCyxN42/9OB/EbU27d6vusIV4KVYgyAWg5PGzCqOscF6htAY5ksMxv1e0ip52Bsbfm5T+g/LQ0ZVqpyTSeA==
  dependencies:
    chalk "^4.0.0"
    debug "^4.3.1"
    log-symbols "^4.0.0"
    ora "^5.0.0"
    pretty-ms "^7.0.0"

"@electron-forge/cli@^6.1.1":
  version "6.1.1"
  resolved "http://npm.htsc/@electron-forge/cli/download/@electron-forge/cli-6.1.1.tgz#671b81f365570a385b40d9726b3d9a027c503782"
  integrity sha1-ZxuB82VXCjhbQNlyaz2aAnxQN4I=
  dependencies:
    "@electron-forge/core" "6.1.1"
    "@electron-forge/shared-types" "6.1.1"
    "@electron/get" "^2.0.0"
    chalk "^4.0.0"
    commander "^4.1.1"
    debug "^4.3.1"
    fs-extra "^10.0.0"
    listr2 "^5.0.3"
    semver "^7.2.1"

"@electron-forge/core-utils@6.1.1":
  version "6.1.1"
  resolved "http://npm.htsc/@electron-forge/core-utils/download/@electron-forge/core-utils-6.1.1.tgz#4eaa3b3d5f4d0b888c3500e3b6f4f0b975100c2c"
  integrity sha1-Tqo7PV9NC4iMNQDjtvTwuXUQDCw=
  dependencies:
    "@electron-forge/shared-types" "6.1.1"
    "@electron/rebuild" "^3.2.10"
    "@malept/cross-spawn-promise" "^2.0.0"
    chalk "^4.0.0"
    debug "^4.3.1"
    find-up "^5.0.0"
    fs-extra "^10.0.0"
    log-symbols "^4.0.0"
    semver "^7.2.1"
    yarn-or-npm "^3.0.1"

"@electron-forge/core@6.1.1":
  version "6.1.1"
  resolved "http://npm.htsc/@electron-forge/core/download/@electron-forge/core-6.1.1.tgz#09995ca65c3b35efef24695fdb8b8033585965ac"
  integrity sha1-CZlcplw7Ne/vJGlf24uAM1hZZaw=
  dependencies:
    "@electron-forge/core-utils" "6.1.1"
    "@electron-forge/maker-base" "6.1.1"
    "@electron-forge/plugin-base" "6.1.1"
    "@electron-forge/publisher-base" "6.1.1"
    "@electron-forge/shared-types" "6.1.1"
    "@electron-forge/template-base" "6.1.1"
    "@electron-forge/template-vite" "6.1.1"
    "@electron-forge/template-webpack" "6.1.1"
    "@electron-forge/template-webpack-typescript" "6.1.1"
    "@electron/get" "^2.0.0"
    "@electron/rebuild" "^3.2.10"
    "@malept/cross-spawn-promise" "^2.0.0"
    chalk "^4.0.0"
    debug "^4.3.1"
    electron-packager "^17.1.1"
    fast-glob "^3.2.7"
    filenamify "^4.1.0"
    find-up "^5.0.0"
    fs-extra "^10.0.0"
    got "^11.8.5"
    interpret "^3.1.1"
    listr2 "^5.0.3"
    lodash "^4.17.20"
    log-symbols "^4.0.0"
    node-fetch "^2.6.7"
    progress "^2.0.3"
    rechoir "^0.8.0"
    resolve-package "^1.0.1"
    semver "^7.2.1"
    source-map-support "^0.5.13"
    sudo-prompt "^9.1.1"
    username "^5.1.0"
    yarn-or-npm "^3.0.1"

"@electron-forge/maker-base@6.0.0-beta.74":
  version "6.0.0-beta.74"
  resolved "http://registry.npm.htsc/@electron-forge/maker-base/-/maker-base-6.0.0-beta.74.tgz#78893f2818909dd9856f868c1bd77a89888f1856"
  integrity sha512-qUsh4Zv5xqxlZkjFOuy/FZrV3gMQmcHYmkMLx9gFgl9chX8kunSsQC51Vbp6i2/2ladpadHzNDWpf4olIA5jaw==
  dependencies:
    "@electron-forge/shared-types" "6.0.0-beta.74"
    fs-extra "^10.0.0"
    which "^2.0.2"

"@electron-forge/maker-base@6.0.5":
  version "6.0.5"
  resolved "http://npm.htsc/@electron-forge/maker-base/download/@electron-forge/maker-base-6.0.5.tgz#09dc0f458ca1d15ce93be9210ea44e01dca911cb"
  integrity sha1-CdwPRYyh0VzpO+khDqROAdypEcs=
  dependencies:
    "@electron-forge/shared-types" "6.0.5"
    fs-extra "^10.0.0"
    which "^2.0.2"

"@electron-forge/maker-base@6.1.1":
  version "6.1.1"
  resolved "http://npm.htsc/@electron-forge/maker-base/download/@electron-forge/maker-base-6.1.1.tgz#2c8fdaaaa28ff39fbf4ff26e664dc79265f96b49"
  integrity sha1-LI/aqqKP85+/T/JuZk3HkmX5a0k=
  dependencies:
    "@electron-forge/shared-types" "6.1.1"
    fs-extra "^10.0.0"
    which "^2.0.2"

"@electron-forge/maker-deb@^6.1.1":
  version "6.1.1"
  resolved "http://npm.htsc/@electron-forge/maker-deb/download/@electron-forge/maker-deb-6.1.1.tgz#6482e239b09438f57cbdfd60b9f6474fbf09c334"
  integrity sha1-ZILiObCUOPV8vf1gufZHT78JwzQ=
  dependencies:
    "@electron-forge/maker-base" "6.1.1"
    "@electron-forge/shared-types" "6.1.1"
  optionalDependencies:
    electron-installer-debian "^3.0.0"

"@electron-forge/maker-dmg@^6.0.5":
  version "6.0.5"
  resolved "http://npm.htsc/@electron-forge/maker-dmg/download/@electron-forge/maker-dmg-6.0.5.tgz#4bb64efb73d78aac121a544307b7bc4559e26352"
  integrity sha1-S7ZO+3PXiqwSGlRDB7e8RVniY1I=
  dependencies:
    "@electron-forge/maker-base" "6.0.5"
    "@electron-forge/shared-types" "6.0.5"
    fs-extra "^10.0.0"
  optionalDependencies:
    electron-installer-dmg "^4.0.0"

"@electron-forge/maker-rpm@^6.1.1":
  version "6.1.1"
  resolved "http://npm.htsc/@electron-forge/maker-rpm/download/@electron-forge/maker-rpm-6.1.1.tgz#ab532e3f5d975708f0e7d5cbe8764563b8869e75"
  integrity sha1-q1MuP12XVwjw59XL6HZFY7iGnnU=
  dependencies:
    "@electron-forge/maker-base" "6.1.1"
    "@electron-forge/shared-types" "6.1.1"
  optionalDependencies:
    electron-installer-redhat "^3.2.0"

"@electron-forge/maker-squirrel@^6.1.1":
  version "6.1.1"
  resolved "http://npm.htsc/@electron-forge/maker-squirrel/download/@electron-forge/maker-squirrel-6.1.1.tgz#34bcaf25f3872d8394826ab9237ad2716111eb06"
  integrity sha1-NLyvJfOHLYOUgmq5I3rScWER6wY=
  dependencies:
    "@electron-forge/maker-base" "6.1.1"
    "@electron-forge/shared-types" "6.1.1"
    fs-extra "^10.0.0"
  optionalDependencies:
    electron-winstaller "^5.0.0"

"@electron-forge/maker-wix@^6.0.0-beta.74":
  version "6.0.0-beta.74"
  resolved "http://registry.npm.htsc/@electron-forge/maker-wix/-/maker-wix-6.0.0-beta.74.tgz#daca7dfeb98fe333b00e675501a8530de4fd6c60"
  integrity sha512-o9FNhLnxjEn0RkjeEomO/WeOnAuHdHfugUNnLpeBaWPvBUsWIp9kg3IJ9oDfIK/OJHQoLq25VrO9I75mzOMUzg==
  dependencies:
    "@electron-forge/maker-base" "6.0.0-beta.74"
    "@electron-forge/shared-types" "6.0.0-beta.74"
    chalk "^4.0.0"
    electron-wix-msi "^5.0.0"
    log-symbols "^4.0.0"
    parse-author "^2.0.0"

"@electron-forge/maker-zip@^6.1.1":
  version "6.1.1"
  resolved "http://npm.htsc/@electron-forge/maker-zip/download/@electron-forge/maker-zip-6.1.1.tgz#fba5cd18b6f33497f3aadbdade5c0a2142ca9e9a"
  integrity sha1-+6XNGLbzNJfzqtva3lwKIULKnpo=
  dependencies:
    "@electron-forge/maker-base" "6.1.1"
    "@electron-forge/shared-types" "6.1.1"
    cross-zip "^4.0.0"
    fs-extra "^10.0.0"
    got "^11.8.5"

"@electron-forge/plugin-base@6.1.1":
  version "6.1.1"
  resolved "http://npm.htsc/@electron-forge/plugin-base/download/@electron-forge/plugin-base-6.1.1.tgz#73f4688654cd29591af533be53c2a3a656405331"
  integrity sha1-c/RohlTNKVka9TO+U8KjplZAUzE=
  dependencies:
    "@electron-forge/shared-types" "6.1.1"

"@electron-forge/plugin-webpack@^6.1.1":
  version "6.1.1"
  resolved "http://npm.htsc/@electron-forge/plugin-webpack/download/@electron-forge/plugin-webpack-6.1.1.tgz#70065698a7b7848dcea6b33b10ac83d5d9cf5192"
  integrity sha1-cAZWmKe3hI3OprM7EKyD1dnPUZI=
  dependencies:
    "@electron-forge/core-utils" "6.1.1"
    "@electron-forge/plugin-base" "6.1.1"
    "@electron-forge/shared-types" "6.1.1"
    "@electron-forge/web-multi-logger" "6.1.1"
    chalk "^4.0.0"
    debug "^4.3.1"
    fs-extra "^10.0.0"
    html-webpack-plugin "^5.3.1"
    webpack "^5.69.1"
    webpack-dev-server "^4.0.0"
    webpack-merge "^5.7.3"

"@electron-forge/publisher-base@6.1.1":
  version "6.1.1"
  resolved "http://npm.htsc/@electron-forge/publisher-base/download/@electron-forge/publisher-base-6.1.1.tgz#45e53554613b9ff9bca18cd2b4c8aab5ba85f099"
  integrity sha1-ReU1VGE7n/m8oYzStMiqtbqF8Jk=
  dependencies:
    "@electron-forge/shared-types" "6.1.1"

"@electron-forge/shared-types@6.0.0-beta.74":
  version "6.0.0-beta.74"
  resolved "http://registry.npm.htsc/@electron-forge/shared-types/-/shared-types-6.0.0-beta.74.tgz#6d35aebe281b97d3f22655519e6ee609b7fb9b32"
  integrity sha512-FFiSEXk5fxM2+2YFCjsIxD4iZlT47pWPqiM8xSBlopSifQmYygXFwnRWBN0oeOwE3IFRZ5GHREVEgTVJO01RTQ==
  dependencies:
    "@electron-forge/async-ora" "6.0.0-beta.74"
    "@electron/rebuild" "^3.2.10"
    electron-packager "^17.1.0"
    listr2 "^5.0.3"
    ora "^5.0.0"

"@electron-forge/shared-types@6.0.5":
  version "6.0.5"
  resolved "http://npm.htsc/@electron-forge/shared-types/download/@electron-forge/shared-types-6.0.5.tgz#8b265687fad188799384f88fba821f8f0eac689e"
  integrity sha1-iyZWh/rRiHmThPiPuoIfjw6saJ4=
  dependencies:
    "@electron/rebuild" "^3.2.10"
    electron-packager "^17.1.1"
    listr2 "^5.0.3"

"@electron-forge/shared-types@6.1.1":
  version "6.1.1"
  resolved "http://npm.htsc/@electron-forge/shared-types/download/@electron-forge/shared-types-6.1.1.tgz#f08cf783bf0a086331784b9f13110b4fade84846"
  integrity sha1-8Iz3g78KCGMxeEufExELT63oSEY=
  dependencies:
    "@electron/rebuild" "^3.2.10"
    electron-packager "^17.1.1"
    listr2 "^5.0.3"

"@electron-forge/template-base@6.1.1":
  version "6.1.1"
  resolved "http://npm.htsc/@electron-forge/template-base/download/@electron-forge/template-base-6.1.1.tgz#ff6f32735491a235f404bf0cb069c8737fbfc4d4"
  integrity sha1-/28yc1SRojX0BL8MsGnIc3+/xNQ=
  dependencies:
    "@electron-forge/shared-types" "6.1.1"
    "@malept/cross-spawn-promise" "^2.0.0"
    debug "^4.3.1"
    fs-extra "^10.0.0"
    username "^5.1.0"

"@electron-forge/template-vite@6.1.1":
  version "6.1.1"
  resolved "http://npm.htsc/@electron-forge/template-vite/download/@electron-forge/template-vite-6.1.1.tgz#b4eff93c1f9c4e6eeb60d0c71359c36f0c485a13"
  integrity sha1-tO/5PB+cTm7rYNDHE1nDbwxIWhM=
  dependencies:
    "@electron-forge/shared-types" "6.1.1"
    "@electron-forge/template-base" "6.1.1"
    fs-extra "^10.0.0"

"@electron-forge/template-webpack-typescript@6.1.1":
  version "6.1.1"
  resolved "http://npm.htsc/@electron-forge/template-webpack-typescript/download/@electron-forge/template-webpack-typescript-6.1.1.tgz#422a9e93daa9ed957dc3ee28d9d5092a8ee3397b"
  integrity sha1-Qiqek9qp7ZV9w+4o2dUJKo7jOXs=
  dependencies:
    "@electron-forge/shared-types" "6.1.1"
    "@electron-forge/template-base" "6.1.1"
    fs-extra "^10.0.0"

"@electron-forge/template-webpack@6.1.1":
  version "6.1.1"
  resolved "http://npm.htsc/@electron-forge/template-webpack/download/@electron-forge/template-webpack-6.1.1.tgz#34413a0c21a68b70ea06b00c8b9d76df2cf6cbab"
  integrity sha1-NEE6DCGmi3DqBrAMi5123yz2y6s=
  dependencies:
    "@electron-forge/shared-types" "6.1.1"
    "@electron-forge/template-base" "6.1.1"
    fs-extra "^10.0.0"

"@electron-forge/web-multi-logger@6.1.1":
  version "6.1.1"
  resolved "http://npm.htsc/@electron-forge/web-multi-logger/download/@electron-forge/web-multi-logger-6.1.1.tgz#56325455aa37a3c677602753084d9317ff8d9fb9"
  integrity sha1-VjJUVao3o8Z3YCdTCE2TF/+Nn7k=
  dependencies:
    express "^4.17.1"
    express-ws "^5.0.2"
    xterm "^4.9.0"
    xterm-addon-fit "^0.5.0"
    xterm-addon-search "^0.8.0"

"@electron/asar@^3.2.1":
  version "3.2.4"
  resolved "http://npm.htsc/@electron/asar/download/@electron/asar-3.2.4.tgz#7e8635a3c4f6d8b3f8ae6efaf5ecb9fbf3bd9864"
  integrity sha1-foY1o8T22LP4rm769ey5+/O9mGQ=
  dependencies:
    chromium-pickle-js "^0.2.0"
    commander "^5.0.0"
    glob "^7.1.6"
    minimatch "^3.0.4"

"@electron/get@^2.0.0":
  version "2.0.2"
  resolved "http://npm.htsc/@electron/get/download/@electron/get-2.0.2.tgz#ae2a967b22075e9c25aaf00d5941cd79c21efd7e"
  integrity sha1-riqWeyIHXpwlqvANWUHNecIe/X4=
  dependencies:
    debug "^4.1.1"
    env-paths "^2.2.0"
    fs-extra "^8.1.0"
    got "^11.8.5"
    progress "^2.0.3"
    semver "^6.2.0"
    sumchecker "^3.0.1"
  optionalDependencies:
    global-agent "^3.0.0"

"@electron/notarize@^1.2.3":
  version "1.2.3"
  resolved "http://npm.htsc/@electron/notarize/download/@electron/notarize-1.2.3.tgz#38056a629e5a0b5fd56c975c4828c0f74285b644"
  integrity sha1-OAVqYp5aC1/VbJdcSCjA90KFtkQ=
  dependencies:
    debug "^4.1.1"
    fs-extra "^9.0.1"

"@electron/osx-sign@^1.0.1":
  version "1.0.4"
  resolved "http://npm.htsc/@electron/osx-sign/download/@electron/osx-sign-1.0.4.tgz#8e91442846471636ca0469426a82b253b9170151"
  integrity sha1-jpFEKEZHFjbKBGlCaoKyU7kXAVE=
  dependencies:
    compare-version "^0.1.2"
    debug "^4.3.4"
    fs-extra "^10.0.0"
    isbinaryfile "^4.0.8"
    minimist "^1.2.6"
    plist "^3.0.5"

"@electron/osx-sign@^1.0.5":
  version "1.3.3"
  resolved "http://registry.npm.htsc/@electron/osx-sign/-/osx-sign-1.3.3.tgz#af751510488318d9f7663694af85819690d75583"
  integrity sha512-KZ8mhXvWv2rIEgMbWZ4y33bDHyUKMXnx4M0sTyPNK/vcB81ImdeY9Ggdqy0SWbMDgmbqyQ+phgejh6V3R2QuSg==
  dependencies:
    compare-version "^0.1.2"
    debug "^4.3.4"
    fs-extra "^10.0.0"
    isbinaryfile "^4.0.8"
    minimist "^1.2.6"
    plist "^3.0.5"

"@electron/rebuild@^3.2.10":
  version "3.2.13"
  resolved "http://npm.htsc/@electron/rebuild/download/@electron/rebuild-3.2.13.tgz#98fbb98981b1a86162546a2ab91b2355569cca4c"
  integrity sha1-mPu5iYGxqGFiVGoquRsjVVacykw=
  dependencies:
    "@malept/cross-spawn-promise" "^2.0.0"
    chalk "^4.0.0"
    debug "^4.1.1"
    detect-libc "^2.0.1"
    fs-extra "^10.0.0"
    got "^11.7.0"
    node-abi "^3.0.0"
    node-api-version "^0.1.4"
    node-gyp "^9.0.0"
    ora "^5.1.0"
    semver "^7.3.5"
    tar "^6.0.5"
    yargs "^17.0.1"

"@electron/universal@^1.3.2":
  version "1.3.4"
  resolved "http://npm.htsc/@electron/universal/download/@electron/universal-1.3.4.tgz#bccd94b635d7c85eeed5eabba457eb4ed2be2777"
  integrity sha1-vM2UtjXXyF7u1eq7pFfrTtK+J3c=
  dependencies:
    "@electron/asar" "^3.2.1"
    "@malept/cross-spawn-promise" "^1.1.0"
    debug "^4.3.1"
    dir-compare "^3.0.0"
    fs-extra "^9.0.1"
    minimatch "^3.0.4"
    plist "^3.0.4"

"@electron/windows-sign@^1.1.2":
  version "1.2.2"
  resolved "http://registry.npm.htsc/@electron/windows-sign/-/windows-sign-1.2.2.tgz#8ceaad52d5c1eb18702f48103d5f3bc7c338fa9d"
  integrity sha512-dfZeox66AvdPtb2lD8OsIIQh12Tp0GNCRUDfBHIKGpbmopZto2/A8nSpYYLoedPIHpqkeblZ/k8OV0Gy7PYuyQ==
  dependencies:
    cross-dirname "^0.1.0"
    debug "^4.3.4"
    fs-extra "^11.1.1"
    minimist "^1.2.8"
    postject "^1.0.0-alpha.6"

"@eslint-community/eslint-utils@^4.2.0":
  version "4.4.0"
  resolved "http://npm.htsc/@eslint-community/eslint-utils/download/@eslint-community/eslint-utils-4.4.0.tgz#a23514e8fb9af1269d5f7788aa556798d61c6b59"
  integrity sha1-ojUU6Pua8SadX3eIqlVnmNYca1k=
  dependencies:
    eslint-visitor-keys "^3.3.0"

"@eslint-community/regexpp@^4.4.0":
  version "4.5.1"
  resolved "http://npm.htsc/@eslint-community/regexpp/download/@eslint-community/regexpp-4.5.1.tgz#cdd35dce4fa1a89a4fd42b1599eb35b3af408884"
  integrity sha1-zdNdzk+hqJpP1CsVmes1s69AiIQ=

"@eslint/eslintrc@^0.4.3":
  version "0.4.3"
  resolved "http://npm.htsc/@eslint/eslintrc/download/@eslint/eslintrc-0.4.3.tgz#9e42981ef035beb3dd49add17acb96e8ff6f394c"
  integrity sha1-nkKYHvA1vrPdSa3ResuW6P9vOUw=
  dependencies:
    ajv "^6.12.4"
    debug "^4.1.1"
    espree "^7.3.0"
    globals "^13.9.0"
    ignore "^4.0.6"
    import-fresh "^3.2.1"
    js-yaml "^3.13.1"
    minimatch "^3.0.4"
    strip-json-comments "^3.1.1"

"@hapi/hoek@^9.0.0":
  version "9.3.0"
  resolved "http://npm.htsc/@hapi/hoek/download/@hapi/hoek-9.3.0.tgz#8368869dcb735be2e7f5cb7647de78e167a251fb"
  integrity sha1-g2iGnctzW+Ln9ct2R9544WeiUfs=

"@hapi/topo@^5.0.0":
  version "5.1.0"
  resolved "http://npm.htsc/@hapi/topo/download/@hapi/topo-5.1.0.tgz#dc448e332c6c6e37a4dc02fd84ba8d44b9afb012"
  integrity sha1-3ESOMyxsbjek3AL9hLqNRLmvsBI=
  dependencies:
    "@hapi/hoek" "^9.0.0"

"@ht/eslint-config-htsc@2.0.20":
  version "2.0.20"
  resolved "http://npm.htsc/@ht/eslint-config-htsc/download/@ht/eslint-config-htsc-2.0.20.tgz#32e908d804d54c654aaf388c7540e69ef4a7118f"
  integrity sha1-MukI2ATVTGVKrziMdUDmnvSnEY8=
  dependencies:
    "@babel/eslint-parser" "^7.18.9"
    "@babel/eslint-plugin" "^7.13.10"
    "@modern-js-app/eslint-config" "1.2.4"
    "@typescript-eslint/eslint-plugin" "^5.12.1"
    "@typescript-eslint/parser" "^5.12.1"
    eslint "^7.32.0"
    eslint-config-airbnb "^18.2.1"
    eslint-config-prettier "^8.3.0"
    eslint-import-resolver-webpack "^0.13.1"
    eslint-plugin-eslint-comments "^3.1.1"
    eslint-plugin-filenames "^1.3.2"
    eslint-plugin-import "^2.22.1"
    eslint-plugin-jsx-a11y "^6.4.1"
    eslint-plugin-markdown "^2.2.0"
    eslint-plugin-node "^11.1.0"
    eslint-plugin-prettier "^3.4.1"
    eslint-plugin-promise "^5.1.0"
    eslint-plugin-react "^7.24.0"
    eslint-plugin-react-hooks "^4.2.0"
    eslint-plugin-vue "8.7.1"
    postcss "^8.4.16"
    postcss-html "^1.5.0"
    postcss-less "^6.0.0"
    prettier "^2.7.1"
    stylelint "^14.9.1"
    stylelint-config-css-modules "^4.1.0"
    stylelint-config-prettier "^9.0.3"
    stylelint-config-standard "^26.0.0"
    stylelint-declaration-block-no-ignored-properties "^2.5.0"
    stylelint-prettier "^2.0.0"
    typescript "^4.0.0"

"@ht/openim-electron-client-sdk@0.15.0-beta.1":
  version "0.15.0-beta.1"
  resolved "http://registry.npm.htsc/@ht/openim-electron-client-sdk/-/openim-electron-client-sdk-0.15.0-beta.1.tgz#20afec5864d8d1afb0b51626e28bdb9b1ed21041"
  integrity sha512-Ze2ii7q4vk17XtOpUOoqp0zkzxgwMWgOfAXu92od5cfdr8FWRB1qCMSi4xUEm37AfFUFQj1PhsfNu3jpKd0+VQ==
  dependencies:
    koffi "2.8.0"
    uuid "^9.0.0"

"@ht/<EMAIL>.6":
  version "0.12.0-beta.dev.6"
  resolved "http://registry.npm.htsc/@ht/openim-wasm-client-sdk/-/openim-wasm-client-sdk-0.12.0-beta.dev.6.tgz#906ced5ee8895553a331699019e2347d6aaf1fb6"
  integrity sha512-FQFNwRZOd9tTJqHBEurZhFSjF/CopMysvJsiLeQoQahfa0FEp6EwK6uRv54MVLGk7KiaaBku6DUE5+Xh3rKHZg==

"@humanwhocodes/config-array@^0.5.0":
  version "0.5.0"
  resolved "http://npm.htsc/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.5.0.tgz#1407967d4c6eecd7388f83acf1eaf4d0c6e58ef9"
  integrity sha1-FAeWfUxu7Nc4j4Os8er00Mbljvk=
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.0"
    debug "^4.1.1"
    minimatch "^3.0.4"

"@humanwhocodes/object-schema@^1.2.0":
  version "1.2.1"
  resolved "http://npm.htsc/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-1.2.1.tgz#b520529ec21d8e5945a1851dfd1c32e94e39ff45"
  integrity sha1-tSBSnsIdjllFoYUd/Rwy6U45/0U=

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  resolved "http://npm.htsc/@isaacs/cliui/download/@isaacs/cliui-8.0.2.tgz#b37667b7bc181c168782259bab42474fbf52b550"
  integrity sha1-s3Znt7wYHBaHgiWbq0JHT79StVA=
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@jridgewell/gen-mapping@^0.3.0", "@jridgewell/gen-mapping@^0.3.2":
  version "0.3.3"
  resolved "http://npm.htsc/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.3.tgz#7e02e6eb5df901aaedb08514203b096614024098"
  integrity sha1-fgLm6135AartsIUUIDsJZhQCQJg=
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@3.1.0":
  version "3.1.0"
  resolved "http://npm.htsc/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.0.tgz#2203b118c157721addfe69d47b70465463066d78"
  integrity sha1-IgOxGMFXchrd/mnUe3BGVGMGbXg=

"@jridgewell/resolve-uri@^3.0.3":
  version "3.1.1"
  resolved "http://npm.htsc/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.1.tgz#c08679063f279615a3326583ba3a90d1d82cc721"
  integrity sha1-wIZ5Bj8nlhWjMmWDujqQ0dgsxyE=

"@jridgewell/set-array@^1.0.1":
  version "1.1.2"
  resolved "http://npm.htsc/@jridgewell/set-array/download/@jridgewell/set-array-1.1.2.tgz#7c6cf998d6d20b914c0a55a91ae928ff25965e72"
  integrity sha1-fGz5mNbSC5FMClWpGuko/yWWXnI=

"@jridgewell/source-map@^0.3.3":
  version "0.3.3"
  resolved "http://npm.htsc/@jridgewell/source-map/download/@jridgewell/source-map-0.3.3.tgz#8108265659d4c33e72ffe14e33d6cc5eb59f2fda"
  integrity sha1-gQgmVlnUwz5y/+FOM9bMXrWfL9o=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/sourcemap-codec@1.4.14":
  version "1.4.14"
  resolved "http://npm.htsc/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.4.14.tgz#add4c98d341472a289190b424efbdb096991bb24"
  integrity sha1-rdTJjTQUcqKJGQtCTvvbCWmRuyQ=

"@jridgewell/sourcemap-codec@^1.4.10":
  version "1.4.15"
  resolved "http://npm.htsc/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.4.15.tgz#d7c6e6755c78567a951e04ab52ef0fd26de59f32"
  integrity sha1-18bmdVx4VnqVHgSrUu8P0m3lnzI=

"@jridgewell/trace-mapping@0.3.9":
  version "0.3.9"
  resolved "http://npm.htsc/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.9.tgz#6534fd5933a53ba7cbf3a17615e273a0d1273ff9"
  integrity sha1-ZTT9WTOlO6fL86F2FeJzoNEnP/k=
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@jridgewell/trace-mapping@^0.3.17", "@jridgewell/trace-mapping@^0.3.9":
  version "0.3.18"
  resolved "http://npm.htsc/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.18.tgz#25783b2086daf6ff1dcb53c9249ae480e4dd4cd6"
  integrity sha1-JXg7IIba9v8dy1PJJJrkgOTdTNY=
  dependencies:
    "@jridgewell/resolve-uri" "3.1.0"
    "@jridgewell/sourcemap-codec" "1.4.14"

"@leichtgewicht/ip-codec@^2.0.1":
  version "2.0.4"
  resolved "http://npm.htsc/@leichtgewicht/ip-codec/download/@leichtgewicht/ip-codec-2.0.4.tgz#b2ac626d6cb9c8718ab459166d4bb405b8ffa78b"
  integrity sha1-sqxibWy5yHGKtFkWbUu0Bbj/p4s=

"@malept/cross-spawn-promise@^1.0.0", "@malept/cross-spawn-promise@^1.1.0":
  version "1.1.1"
  resolved "http://npm.htsc/@malept/cross-spawn-promise/download/@malept/cross-spawn-promise-1.1.1.tgz#504af200af6b98e198bce768bc1730c6936ae01d"
  integrity sha1-UEryAK9rmOGYvOdovBcwxpNq4B0=
  dependencies:
    cross-spawn "^7.0.1"

"@malept/cross-spawn-promise@^2.0.0":
  version "2.0.0"
  resolved "http://npm.htsc/@malept/cross-spawn-promise/download/@malept/cross-spawn-promise-2.0.0.tgz#d0772de1aa680a0bfb9ba2f32b4c828c7857cb9d"
  integrity sha1-0Hct4apoCgv7m6LzK0yCjHhXy50=
  dependencies:
    cross-spawn "^7.0.1"

"@modern-js-app/eslint-config@1.2.4":
  version "1.2.4"
  resolved "http://npm.htsc/@modern-js-app/eslint-config/download/@modern-js-app/eslint-config-1.2.4.tgz#dff49b67ad8d3e2d0c141f5459fa48cf1bcba8d3"
  integrity sha1-3/SbZ62NPi0MFB9UWfpIzxvLqNM=
  dependencies:
    "@modern-js/babel-preset-app" "^1.2.0"

"@modern-js/babel-preset-app@^1.2.0":
  version "1.22.8"
  resolved "http://npm.htsc/@modern-js/babel-preset-app/download/@modern-js/babel-preset-app-1.22.8.tgz#de848dbef7be906f44130645209431dba4922e5f"
  integrity sha1-3oSNvve+kG9EEwZFIJQx26SSLl8=
  dependencies:
    "@babel/core" "^7.18.0"
    "@babel/plugin-transform-destructuring" "^7.18.0"
    "@babel/runtime" "^7.18.0"
    "@babel/types" "^7.18.0"
    "@modern-js/babel-preset-base" "1.22.8"
    "@modern-js/utils" "1.22.8"
    core-js "^3.25.0"

"@modern-js/babel-preset-base@1.22.8":
  version "1.22.8"
  resolved "http://npm.htsc/@modern-js/babel-preset-base/download/@modern-js/babel-preset-base-1.22.8.tgz#4ca889a1d9610078b1acbd8e55f76f4aa8e89158"
  integrity sha1-TKiJodlhAHixrL2OVfdvSqjokVg=
  dependencies:
    "@babel/core" "^7.18.0"
    "@babel/plugin-transform-runtime" "^7.18.0"
    "@babel/plugin-transform-typescript" "^7.18.0"
    "@babel/preset-env" "^7.18.0"
    "@babel/preset-react" "^7.17.12"
    "@babel/preset-typescript" "^7.17.12"
    "@babel/runtime" "^7.18.0"
    "@modern-js/utils" "1.22.8"
    "@types/babel__core" "^7.1.16"
    cosmiconfig "^7.0.1"
    lodash "^4.17.21"
    resolve "^1.22.0"

"@modern-js/utils@1.22.8":
  version "1.22.8"
  resolved "http://npm.htsc/@modern-js/utils/download/@modern-js/utils-1.22.8.tgz#eae0569dd5ef57cc509b7b4cd023247e4c27f36f"
  integrity sha1-6uBWndXvV8xQm3tM0CMkfkwn828=
  dependencies:
    caniuse-lite "^1.0.30001332"
    lodash "^4.17.21"

"@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1":
  version "5.1.1-v1"
  resolved "http://npm.htsc/@nicolo-ribaudo/eslint-scope-5-internals/download/@nicolo-ribaudo/eslint-scope-5-internals-5.1.1-v1.tgz#dbf733a965ca47b1973177dc0bb6c889edcfb129"
  integrity sha1-2/czqWXKR7GXMXfcC7bIie3PsSk=
  dependencies:
    eslint-scope "5.1.1"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "http://npm.htsc/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz#7619c2eb21b25483f6d167548b4cfd5a7488c3d5"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "http://npm.htsc/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz#5bd262af94e9d25bd1e71b05deed44876a222e8b"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "http://npm.htsc/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz#e95737e8bb6746ddedf69c556953494f196fe69a"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@npmcli/fs@^3.1.0":
  version "3.1.0"
  resolved "http://npm.htsc/@npmcli/fs/download/@npmcli/fs-3.1.0.tgz#233d43a25a91d68c3a863ba0da6a3f00924a173e"
  integrity sha1-Iz1DolqR1ow6hjug2mo/AJJKFz4=
  dependencies:
    semver "^7.3.5"

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"
  resolved "http://npm.htsc/@pkgjs/parseargs/download/@pkgjs/parseargs-0.11.0.tgz#a77ea742fab25775145434eb1d2328cf5013ac33"
  integrity sha1-p36nQvqyV3UUVDTrHSMoz1ATrDM=

"@sideway/address@^4.1.3":
  version "4.1.4"
  resolved "http://npm.htsc/@sideway/address/download/@sideway/address-4.1.4.tgz#03dccebc6ea47fdc226f7d3d1ad512955d4783f0"
  integrity sha1-A9zOvG6kf9wib309GtUSlV1Hg/A=
  dependencies:
    "@hapi/hoek" "^9.0.0"

"@sideway/formula@^3.0.1":
  version "3.0.1"
  resolved "http://npm.htsc/@sideway/formula/download/@sideway/formula-3.0.1.tgz#80fcbcbaf7ce031e0ef2dd29b1bfc7c3f583611f"
  integrity sha1-gPy8uvfOAx4O8t0psb/Hw/WDYR8=

"@sideway/pinpoint@^2.0.0":
  version "2.0.0"
  resolved "http://npm.htsc/@sideway/pinpoint/download/@sideway/pinpoint-2.0.0.tgz#cff8ffadc372ad29fd3f78277aeb29e632cc70df"
  integrity sha1-z/j/rcNyrSn9P3gneusp5jLMcN8=

"@sindresorhus/is@^4.0.0":
  version "4.6.0"
  resolved "http://npm.htsc/@sindresorhus/is/download/@sindresorhus/is-4.6.0.tgz#3c7c9c46e678feefe7a2e5bb609d3dbd665ffb3f"
  integrity sha1-PHycRuZ4/u/nouW7YJ09vWZf+z8=

"@szmarczak/http-timer@^4.0.5":
  version "4.0.6"
  resolved "http://npm.htsc/@szmarczak/http-timer/download/@szmarczak/http-timer-4.0.6.tgz#b4a914bb62e7c272d4e5989fe4440f812ab1d807"
  integrity sha1-tKkUu2LnwnLU5Zif5EQPgSqx2Ac=
  dependencies:
    defer-to-connect "^2.0.0"

"@tootallnate/once@2":
  version "2.0.0"
  resolved "http://npm.htsc/@tootallnate/once/download/@tootallnate/once-2.0.0.tgz#f544a148d3ab35801c1f633a7441fd87c2e484bf"
  integrity sha1-9UShSNOrNYAcH2M6dEH9h8LkhL8=

"@tsconfig/node10@^1.0.7":
  version "1.0.9"
  resolved "http://npm.htsc/@tsconfig/node10/download/@tsconfig/node10-1.0.9.tgz#df4907fc07a886922637b15e02d4cebc4c0021b2"
  integrity sha1-30kH/AeohpImN7FeAtTOvEwAIbI=

"@tsconfig/node12@^1.0.7":
  version "1.0.11"
  resolved "http://npm.htsc/@tsconfig/node12/download/@tsconfig/node12-1.0.11.tgz#ee3def1f27d9ed66dac6e46a295cffb0152e058d"
  integrity sha1-7j3vHyfZ7WbaxuRqKVz/sBUuBY0=

"@tsconfig/node14@^1.0.0":
  version "1.0.3"
  resolved "http://npm.htsc/@tsconfig/node14/download/@tsconfig/node14-1.0.3.tgz#e4386316284f00b98435bf40f72f75a09dabf6c1"
  integrity sha1-5DhjFihPALmENb9A9y91oJ2r9sE=

"@tsconfig/node16@^1.0.2":
  version "1.0.3"
  resolved "http://npm.htsc/@tsconfig/node16/download/@tsconfig/node16-1.0.3.tgz#472eaab5f15c1ffdd7f8628bd4c4f753995ec79e"
  integrity sha1-Ry6qtfFcH/3X+GKL1MT3U5lex54=

"@types/babel__core@^7.1.16":
  version "7.20.3"
  resolved "http://npm.htsc/@types/babel__core/download/@types/babel__core-7.20.3.tgz#d5625a50b6f18244425a1359a858c73d70340778"
  integrity sha1-1WJaULbxgkRCWhNZqFjHPXA0B3g=
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.6.6"
  resolved "http://npm.htsc/@types/babel__generator/download/@types/babel__generator-7.6.6.tgz#676f89f67dc8ddaae923f70ebc5f1fa800c031a8"
  integrity sha1-Z2+J9n3I3arpI/cOvF8fqADAMag=
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.3"
  resolved "http://npm.htsc/@types/babel__template/download/@types/babel__template-7.4.3.tgz#db9ac539a2fe05cfe9e168b24f360701bde41f5f"
  integrity sha1-25rFOaL+Bc/p4WiyTzYHAb3kH18=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*":
  version "7.20.3"
  resolved "http://npm.htsc/@types/babel__traverse/download/@types/babel__traverse-7.20.3.tgz#a971aa47441b28ef17884ff945d0551265a2d058"
  integrity sha1-qXGqR0QbKO8XiE/5RdBVEmWi0Fg=
  dependencies:
    "@babel/types" "^7.20.7"

"@types/body-parser@*":
  version "1.19.2"
  resolved "http://npm.htsc/@types/body-parser/download/@types/body-parser-1.19.2.tgz#aea2059e28b7658639081347ac4fab3de166e6f0"
  integrity sha1-rqIFnii3ZYY5CBNHrE+rPeFm5vA=
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/bonjour@^3.5.9":
  version "3.5.10"
  resolved "http://npm.htsc/@types/bonjour/download/@types/bonjour-3.5.10.tgz#0f6aadfe00ea414edc86f5d106357cda9701e275"
  integrity sha1-D2qt/gDqQU7chvXRBjV82pcB4nU=
  dependencies:
    "@types/node" "*"

"@types/cacheable-request@^6.0.1":
  version "6.0.3"
  resolved "http://npm.htsc/@types/cacheable-request/download/@types/cacheable-request-6.0.3.tgz#a430b3260466ca7b5ca5bfd735693b36e7a9d183"
  integrity sha1-pDCzJgRmyntcpb/XNWk7Nuep0YM=
  dependencies:
    "@types/http-cache-semantics" "*"
    "@types/keyv" "^3.1.4"
    "@types/node" "*"
    "@types/responselike" "^1.0.0"

"@types/connect-history-api-fallback@^1.3.5":
  version "1.5.0"
  resolved "http://npm.htsc/@types/connect-history-api-fallback/download/@types/connect-history-api-fallback-1.5.0.tgz#9fd20b3974bdc2bcd4ac6567e2e0f6885cb2cf41"
  integrity sha1-n9ILOXS9wrzUrGVn4uD2iFyyz0E=
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  version "3.4.35"
  resolved "http://npm.htsc/@types/connect/download/@types/connect-3.4.35.tgz#5fcf6ae445e4021d1fc2219a4873cc73a3bb2ad1"
  integrity sha1-X89q5EXkAh0fwiGaSHPMc6O7KtE=
  dependencies:
    "@types/node" "*"

"@types/eslint-scope@^3.7.3":
  version "3.7.4"
  resolved "http://npm.htsc/@types/eslint-scope/download/@types/eslint-scope-3.7.4.tgz#37fc1223f0786c39627068a12e94d6e6fc61de16"
  integrity sha1-N/wSI/B4bDlicGihLpTW5vxh3hY=
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*":
  version "8.37.0"
  resolved "http://npm.htsc/@types/eslint/download/@types/eslint-8.37.0.tgz#29cebc6c2a3ac7fea7113207bf5a828fdf4d7ef1"
  integrity sha1-Kc68bCo6x/6nETIHv1qCj99NfvE=
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^1.0.0":
  version "1.0.1"
  resolved "http://npm.htsc/@types/estree/download/@types/estree-1.0.1.tgz#aa22750962f3bf0e79d753d3cc067f010c95f194"
  integrity sha1-qiJ1CWLzvw5511PTzAZ/AQyV8ZQ=

"@types/express-serve-static-core@*", "@types/express-serve-static-core@^4.17.33":
  version "4.17.35"
  resolved "http://npm.htsc/@types/express-serve-static-core/download/@types/express-serve-static-core-4.17.35.tgz#c95dd4424f0d32e525d23812aa8ab8e4d3906c4f"
  integrity sha1-yV3UQk8NMuUl0jgSqoq45NOQbE8=
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*", "@types/express@^4.17.13":
  version "4.17.17"
  resolved "http://npm.htsc/@types/express/download/@types/express-4.17.17.tgz#01d5437f6ef9cfa8668e616e13c2f2ac9a491ae4"
  integrity sha1-AdVDf275z6hmjmFuE8LyrJpJGuQ=
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.33"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/fs-extra@^9.0.1":
  version "9.0.13"
  resolved "http://npm.htsc/@types/fs-extra/download/@types/fs-extra-9.0.13.tgz#7594fbae04fe7f1918ce8b3d213f74ff44ac1f45"
  integrity sha1-dZT7rgT+fxkYzos9IT90/0SsH0U=
  dependencies:
    "@types/node" "*"

"@types/glob@^7.1.1":
  version "7.2.0"
  resolved "http://npm.htsc/@types/glob/download/@types/glob-7.2.0.tgz#bc1b5bf3aa92f25bd5dd39f35c57361bdce5b2eb"
  integrity sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/html-minifier-terser@^6.0.0":
  version "6.1.0"
  resolved "http://npm.htsc/@types/html-minifier-terser/download/@types/html-minifier-terser-6.1.0.tgz#4fc33a00c1d0c16987b1a20cf92d20614c55ac35"
  integrity sha1-T8M6AMHQwWmHsaIM+S0gYUxVrDU=

"@types/http-cache-semantics@*":
  version "4.0.1"
  resolved "http://npm.htsc/@types/http-cache-semantics/download/@types/http-cache-semantics-4.0.1.tgz#0ea7b61496902b95890dc4c3a116b60cb8dae812"
  integrity sha1-Dqe2FJaQK5WJDcTDoRa2DLja6BI=

"@types/http-proxy@^1.17.8":
  version "1.17.11"
  resolved "http://npm.htsc/@types/http-proxy/download/@types/http-proxy-1.17.11.tgz#0ca21949a5588d55ac2b659b69035c84bd5da293"
  integrity sha1-DKIZSaVYjVWsK2WbaQNchL1dopM=
  dependencies:
    "@types/node" "*"

"@types/json-schema@*", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  version "7.0.12"
  resolved "http://npm.htsc/@types/json-schema/download/@types/json-schema-7.0.12.tgz#d70faba7039d5fca54c83c7dbab41051d2b6f6cb"
  integrity sha1-1w+rpwOdX8pUyDx9urQQUdK29ss=

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "http://npm.htsc/@types/json5/download/@types/json5-0.0.29.tgz#ee28707ae94e11d2b827bcbe5270bcea7f3e71ee"
  integrity sha1-7ihweulOEdK4J7y+UnC86n8+ce4=

"@types/keyv@^3.1.4":
  version "3.1.4"
  resolved "http://npm.htsc/@types/keyv/download/@types/keyv-3.1.4.tgz#3ccdb1c6751b0c7e52300bcdacd5bcbf8faa75b6"
  integrity sha1-PM2xxnUbDH5SMAvNrNW8v4+qdbY=
  dependencies:
    "@types/node" "*"

"@types/mdast@^3.0.0":
  version "3.0.12"
  resolved "http://npm.htsc/@types/mdast/download/@types/mdast-3.0.12.tgz#beeb511b977c875a5b0cc92eab6fcac2f0895514"
  integrity sha1-vutRG5d8h1pbDMkuq2/KwvCJVRQ=
  dependencies:
    "@types/unist" "^2"

"@types/mime@*":
  version "3.0.1"
  resolved "http://npm.htsc/@types/mime/download/@types/mime-3.0.1.tgz#5f8f2bca0a5863cb69bc0b0acd88c96cb1d4ae10"
  integrity sha1-X48rygpYY8tpvAsKzYjJbLHUrhA=

"@types/mime@^1":
  version "1.3.2"
  resolved "http://npm.htsc/@types/mime/download/@types/mime-1.3.2.tgz#93e25bf9ee75fe0fd80b594bc4feb0e862111b5a"
  integrity sha1-k+Jb+e51/g/YC1lLxP6w6GIRG1o=

"@types/minimatch@*":
  version "5.1.2"
  resolved "http://npm.htsc/@types/minimatch/download/@types/minimatch-5.1.2.tgz#07508b45797cb81ec3f273011b054cd0755eddca"
  integrity sha1-B1CLRXl8uB7D8nMBGwVM0HVe3co=

"@types/minimist@^1.2.0":
  version "1.2.2"
  resolved "http://npm.htsc/@types/minimist/download/@types/minimist-1.2.2.tgz#ee771e2ba4b3dc5b372935d549fd9617bf345b8c"
  integrity sha1-7nceK6Sz3Fs3KTXVSf2WF780W4w=

"@types/node@*":
  version "20.3.2"
  resolved "http://npm.htsc/@types/node/download/@types/node-20.3.2.tgz#fa6a90f2600e052a03c18b8cb3fd83dd4e599898"
  integrity sha1-+mqQ8mAOBSoDwYuMs/2D3U5ZmJg=

"@types/node@^22.7.7":
  version "22.15.30"
  resolved "http://registry.npm.htsc/@types/node/-/node-22.15.30.tgz#3a20431783e28dd0b0326f84ab386a2ec81d921d"
  integrity sha512-6Q7lr06bEHdlfplU6YRbgG1SFBdlsfNC4/lX+SkhiTs0cpJkOElmWls8PxDFv4yY/xKb8Y6SO0OmSX4wgqTZbA==
  dependencies:
    undici-types "~6.21.0"

"@types/normalize-package-data@^2.4.0":
  version "2.4.1"
  resolved "http://npm.htsc/@types/normalize-package-data/download/@types/normalize-package-data-2.4.1.tgz#d3357479a0fdfdd5907fe67e17e0a85c906e1301"
  integrity sha1-0zV0eaD9/dWQf+Z+F+CoXJBuEwE=

"@types/parse-json@^4.0.0":
  version "4.0.0"
  resolved "http://npm.htsc/@types/parse-json/download/@types/parse-json-4.0.0.tgz#2f8bb441434d163b35fb8ffdccd7138927ffb8c0"
  integrity sha1-L4u0QUNNFjs1+4/9zNcTiSf/uMA=

"@types/prop-types@*":
  version "15.7.5"
  resolved "http://npm.htsc/@types/prop-types/download/@types/prop-types-15.7.5.tgz#5f19d2b85a98e9558036f6a3cacc8819420f05cf"
  integrity sha1-XxnSuFqY6VWANvajysyIGUIPBc8=

"@types/qs@*":
  version "6.9.7"
  resolved "http://npm.htsc/@types/qs/download/@types/qs-6.9.7.tgz#63bb7d067db107cc1e457c303bc25d511febf6cb"
  integrity sha1-Y7t9Bn2xB8weRXwwO8JdUR/r9ss=

"@types/range-parser@*":
  version "1.2.4"
  resolved "http://npm.htsc/@types/range-parser/download/@types/range-parser-1.2.4.tgz#cd667bcfdd025213aafb7ca5915a932590acdcdc"
  integrity sha1-zWZ7z90CUhOq+3ylkVqTJZCs3Nw=

"@types/react-dom@^17.0.0":
  version "17.0.20"
  resolved "http://npm.htsc/@types/react-dom/download/@types/react-dom-17.0.20.tgz#e0c8901469d732b36d8473b40b679ad899da1b53"
  integrity sha1-4MiQFGnXMrNthHO0C2ea2JnaG1M=
  dependencies:
    "@types/react" "^17"

"@types/react@^17", "@types/react@^17.0.0":
  version "17.0.62"
  resolved "http://npm.htsc/@types/react/download/@types/react-17.0.62.tgz#2efe8ddf8533500ec44b1334dd1a97caa2f860e3"
  integrity sha1-Lv6N34UzUA7ESxM03RqXyqL4YOM=
  dependencies:
    "@types/prop-types" "*"
    "@types/scheduler" "*"
    csstype "^3.0.2"

"@types/responselike@^1.0.0":
  version "1.0.0"
  resolved "http://npm.htsc/@types/responselike/download/@types/responselike-1.0.0.tgz#251f4fe7d154d2bad125abe1b429b23afd262e29"
  integrity sha1-JR9P59FU0rrRJavhtCmyOv0mLik=
  dependencies:
    "@types/node" "*"

"@types/retry@0.12.0":
  version "0.12.0"
  resolved "http://npm.htsc/@types/retry/download/@types/retry-0.12.0.tgz#2b35eccfcee7d38cd72ad99232fbd58bffb3c84d"
  integrity sha1-KzXsz87n04zXKtmSMvvVi/+zyE0=

"@types/scheduler@*":
  version "0.16.3"
  resolved "http://npm.htsc/@types/scheduler/download/@types/scheduler-0.16.3.tgz#cef09e3ec9af1d63d2a6cc5b383a737e24e6dcf5"
  integrity sha1-zvCePsmvHWPSpsxbODpzfiTm3PU=

"@types/semver@^7.3.12", "@types/semver@^7.3.6":
  version "7.5.0"
  resolved "http://npm.htsc/@types/semver/download/@types/semver-7.5.0.tgz#591c1ce3a702c45ee15f47a42ade72c2fd78978a"
  integrity sha1-WRwc46cCxF7hX0ekKt5ywv14l4o=

"@types/send@*":
  version "0.17.1"
  resolved "http://npm.htsc/@types/send/download/@types/send-0.17.1.tgz#ed4932b8a2a805f1fe362a70f4e62d0ac994e301"
  integrity sha1-7UkyuKKoBfH+Nipw9OYtCsmU4wE=
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-index@^1.9.1":
  version "1.9.1"
  resolved "http://npm.htsc/@types/serve-index/download/@types/serve-index-1.9.1.tgz#1b5e85370a192c01ec6cec4735cf2917337a6278"
  integrity sha1-G16FNwoZLAHsbOxHNc8pFzN6Yng=
  dependencies:
    "@types/express" "*"

"@types/serve-static@*", "@types/serve-static@^1.13.10":
  version "1.15.1"
  resolved "http://npm.htsc/@types/serve-static/download/@types/serve-static-1.15.1.tgz#86b1753f0be4f9a1bee68d459fcda5be4ea52b5d"
  integrity sha1-hrF1Pwvk+aG+5o1Fn82lvk6lK10=
  dependencies:
    "@types/mime" "*"
    "@types/node" "*"

"@types/sockjs@^0.3.33":
  version "0.3.33"
  resolved "http://npm.htsc/@types/sockjs/download/@types/sockjs-0.3.33.tgz#570d3a0b99ac995360e3136fd6045113b1bd236f"
  integrity sha1-Vw06C5msmVNg4xNv1gRRE7G9I28=
  dependencies:
    "@types/node" "*"

"@types/unist@^2", "@types/unist@^2.0.2":
  version "2.0.7"
  resolved "http://npm.htsc/@types/unist/download/@types/unist-2.0.7.tgz#5b06ad6894b236a1d2bd6b2f07850ca5c59cf4d6"
  integrity sha1-WwataJSyNqHSvWsvB4UMpcWc9NY=

"@types/ws@^8.5.5":
  version "8.5.5"
  resolved "http://npm.htsc/@types/ws/download/@types/ws-8.5.5.tgz#af587964aa06682702ee6dcbc7be41a80e4b28eb"
  integrity sha1-r1h5ZKoGaCcC7m3Lx75BqA5LKOs=
  dependencies:
    "@types/node" "*"

"@types/yauzl@^2.9.1":
  version "2.10.0"
  resolved "http://npm.htsc/@types/yauzl/download/@types/yauzl-2.10.0.tgz#b3248295276cf8c6f153ebe6a9aba0c988cb2599"
  integrity sha1-sySClSds+MbxU+vmqaugyYjLJZk=
  dependencies:
    "@types/node" "*"

"@typescript-eslint/eslint-plugin@^5.12.1":
  version "5.62.0"
  resolved "http://npm.htsc/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-5.62.0.tgz#aeef0328d172b9e37d9bab6dbc13b87ed88977db"
  integrity sha1-ru8DKNFyueN9m6ttvBO4ftiJd9s=
  dependencies:
    "@eslint-community/regexpp" "^4.4.0"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/type-utils" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    debug "^4.3.4"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    natural-compare-lite "^1.4.0"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/parser@^5.12.1":
  version "5.62.0"
  resolved "http://npm.htsc/@typescript-eslint/parser/download/@typescript-eslint/parser-5.62.0.tgz#1b63d082d849a2fcae8a569248fbe2ee1b8a56c7"
  integrity sha1-G2PQgthJovyuilaSSPvi7huKVsc=
  dependencies:
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@5.62.0":
  version "5.62.0"
  resolved "http://npm.htsc/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-5.62.0.tgz#d9457ccc6a0b8d6b37d0eb252a23022478c5460c"
  integrity sha1-2UV8zGoLjWs30OslKiMCJHjFRgw=
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"

"@typescript-eslint/type-utils@5.62.0":
  version "5.62.0"
  resolved "http://npm.htsc/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-5.62.0.tgz#286f0389c41681376cdad96b309cedd17d70346a"
  integrity sha1-KG8DicQWgTds2tlrMJzt0X1wNGo=
  dependencies:
    "@typescript-eslint/typescript-estree" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    debug "^4.3.4"
    tsutils "^3.21.0"

"@typescript-eslint/types@5.62.0":
  version "5.62.0"
  resolved "http://npm.htsc/@typescript-eslint/types/download/@typescript-eslint/types-5.62.0.tgz#258607e60effa309f067608931c3df6fed41fd2f"
  integrity sha1-JYYH5g7/ownwZ2CJMcPfb+1B/S8=

"@typescript-eslint/typescript-estree@5.62.0":
  version "5.62.0"
  resolved "http://npm.htsc/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-5.62.0.tgz#7d17794b77fabcac615d6a48fb143330d962eb9b"
  integrity sha1-fRd5S3f6vKxhXWpI+xQzMNli65s=
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/utils@5.62.0":
  version "5.62.0"
  resolved "http://npm.htsc/@typescript-eslint/utils/download/@typescript-eslint/utils-5.62.0.tgz#141e809c71636e4a75daa39faed2fb5f4b10df86"
  integrity sha1-FB6AnHFjbkp12qOfrtL7X0sQ34Y=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@types/json-schema" "^7.0.9"
    "@types/semver" "^7.3.12"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    eslint-scope "^5.1.1"
    semver "^7.3.7"

"@typescript-eslint/visitor-keys@5.62.0":
  version "5.62.0"
  resolved "http://npm.htsc/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-5.62.0.tgz#2174011917ce582875954ffe2f6912d5931e353e"
  integrity sha1-IXQBGRfOWCh1lU/+L2kS1ZMeNT4=
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    eslint-visitor-keys "^3.3.0"

"@vercel/webpack-asset-relocator-loader@1.7.3":
  version "1.7.3"
  resolved "http://npm.htsc/@vercel/webpack-asset-relocator-loader/download/@vercel/webpack-asset-relocator-loader-1.7.3.tgz#e65ca1fd9feb045039788f9b4710e5acc84b01b0"
  integrity sha1-5lyh/Z/rBFA5eI+bRxDlrMhLAbA=
  dependencies:
    resolve "^1.10.0"

"@webassemblyjs/ast@1.11.6", "@webassemblyjs/ast@^1.11.5":
  version "1.11.6"
  resolved "http://npm.htsc/@webassemblyjs/ast/download/@webassemblyjs/ast-1.11.6.tgz#db046555d3c413f8966ca50a95176a0e2c642e24"
  integrity sha1-2wRlVdPEE/iWbKUKlRdqDixkLiQ=
  dependencies:
    "@webassemblyjs/helper-numbers" "1.11.6"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"

"@webassemblyjs/floating-point-hex-parser@1.11.6":
  version "1.11.6"
  resolved "http://npm.htsc/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.11.6.tgz#dacbcb95aff135c8260f77fa3b4c5fea600a6431"
  integrity sha1-2svLla/xNcgmD3f6O0xf6mAKZDE=

"@webassemblyjs/helper-api-error@1.11.6":
  version "1.11.6"
  resolved "http://npm.htsc/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.11.6.tgz#6132f68c4acd59dcd141c44b18cbebbd9f2fa768"
  integrity sha1-YTL2jErNWdzRQcRLGMvrvZ8vp2g=

"@webassemblyjs/helper-buffer@1.11.6":
  version "1.11.6"
  resolved "http://npm.htsc/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.11.6.tgz#b66d73c43e296fd5e88006f18524feb0f2c7c093"
  integrity sha1-tm1zxD4pb9XogAbxhST+sPLHwJM=

"@webassemblyjs/helper-numbers@1.11.6":
  version "1.11.6"
  resolved "http://npm.htsc/@webassemblyjs/helper-numbers/download/@webassemblyjs/helper-numbers-1.11.6.tgz#cbce5e7e0c1bd32cf4905ae444ef64cea919f1b5"
  integrity sha1-y85efgwb0yz0kFrkRO9kzqkZ8bU=
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.11.6"
    "@webassemblyjs/helper-api-error" "1.11.6"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.11.6":
  version "1.11.6"
  resolved "http://npm.htsc/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.11.6.tgz#bb2ebdb3b83aa26d9baad4c46d4315283acd51e9"
  integrity sha1-uy69s7g6om2bqtTEbUMVKDrNUek=

"@webassemblyjs/helper-wasm-section@1.11.6":
  version "1.11.6"
  resolved "http://npm.htsc/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.11.6.tgz#ff97f3863c55ee7f580fd5c41a381e9def4aa577"
  integrity sha1-/5fzhjxV7n9YD9XEGjgene9KpXc=
  dependencies:
    "@webassemblyjs/ast" "1.11.6"
    "@webassemblyjs/helper-buffer" "1.11.6"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/wasm-gen" "1.11.6"

"@webassemblyjs/ieee754@1.11.6":
  version "1.11.6"
  resolved "http://npm.htsc/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.11.6.tgz#bb665c91d0b14fffceb0e38298c329af043c6e3a"
  integrity sha1-u2ZckdCxT//OsOOCmMMprwQ8bjo=
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.11.6":
  version "1.11.6"
  resolved "http://npm.htsc/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.11.6.tgz#70e60e5e82f9ac81118bc25381a0b283893240d7"
  integrity sha1-cOYOXoL5rIERi8JTgaCyg4kyQNc=
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.11.6":
  version "1.11.6"
  resolved "http://npm.htsc/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.11.6.tgz#90f8bc34c561595fe156603be7253cdbcd0fab5a"
  integrity sha1-kPi8NMVhWV/hVmA75yU8280Pq1o=

"@webassemblyjs/wasm-edit@^1.11.5":
  version "1.11.6"
  resolved "http://npm.htsc/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.11.6.tgz#c72fa8220524c9b416249f3d94c2958dfe70ceab"
  integrity sha1-xy+oIgUkybQWJJ89lMKVjf5wzqs=
  dependencies:
    "@webassemblyjs/ast" "1.11.6"
    "@webassemblyjs/helper-buffer" "1.11.6"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/helper-wasm-section" "1.11.6"
    "@webassemblyjs/wasm-gen" "1.11.6"
    "@webassemblyjs/wasm-opt" "1.11.6"
    "@webassemblyjs/wasm-parser" "1.11.6"
    "@webassemblyjs/wast-printer" "1.11.6"

"@webassemblyjs/wasm-gen@1.11.6":
  version "1.11.6"
  resolved "http://npm.htsc/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.11.6.tgz#fb5283e0e8b4551cc4e9c3c0d7184a65faf7c268"
  integrity sha1-+1KD4Oi0VRzE6cPA1xhKZfr3wmg=
  dependencies:
    "@webassemblyjs/ast" "1.11.6"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/ieee754" "1.11.6"
    "@webassemblyjs/leb128" "1.11.6"
    "@webassemblyjs/utf8" "1.11.6"

"@webassemblyjs/wasm-opt@1.11.6":
  version "1.11.6"
  resolved "http://npm.htsc/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.11.6.tgz#d9a22d651248422ca498b09aa3232a81041487c2"
  integrity sha1-2aItZRJIQiykmLCaoyMqgQQUh8I=
  dependencies:
    "@webassemblyjs/ast" "1.11.6"
    "@webassemblyjs/helper-buffer" "1.11.6"
    "@webassemblyjs/wasm-gen" "1.11.6"
    "@webassemblyjs/wasm-parser" "1.11.6"

"@webassemblyjs/wasm-parser@1.11.6", "@webassemblyjs/wasm-parser@^1.11.5":
  version "1.11.6"
  resolved "http://npm.htsc/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.11.6.tgz#bb85378c527df824004812bbdb784eea539174a1"
  integrity sha1-u4U3jFJ9+CQASBK723hO6lORdKE=
  dependencies:
    "@webassemblyjs/ast" "1.11.6"
    "@webassemblyjs/helper-api-error" "1.11.6"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/ieee754" "1.11.6"
    "@webassemblyjs/leb128" "1.11.6"
    "@webassemblyjs/utf8" "1.11.6"

"@webassemblyjs/wast-printer@1.11.6":
  version "1.11.6"
  resolved "http://npm.htsc/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.11.6.tgz#a7bf8dd7e362aeb1668ff43f35cb849f188eff20"
  integrity sha1-p7+N1+NirrFmj/Q/NcuEnxiO/yA=
  dependencies:
    "@webassemblyjs/ast" "1.11.6"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "http://npm.htsc/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz#eef014a3145ae477a1cbc00cd1e552336dceb790"
  integrity sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "http://npm.htsc/@xtuc/long/download/@xtuc/long-4.2.2.tgz#d291c6a4e97989b5c61d9acf396ae4fe133a718d"
  integrity sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=

abbrev@^1.0.0:
  version "1.1.1"
  resolved "http://npm.htsc/abbrev/download/abbrev-1.1.1.tgz#f8f2c887ad10bf67f634f005b6987fed3179aac8"
  integrity sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=

accepts@~1.3.4, accepts@~1.3.5, accepts@~1.3.8:
  version "1.3.8"
  resolved "http://npm.htsc/accepts/download/accepts-1.3.8.tgz#0bf0be125b67014adcb0b0921e62db7bffe16b2e"
  integrity sha1-C/C+EltnAUrcsLCSHmLbe//hay4=
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-import-assertions@^1.9.0:
  version "1.9.0"
  resolved "http://npm.htsc/acorn-import-assertions/download/acorn-import-assertions-1.9.0.tgz#507276249d684797c84e0734ef84860334cfb1ac"
  integrity sha1-UHJ2JJ1oR5fITgc074SGAzTPsaw=

acorn-jsx@^5.3.1, acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "http://npm.htsc/acorn-jsx/download/acorn-jsx-5.3.2.tgz#7ed5bb55908b3b2f1bc55c6af1653bada7f07937"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn-walk@^8.1.1:
  version "8.2.0"
  resolved "http://npm.htsc/acorn-walk/download/acorn-walk-8.2.0.tgz#741210f2e2426454508853a2f44d0ab83b7f69c1"
  integrity sha1-dBIQ8uJCZFRQiFOi9E0KuDt/acE=

acorn@^7.4.0:
  version "7.4.1"
  resolved "http://npm.htsc/acorn/download/acorn-7.4.1.tgz#feaed255973d2e77555b83dbc08851a6c63520fa"
  integrity sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=

acorn@^8.4.1, acorn@^8.7.1, acorn@^8.8.2:
  version "8.9.0"
  resolved "http://npm.htsc/acorn/download/acorn-8.9.0.tgz#78a16e3b2bcc198c10822786fa6679e245db5b59"
  integrity sha1-eKFuOyvMGYwQgieG+mZ54kXbW1k=

acorn@^8.9.0:
  version "8.10.0"
  resolved "http://npm.htsc/acorn/download/acorn-8.10.0.tgz#8be5b3907a67221a81ab23c7889c4c5526b62ec5"
  integrity sha1-i+WzkHpnIhqBqyPHiJxMVSa2LsU=

agent-base@6, agent-base@^6.0.2:
  version "6.0.2"
  resolved "http://npm.htsc/agent-base/download/agent-base-6.0.2.tgz#49fff58577cfee3f37176feab4c22e00f86d7f77"
  integrity sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=
  dependencies:
    debug "4"

agentkeepalive@^4.2.1:
  version "4.3.0"
  resolved "http://npm.htsc/agentkeepalive/download/agentkeepalive-4.3.0.tgz#bb999ff07412653c1803b3ced35e50729830a255"
  integrity sha1-u5mf8HQSZTwYA7PO015QcpgwolU=
  dependencies:
    debug "^4.1.0"
    depd "^2.0.0"
    humanize-ms "^1.2.1"

aggregate-error@^3.0.0:
  version "3.1.0"
  resolved "http://npm.htsc/aggregate-error/download/aggregate-error-3.1.0.tgz#92670ff50f5359bdb7a3e0d40d0ec30c5737687a"
  integrity sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ajv-formats@^2.1.1:
  version "2.1.1"
  resolved "http://npm.htsc/ajv-formats/download/ajv-formats-2.1.1.tgz#6e669400659eb74973bbf2e33327180a0996b520"
  integrity sha1-bmaUAGWet0lzu/LjMycYCgmWtSA=
  dependencies:
    ajv "^8.0.0"

ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "http://npm.htsc/ajv-keywords/download/ajv-keywords-3.5.2.tgz#31f29da5ab6e00d1c2d329acf7b5929614d5014d"
  integrity sha1-MfKdpatuANHC0yms97WSlhTVAU0=

ajv-keywords@^5.1.0:
  version "5.1.0"
  resolved "http://npm.htsc/ajv-keywords/download/ajv-keywords-5.1.0.tgz#69d4d385a4733cdbeab44964a1170a88f87f0e16"
  integrity sha1-adTThaRzPNvqtElkoRcKiPh/DhY=
  dependencies:
    fast-deep-equal "^3.1.3"

ajv@^6.10.0, ajv@^6.12.4, ajv@^6.12.5:
  version "6.12.6"
  resolved "http://npm.htsc/ajv/download/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.0, ajv@^8.0.1, ajv@^8.9.0:
  version "8.12.0"
  resolved "http://npm.htsc/ajv/download/ajv-8.12.0.tgz#d1a0527323e22f53562c567c00991577dfbe19d1"
  integrity sha1-0aBScyPiL1NWLFZ8AJkVd9++GdE=
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

ansi-colors@^4.1.1:
  version "4.1.3"
  resolved "http://npm.htsc/ansi-colors/download/ansi-colors-4.1.3.tgz#37611340eb2243e70cc604cad35d63270d48781b"
  integrity sha1-N2ETQOsiQ+cMxgTK011jJw1IeBs=

ansi-escapes@^4.3.0:
  version "4.3.2"
  resolved "http://npm.htsc/ansi-escapes/download/ansi-escapes-4.3.2.tgz#6b2291d1db7d98b6521d5f1efa42d0f3a9feb65e"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-html-community@^0.0.8:
  version "0.0.8"
  resolved "http://npm.htsc/ansi-html-community/download/ansi-html-community-0.0.8.tgz#69fbc4d6ccbe383f9736934ae34c3f8290f1bf41"
  integrity sha1-afvE1sy+OD+XNpNK40w/gpDxv0E=

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://npm.htsc/ansi-regex/download/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-regex@^6.0.1:
  version "6.0.1"
  resolved "http://npm.htsc/ansi-regex/download/ansi-regex-6.0.1.tgz#3183e38fae9a65d7cb5e53945cd5897d0260a06a"
  integrity sha1-MYPjj66aZdfLXlOUXNWJfQJgoGo=

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://npm.htsc/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "http://npm.htsc/ansi-styles/download/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^6.1.0:
  version "6.2.1"
  resolved "http://npm.htsc/ansi-styles/download/ansi-styles-6.2.1.tgz#0e62320cf99c21afff3b3012192546aacbfb05c5"
  integrity sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=

anymatch@~3.1.2:
  version "3.1.3"
  resolved "http://npm.htsc/anymatch/download/anymatch-3.1.3.tgz#790c58b19ba1720a84205b57c618d5ad8524973e"
  integrity sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

appdmg@^0.6.4:
  version "0.6.6"
  resolved "http://npm.htsc/appdmg/download/appdmg-0.6.6.tgz#d06bd82b530032fd7a8f0970a1c6ee6196e1efce"
  integrity sha1-0GvYK1MAMv16jwlwocbuYZbh784=
  dependencies:
    async "^1.4.2"
    ds-store "^0.1.5"
    execa "^1.0.0"
    fs-temp "^1.0.0"
    fs-xattr "^0.3.0"
    image-size "^0.7.4"
    is-my-json-valid "^2.20.0"
    minimist "^1.1.3"
    parse-color "^1.0.0"
    path-exists "^4.0.0"
    repeat-string "^1.5.4"

"aproba@^1.0.3 || ^2.0.0":
  version "2.0.0"
  resolved "http://npm.htsc/aproba/download/aproba-2.0.0.tgz#52520b8ae5b569215b354efc0caa3fe1e45a8adc"
  integrity sha1-UlILiuW1aSFbNU78DKo/4eRaitw=

are-we-there-yet@^3.0.0:
  version "3.0.1"
  resolved "http://npm.htsc/are-we-there-yet/download/are-we-there-yet-3.0.1.tgz#679df222b278c64f2cdba1175cdc00b0d96164bd"
  integrity sha1-Z53yIrJ4xk8s26EXXNwAsNlhZL0=
  dependencies:
    delegates "^1.0.0"
    readable-stream "^3.6.0"

arg@^4.1.0:
  version "4.1.3"
  resolved "http://npm.htsc/arg/download/arg-4.1.3.tgz#269fc7ad5b8e42cb63c896d5666017261c144089"
  integrity sha1-Jp/HrVuOQstjyJbVZmAXJhwUQIk=

argparse@^1.0.7:
  version "1.0.10"
  resolved "http://npm.htsc/argparse/download/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "http://npm.htsc/argparse/download/argparse-2.0.1.tgz#246f50f3ca78a3240f6c997e8a9bd1eac49e4b38"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

aria-query@^5.1.3:
  version "5.3.0"
  resolved "http://npm.htsc/aria-query/download/aria-query-5.3.0.tgz#650c569e41ad90b51b3d7df5e5eed1c7549c103e"
  integrity sha1-ZQxWnkGtkLUbPX315e7Rx1ScED4=
  dependencies:
    dequal "^2.0.3"

array-buffer-byte-length@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/array-buffer-byte-length/download/array-buffer-byte-length-1.0.0.tgz#fabe8bc193fea865f317fe7807085ee0dee5aead"
  integrity sha1-+r6LwZP+qGXzF/54Bwhe4N7lrq0=
  dependencies:
    call-bind "^1.0.2"
    is-array-buffer "^3.0.1"

array-find@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/array-find/download/array-find-1.0.0.tgz#6c8e286d11ed768327f8e62ecee87353ca3e78b8"
  integrity sha1-bI4obRHtdoMn+OYuzuhzU8o+eLg=

array-flatten@1.1.1:
  version "1.1.1"
  resolved "http://npm.htsc/array-flatten/download/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-flatten@^2.1.2:
  version "2.1.2"
  resolved "http://npm.htsc/array-flatten/download/array-flatten-2.1.2.tgz#24ef80a28c1a893617e2149b0c6d0d788293b099"
  integrity sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk=

array-includes@^3.1.6:
  version "3.1.6"
  resolved "http://npm.htsc/array-includes/download/array-includes-3.1.6.tgz#9e9e720e194f198266ba9e18c29e6a9b0e4b225f"
  integrity sha1-np5yDhlPGYJmup4Ywp5qmw5LIl8=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    get-intrinsic "^1.1.3"
    is-string "^1.0.7"

array-union@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/array-union/download/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array.prototype.flat@^1.3.1:
  version "1.3.1"
  resolved "http://npm.htsc/array.prototype.flat/download/array.prototype.flat-1.3.1.tgz#ffc6576a7ca3efc2f46a143b9d1dda9b4b3cf5e2"
  integrity sha1-/8ZXanyj78L0ahQ7nR3am0s89eI=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    es-shim-unscopables "^1.0.0"

array.prototype.flatmap@^1.3.1:
  version "1.3.1"
  resolved "http://npm.htsc/array.prototype.flatmap/download/array.prototype.flatmap-1.3.1.tgz#1aae7903c2100433cb8261cd4ed310aab5c4a183"
  integrity sha1-Gq55A8IQBDPLgmHNTtMQqrXEoYM=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    es-shim-unscopables "^1.0.0"

array.prototype.tosorted@^1.1.1:
  version "1.1.1"
  resolved "http://npm.htsc/array.prototype.tosorted/download/array.prototype.tosorted-1.1.1.tgz#ccf44738aa2b5ac56578ffda97c03fd3e23dd532"
  integrity sha1-zPRHOKorWsVleP/al8A/0+I91TI=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    es-shim-unscopables "^1.0.0"
    get-intrinsic "^1.1.3"

arrify@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/arrify/download/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"
  integrity sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=

asar@^3.0.0:
  version "3.2.0"
  resolved "http://npm.htsc/asar/download/asar-3.2.0.tgz#e6edb5edd6f627ebef04db62f771c61bea9c1221"
  integrity sha1-5u217db2J+vvBNti93HGG+qcEiE=
  dependencies:
    chromium-pickle-js "^0.2.0"
    commander "^5.0.0"
    glob "^7.1.6"
    minimatch "^3.0.4"
  optionalDependencies:
    "@types/glob" "^7.1.1"

ast-types-flow@^0.0.7:
  version "0.0.7"
  resolved "http://npm.htsc/ast-types-flow/download/ast-types-flow-0.0.7.tgz#f70b735c6bca1a5c9c22d982c3e39e7feba3bdad"
  integrity sha1-9wtzXGvKGlycItmCw+Oef+ujva0=

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/astral-regex/download/astral-regex-2.0.0.tgz#483143c567aeed4785759c0865786dc77d7d2e31"
  integrity sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=

async@^1.4.2:
  version "1.5.2"
  resolved "http://npm.htsc/async/download/async-1.5.2.tgz#ec6a61ae56480c0c3cb241c95618e20892f9672a"
  integrity sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://npm.htsc/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

at-least-node@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/at-least-node/download/at-least-node-1.0.0.tgz#602cd4b46e844ad4effc92a8011a3c46e0238dc2"
  integrity sha1-YCzUtG6EStTv/JKoARo8RuAjjcI=

author-regex@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/author-regex/download/author-regex-1.0.0.tgz#d08885be6b9bbf9439fe087c76287245f0a81450"
  integrity sha1-0IiFvmubv5Q5/gh8dihyRfCoFFA=

available-typed-arrays@^1.0.5:
  version "1.0.5"
  resolved "http://npm.htsc/available-typed-arrays/download/available-typed-arrays-1.0.5.tgz#92f95616501069d07d10edb2fc37d3e1c65123b7"
  integrity sha1-kvlWFlAQadB9EO2y/DfT4cZRI7c=

axe-core@^4.6.2:
  version "4.7.2"
  resolved "http://npm.htsc/axe-core/download/axe-core-4.7.2.tgz#040a7342b20765cb18bb50b628394c21bccc17a0"
  integrity sha1-BApzQrIHZcsYu1C2KDlMIbzMF6A=

axios@1.4.0:
  version "1.4.0"
  resolved "http://npm.htsc/axios/download/axios-1.4.0.tgz#38a7bf1224cd308de271146038b551d725f0be1f"
  integrity sha1-OKe/EiTNMI3icRRgOLVR1yXwvh8=
  dependencies:
    follow-redirects "^1.15.0"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

axios@^0.27.2:
  version "0.27.2"
  resolved "http://npm.htsc/axios/download/axios-0.27.2.tgz#207658cc8621606e586c85db4b41a750e756d972"
  integrity sha1-IHZYzIYhYG5YbIXbS0GnUOdW2XI=
  dependencies:
    follow-redirects "^1.14.9"
    form-data "^4.0.0"

axobject-query@^3.1.1:
  version "3.2.1"
  resolved "http://npm.htsc/axobject-query/download/axobject-query-3.2.1.tgz#39c378a6e3b06ca679f29138151e45b2b32da62a"
  integrity sha1-OcN4puOwbKZ58pE4FR5FsrMtpio=
  dependencies:
    dequal "^2.0.3"

babel-plugin-polyfill-corejs2@^0.4.6:
  version "0.4.6"
  resolved "http://npm.htsc/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.4.6.tgz#b2df0251d8e99f229a8e60fc4efa9a68b41c8313"
  integrity sha1-st8CUdjpnyKajmD8TvqaaLQcgxM=
  dependencies:
    "@babel/compat-data" "^7.22.6"
    "@babel/helper-define-polyfill-provider" "^0.4.3"
    semver "^6.3.1"

babel-plugin-polyfill-corejs3@^0.8.5:
  version "0.8.5"
  resolved "http://npm.htsc/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.8.5.tgz#a75fa1b0c3fc5bd6837f9ec465c0f48031b8cab1"
  integrity sha1-p1+hsMP8W9aDf57EZcD0gDG4yrE=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.4.3"
    core-js-compat "^3.32.2"

babel-plugin-polyfill-regenerator@^0.5.3:
  version "0.5.3"
  resolved "http://npm.htsc/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.5.3.tgz#d4c49e4b44614607c13fb769bcd85c72bb26a4a5"
  integrity sha1-1MSeS0RhRgfBP7dpvNhccrsmpKU=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.4.3"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "http://npm.htsc/balanced-match/download/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

balanced-match@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/balanced-match/download/balanced-match-2.0.0.tgz#dc70f920d78db8b858535795867bf48f820633d9"
  integrity sha1-3HD5INeNuLhYU1eVhnv0j4IGM9k=

"base32-encode@^0.1.0 || ^1.0.0":
  version "1.2.0"
  resolved "http://npm.htsc/base32-encode/download/base32-encode-1.2.0.tgz#e150573a5e431af0a998e32bdfde7045725ca453"
  integrity sha1-4VBXOl5DGvCpmOMr395wRXJcpFM=
  dependencies:
    to-data-view "^1.1.0"

base64-js@^1.3.1, base64-js@^1.5.1:
  version "1.5.1"
  resolved "http://npm.htsc/base64-js/download/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

batch@0.6.1:
  version "0.6.1"
  resolved "http://npm.htsc/batch/download/batch-0.6.1.tgz#dc34314f4e679318093fc760272525f94bf25c16"
  integrity sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=

big.js@^5.2.2:
  version "5.2.2"
  resolved "http://npm.htsc/big.js/download/big.js-5.2.2.tgz#65f0af382f578bcdc742bd9c281e9cb2d7768328"
  integrity sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "http://npm.htsc/binary-extensions/download/binary-extensions-2.2.0.tgz#75f502eeaf9ffde42fc98829645be4ea76bd9e2d"
  integrity sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=

bl@^4.1.0:
  version "4.1.0"
  resolved "http://npm.htsc/bl/download/bl-4.1.0.tgz#451535264182bec2fbbc83a62ab98cf11d9f7b3a"
  integrity sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

bluebird@^3.1.1:
  version "3.7.2"
  resolved "http://npm.htsc/bluebird/download/bluebird-3.7.2.tgz#9f229c15be272454ffa973ace0dbee79a1b0c36f"
  integrity sha1-nyKcFb4nJFT/qXOs4NvueaGww28=

body-parser@1.20.1:
  version "1.20.1"
  resolved "http://npm.htsc/body-parser/download/body-parser-1.20.1.tgz#b1812a8912c195cd371a3ee5e66faa2338a5c668"
  integrity sha1-sYEqiRLBlc03Gj7l5m+qIzilxmg=
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.11.0"
    raw-body "2.5.1"
    type-is "~1.6.18"
    unpipe "1.0.0"

bonjour-service@^1.0.11:
  version "1.1.1"
  resolved "http://npm.htsc/bonjour-service/download/bonjour-service-1.1.1.tgz#960948fa0e0153f5d26743ab15baf8e33752c135"
  integrity sha1-lglI+g4BU/XSZ0OrFbr44zdSwTU=
  dependencies:
    array-flatten "^2.1.2"
    dns-equal "^1.0.0"
    fast-deep-equal "^3.1.3"
    multicast-dns "^7.2.5"

boolbase@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/boolbase/download/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

boolean@^3.0.1:
  version "3.2.0"
  resolved "http://npm.htsc/boolean/download/boolean-3.2.0.tgz#9e5294af4e98314494cbb17979fa54ca159f116b"
  integrity sha1-nlKUr06YMUSUy7F5efpUyhWfEWs=

bplist-creator@~0.0.3:
  version "0.0.8"
  resolved "http://npm.htsc/bplist-creator/download/bplist-creator-0.0.8.tgz#56b2a6e79e9aec3fc33bf831d09347d73794e79c"
  integrity sha1-VrKm556a7D/DO/gx0JNH1zeU55w=
  dependencies:
    stream-buffers "~2.2.0"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://npm.htsc/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "http://npm.htsc/brace-expansion/download/brace-expansion-2.0.1.tgz#1edc459e0f0c548486ecf9fc99f2221364b9a0ae"
  integrity sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.2, braces@~3.0.2:
  version "3.0.2"
  resolved "http://npm.htsc/braces/download/braces-3.0.2.tgz#3454e1a462ee8d599e236df336cd9ea4f8afe107"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

browserslist@^4.14.5:
  version "4.21.9"
  resolved "http://npm.htsc/browserslist/download/browserslist-4.21.9.tgz#e11bdd3c313d7e2a9e87e8b4b0c7872b13897635"
  integrity sha1-4RvdPDE9fiqeh+i0sMeHKxOJdjU=
  dependencies:
    caniuse-lite "^1.0.30001503"
    electron-to-chromium "^1.4.431"
    node-releases "^2.0.12"
    update-browserslist-db "^1.0.11"

browserslist@^4.21.9, browserslist@^4.22.1:
  version "4.22.1"
  resolved "http://npm.htsc/browserslist/download/browserslist-4.22.1.tgz#ba91958d1a59b87dab6fed8dfbcb3da5e2e9c619"
  integrity sha1-upGVjRpZuH2rb+2N+8s9peLpxhk=
  dependencies:
    caniuse-lite "^1.0.30001541"
    electron-to-chromium "^1.4.535"
    node-releases "^2.0.13"
    update-browserslist-db "^1.0.13"

buffer-crc32@~0.2.3:
  version "0.2.13"
  resolved "http://npm.htsc/buffer-crc32/download/buffer-crc32-0.2.13.tgz#0d333e3f00eac50aa1454abd30ef8c2a5d9a7242"
  integrity sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=

buffer-equal@^1.0.0:
  version "1.0.1"
  resolved "http://npm.htsc/buffer-equal/download/buffer-equal-1.0.1.tgz#2f7651be5b1b3f057fcd6e7ee16cf34767077d90"
  integrity sha1-L3ZRvlsbPwV/zW5+4WzzR2cHfZA=

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "http://npm.htsc/buffer-from/download/buffer-from-1.1.2.tgz#2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

buffer@^5.5.0:
  version "5.7.1"
  resolved "http://npm.htsc/buffer/download/buffer-5.7.1.tgz#ba62e7c13133053582197160851a8f648e99eed0"
  integrity sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

builder-util-runtime@9.1.1:
  version "9.1.1"
  resolved "http://npm.htsc/builder-util-runtime/download/builder-util-runtime-9.1.1.tgz#2da7b34e78a64ad14ccd070d6eed4662d893bd60"
  integrity sha1-LaezTnimStFMzQcNbu1GYtiTvWA=
  dependencies:
    debug "^4.3.4"
    sax "^1.2.4"

bytes@3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/bytes/download/bytes-3.0.0.tgz#d32815404d689699f85a4ea4fa8755dd13a96048"
  integrity sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=

bytes@3.1.2:
  version "3.1.2"
  resolved "http://npm.htsc/bytes/download/bytes-3.1.2.tgz#8b0beeb98605adf1b128fa4386403c009e0221a5"
  integrity sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=

cacache@^17.0.0:
  version "17.1.3"
  resolved "http://npm.htsc/cacache/download/cacache-17.1.3.tgz#c6ac23bec56516a7c0c52020fd48b4909d7c7044"
  integrity sha1-xqwjvsVlFqfAxSAg/Ui0kJ18cEQ=
  dependencies:
    "@npmcli/fs" "^3.1.0"
    fs-minipass "^3.0.0"
    glob "^10.2.2"
    lru-cache "^7.7.1"
    minipass "^5.0.0"
    minipass-collect "^1.0.2"
    minipass-flush "^1.0.5"
    minipass-pipeline "^1.2.4"
    p-map "^4.0.0"
    ssri "^10.0.0"
    tar "^6.1.11"
    unique-filename "^3.0.0"

cacheable-lookup@^5.0.3:
  version "5.0.4"
  resolved "http://npm.htsc/cacheable-lookup/download/cacheable-lookup-5.0.4.tgz#5a6b865b2c44357be3d5ebc2a467b032719a7005"
  integrity sha1-WmuGWyxENXvj1evCpGewMnGacAU=

cacheable-request@^7.0.2:
  version "7.0.2"
  resolved "http://npm.htsc/cacheable-request/download/cacheable-request-7.0.2.tgz#ea0d0b889364a25854757301ca12b2da77f91d27"
  integrity sha1-6g0LiJNkolhUdXMByhKy2nf5HSc=
  dependencies:
    clone-response "^1.0.2"
    get-stream "^5.1.0"
    http-cache-semantics "^4.0.0"
    keyv "^4.0.0"
    lowercase-keys "^2.0.0"
    normalize-url "^6.0.1"
    responselike "^2.0.0"

call-bind@^1.0.0, call-bind@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/call-bind/download/call-bind-1.0.2.tgz#b1d4e89e688119c3c9a903ad30abb2f6a919be3c"
  integrity sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://npm.htsc/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camel-case@^4.1.2:
  version "4.1.2"
  resolved "http://npm.htsc/camel-case/download/camel-case-4.1.2.tgz#9728072a954f805228225a6deea6b38461e1bd5a"
  integrity sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=
  dependencies:
    pascal-case "^3.1.2"
    tslib "^2.0.3"

camelcase-keys@^6.2.2:
  version "6.2.2"
  resolved "http://npm.htsc/camelcase-keys/download/camelcase-keys-6.2.2.tgz#5e755d6ba51aa223ec7d3d52f25778210f9dc3c0"
  integrity sha1-XnVda6UaoiPsfT1S8ld4IQ+dw8A=
  dependencies:
    camelcase "^5.3.1"
    map-obj "^4.0.0"
    quick-lru "^4.0.1"

camelcase@^5.0.0, camelcase@^5.3.1:
  version "5.3.1"
  resolved "http://npm.htsc/camelcase/download/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

caniuse-lite@^1.0.30001332, caniuse-lite@^1.0.30001541:
  version "1.0.30001553"
  resolved "http://npm.htsc/caniuse-lite/download/caniuse-lite-1.0.30001553.tgz#e64e7dc8fd4885cd246bb476471420beb5e474b5"
  integrity sha1-5k59yP1Ihc0ka7R2RxQgvrXkdLU=

caniuse-lite@^1.0.30001503:
  version "1.0.30001507"
  resolved "http://npm.htsc/caniuse-lite/download/caniuse-lite-1.0.30001507.tgz#fae53f6286e7564783eadea9b447819410a59534"
  integrity sha1-+uU/YobnVkeD6t6ptEeBlBCllTQ=

chalk@^2.0.0, chalk@^2.4.2:
  version "2.4.2"
  resolved "http://npm.htsc/chalk/download/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0, chalk@^4.1.0, chalk@^4.1.2:
  version "4.1.2"
  resolved "http://npm.htsc/chalk/download/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

character-entities-legacy@^1.0.0:
  version "1.1.4"
  resolved "http://npm.htsc/character-entities-legacy/download/character-entities-legacy-1.1.4.tgz#94bc1845dce70a5bb9d2ecc748725661293d8fc1"
  integrity sha1-lLwYRdznClu50uzHSHJWYSk9j8E=

character-entities@^1.0.0:
  version "1.2.4"
  resolved "http://npm.htsc/character-entities/download/character-entities-1.2.4.tgz#e12c3939b7eaf4e5b15e7ad4c5e28e1d48c5b16b"
  integrity sha1-4Sw5Obfq9OWxXnrUxeKOHUjFsWs=

character-reference-invalid@^1.0.0:
  version "1.1.4"
  resolved "http://npm.htsc/character-reference-invalid/download/character-reference-invalid-1.1.4.tgz#083329cda0eae272ab3dbbf37e9a382c13af1560"
  integrity sha1-CDMpzaDq4nKrPbvzfpo4LBOvFWA=

chokidar@^3.5.3:
  version "3.5.3"
  resolved "http://npm.htsc/chokidar/download/chokidar-3.5.3.tgz#1cf37c8707b932bd1af1ae22c0432e2acd1903bd"
  integrity sha1-HPN8hwe5Mr0a8a4iwEMuKs0ZA70=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chownr@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/chownr/download/chownr-2.0.0.tgz#15bfbe53d2eab4cf70f18a8cd68ebe5b3cb1dece"
  integrity sha1-Fb++U9LqtM9w8YqM1o6+Wzyx3s4=

chrome-trace-event@^1.0.2:
  version "1.0.3"
  resolved "http://npm.htsc/chrome-trace-event/download/chrome-trace-event-1.0.3.tgz#1015eced4741e15d06664a957dbbf50d041e26ac"
  integrity sha1-EBXs7UdB4V0GZkqVfbv1DQQeJqw=

chromium-pickle-js@^0.2.0:
  version "0.2.0"
  resolved "http://npm.htsc/chromium-pickle-js/download/chromium-pickle-js-0.2.0.tgz#04a106672c18b085ab774d983dfa3ea138f22205"
  integrity sha1-BKEGZywYsIWrd02YPfo+oTjyIgU=

ci-info@^1.5.0:
  version "1.6.0"
  resolved "http://npm.htsc/ci-info/download/ci-info-1.6.0.tgz#2ca20dbb9ceb32d4524a683303313f0304b1e497"
  integrity sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc=

clean-css@^5.2.2:
  version "5.3.2"
  resolved "http://npm.htsc/clean-css/download/clean-css-5.3.2.tgz#70ecc7d4d4114921f5d298349ff86a31a9975224"
  integrity sha1-cOzH1NQRSSH10pg0n/hqMamXUiQ=
  dependencies:
    source-map "~0.6.0"

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "http://npm.htsc/clean-stack/download/clean-stack-2.2.0.tgz#ee8472dbb129e727b31e8a10a427dee9dfe4008b"
  integrity sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://npm.htsc/cli-cursor/download/cli-cursor-3.1.0.tgz#264305a7ae490d1d03bf0c9ba7c925d1753af307"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-spinners@^2.5.0:
  version "2.9.0"
  resolved "http://npm.htsc/cli-spinners/download/cli-spinners-2.9.0.tgz#5881d0ad96381e117bbe07ad91f2008fe6ffd8db"
  integrity sha1-WIHQrZY4HhF7vgetkfIAj+b/2Ns=

cli-truncate@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/cli-truncate/download/cli-truncate-2.1.0.tgz#c39e28bf05edcde5be3b98992a22deed5a2b93c7"
  integrity sha1-w54ovwXtzeW+O5iZKiLe7Vork8c=
  dependencies:
    slice-ansi "^3.0.0"
    string-width "^4.2.0"

cliui@^6.0.0:
  version "6.0.0"
  resolved "http://npm.htsc/cliui/download/cliui-6.0.0.tgz#511d702c0c4e41ca156d7d0e96021f23e13225b1"
  integrity sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

cliui@^7.0.2:
  version "7.0.4"
  resolved "http://npm.htsc/cliui/download/cliui-7.0.4.tgz#a0265ee655476fc807aea9df3df8df7783808b4f"
  integrity sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

cliui@^8.0.1:
  version "8.0.1"
  resolved "http://npm.htsc/cliui/download/cliui-8.0.1.tgz#0c04b075db02cbfe60dc8e6cf2f5486b1a3608aa"
  integrity sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

clone-deep@^4.0.1:
  version "4.0.1"
  resolved "http://npm.htsc/clone-deep/download/clone-deep-4.0.1.tgz#c19fd9bdbbf85942b4fd979c84dcf7d5f07c2387"
  integrity sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clone-response@^1.0.2:
  version "1.0.3"
  resolved "http://npm.htsc/clone-response/download/clone-response-1.0.3.tgz#af2032aa47816399cf5f0a1d0db902f517abb8c3"
  integrity sha1-ryAyqkeBY5nPXwodDbkC9ReruMM=
  dependencies:
    mimic-response "^1.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "http://npm.htsc/clone/download/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

color-convert@^1.9.0:
  version "1.9.3"
  resolved "http://npm.htsc/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://npm.htsc/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-convert@~0.5.0:
  version "0.5.3"
  resolved "http://npm.htsc/color-convert/download/color-convert-0.5.3.tgz#bdb6c69ce660fadffe0b0007cc447e1b9f7282bd"
  integrity sha1-vbbGnOZg+t/+CwAHzER+G59ygr0=

color-name@1.1.3:
  version "1.1.3"
  resolved "http://npm.htsc/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "http://npm.htsc/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

color-support@^1.1.3:
  version "1.1.3"
  resolved "http://npm.htsc/color-support/download/color-support-1.1.3.tgz#93834379a1cc9a0c61f82f52f0d04322251bd5a2"
  integrity sha1-k4NDeaHMmgxh+C9S8NBDIiUb1aI=

colord@^2.9.3:
  version "2.9.3"
  resolved "http://npm.htsc/colord/download/colord-2.9.3.tgz#4f8ce919de456f1d5c1c368c307fe20f3e59fb43"
  integrity sha1-T4zpGd5Fbx1cHDaMMH/iDz5Z+0M=

colorette@^2.0.10, colorette@^2.0.16, colorette@^2.0.19:
  version "2.0.20"
  resolved "http://npm.htsc/colorette/download/colorette-2.0.20.tgz#9eb793e6833067f7235902fcd3b09917a000a95a"
  integrity sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "http://npm.htsc/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.20.0:
  version "2.20.3"
  resolved "http://npm.htsc/commander/download/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@^4.1.1:
  version "4.1.1"
  resolved "http://npm.htsc/commander/download/commander-4.1.1.tgz#9fd602bd936294e9e9ef46a3f4d6964044b18068"
  integrity sha1-n9YCvZNilOnp70aj9NaWQESxgGg=

commander@^5.0.0:
  version "5.1.0"
  resolved "http://npm.htsc/commander/download/commander-5.1.0.tgz#46abbd1652f8e059bddaef99bbdcb2ad9cf179ae"
  integrity sha1-Rqu9FlL44Fm92u+Zu9yyrZzxea4=

commander@^6.2.0:
  version "6.2.1"
  resolved "http://npm.htsc/commander/download/commander-6.2.1.tgz#0792eb682dfbc325999bb2b84fddddba110ac73c"
  integrity sha1-B5LraC37wyWZm7K4T93duhEKxzw=

commander@^8.3.0:
  version "8.3.0"
  resolved "http://npm.htsc/commander/download/commander-8.3.0.tgz#4837ea1b2da67b9c616a67afbb0fafee567bca66"
  integrity sha1-SDfqGy2me5xhamevuw+v7lZ7ymY=

commander@^9.4.0:
  version "9.5.0"
  resolved "http://registry.npm.htsc/commander/-/commander-9.5.0.tgz#bc08d1eb5cedf7ccb797a96199d41c7bc3e60d30"
  integrity sha512-KRs7WVDKg86PWiuAqhDrAQnTXZKraVcCc6vFdL14qrZ/DcWwuRo7VoiYXalXO7S5GKpqYiVEwCbgFDfxNHKJBQ==

compare-version@^0.1.2:
  version "0.1.2"
  resolved "http://npm.htsc/compare-version/download/compare-version-0.1.2.tgz#0162ec2d9351f5ddd59a9202cba935366a725080"
  integrity sha1-AWLsLZNR9d3VmpICy6k1NmpyUIA=

compressible@~2.0.16:
  version "2.0.18"
  resolved "http://npm.htsc/compressible/download/compressible-2.0.18.tgz#af53cca6b070d4c3c0750fbd77286a6d7cc46fba"
  integrity sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.4:
  version "1.7.4"
  resolved "http://npm.htsc/compression/download/compression-1.7.4.tgz#95523eff170ca57c29a0ca41e6fe131f41e5bb8f"
  integrity sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://npm.htsc/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concurrently@^8.0.1:
  version "8.2.0"
  resolved "http://npm.htsc/concurrently/download/concurrently-8.2.0.tgz#cdc9f621a4d913366600355d68254df2c5e782f3"
  integrity sha1-zcn2IaTZEzZmADVdaCVN8sXngvM=
  dependencies:
    chalk "^4.1.2"
    date-fns "^2.30.0"
    lodash "^4.17.21"
    rxjs "^7.8.1"
    shell-quote "^1.8.1"
    spawn-command "0.0.2"
    supports-color "^8.1.1"
    tree-kill "^1.2.2"
    yargs "^17.7.2"

confusing-browser-globals@^1.0.10:
  version "1.0.11"
  resolved "http://npm.htsc/confusing-browser-globals/download/confusing-browser-globals-1.0.11.tgz#ae40e9b57cdd3915408a2805ebd3a5585608dc81"
  integrity sha1-rkDptXzdORVAiigF69OlWFYI3IE=

connect-history-api-fallback@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/connect-history-api-fallback/download/connect-history-api-fallback-2.0.0.tgz#647264845251a0daf25b97ce87834cace0f5f1c8"
  integrity sha1-ZHJkhFJRoNryW5fOh4NMrOD18cg=

console-control-strings@^1.1.0:
  version "1.1.0"
  resolved "http://npm.htsc/console-control-strings/download/console-control-strings-1.1.0.tgz#3d7cf4464db6446ea644bf4b39507f9851008e8e"
  integrity sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=

content-disposition@0.5.4:
  version "0.5.4"
  resolved "http://npm.htsc/content-disposition/download/content-disposition-0.5.4.tgz#8b82b4efac82512a02bb0b1dcec9d2c5e8eb5bfe"
  integrity sha1-i4K076yCUSoCuwsdzsnSxejrW/4=
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4:
  version "1.0.5"
  resolved "http://npm.htsc/content-type/download/content-type-1.0.5.tgz#8b773162656d1d1086784c8f23a54ce6d73d7918"
  integrity sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/convert-source-map/download/convert-source-map-2.0.0.tgz#4b560f649fc4e918dd0ab75cf4961e8bc882d82a"
  integrity sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "http://npm.htsc/cookie-signature/download/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.5.0:
  version "0.5.0"
  resolved "http://npm.htsc/cookie/download/cookie-0.5.0.tgz#d1f5d71adec6558c58f389987c366aa47e994f8b"
  integrity sha1-0fXXGt7GVYxY84mYfDZqpH6ZT4s=

core-js-compat@^3.31.0, core-js-compat@^3.32.2:
  version "3.33.0"
  resolved "http://npm.htsc/core-js-compat/download/core-js-compat-3.33.0.tgz#24aa230b228406450b2277b7c8bfebae932df966"
  integrity sha1-JKojCyKEBkULIne3yL/rrpMt+WY=
  dependencies:
    browserslist "^4.22.1"

core-js@^3.25.0:
  version "3.33.1"
  resolved "http://npm.htsc/core-js/download/core-js-3.33.1.tgz#ef3766cfa382482d0a2c2bc5cb52c6d88805da52"
  integrity sha1-7zdmz6OCSC0KLCvFy1LG2IgF2lI=

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "http://npm.htsc/core-util-is/download/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cosmiconfig@^7.0.0, cosmiconfig@^7.0.1, cosmiconfig@^7.1.0:
  version "7.1.0"
  resolved "http://npm.htsc/cosmiconfig/download/cosmiconfig-7.1.0.tgz#1443b9afa596b670082ea46cbd8f6a62b84635f6"
  integrity sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

create-require@^1.1.0:
  version "1.1.1"
  resolved "http://npm.htsc/create-require/download/create-require-1.1.1.tgz#c1d7e8f1e5f6cfc9ff65f9cd352d37348756c333"
  integrity sha1-wdfo8eX2z8n/ZfnNNS03NIdWwzM=

cross-dirname@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.htsc/cross-dirname/-/cross-dirname-0.1.0.tgz#b899599f30a5389f59e78c150e19f957ad16a37c"
  integrity sha512-+R08/oI0nl3vfPcqftZRpytksBXDzOUveBq/NBVx0sUp1axwzPQrKinNx5yd5sxPu8j1wIy8AfnVQ+5eFdha6Q==

cross-env@^7.0.3:
  version "7.0.3"
  resolved "http://npm.htsc/cross-env/download/cross-env-7.0.3.tgz#865264b29677dc015ba8418918965dd232fc54cf"
  integrity sha1-hlJkspZ33AFbqEGJGJZd0jL8VM8=
  dependencies:
    cross-spawn "^7.0.1"

cross-spawn-windows-exe@^1.1.0, cross-spawn-windows-exe@^1.2.0:
  version "1.2.0"
  resolved "http://npm.htsc/cross-spawn-windows-exe/download/cross-spawn-windows-exe-1.2.0.tgz#46253b0f497676e766faf4a7061004618b5ac5ec"
  integrity sha1-RiU7D0l2dudm+vSnBhAEYYtaxew=
  dependencies:
    "@malept/cross-spawn-promise" "^1.1.0"
    is-wsl "^2.2.0"
    which "^2.0.2"

cross-spawn@^5.0.1:
  version "5.1.0"
  resolved "http://npm.htsc/cross-spawn/download/cross-spawn-5.1.0.tgz#e8bd0efee58fcff6f8f94510a0a554bbfa235449"
  integrity sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^6.0.0, cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "http://npm.htsc/cross-spawn/download/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.0, cross-spawn@^7.0.1, cross-spawn@^7.0.2, cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "http://npm.htsc/cross-spawn/download/cross-spawn-7.0.3.tgz#f73a85b9d5d41d045551c177e2882d4ac85728a6"
  integrity sha1-9zqFudXUHQRVUcF34ogtSshXKKY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

cross-zip@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/cross-zip/download/cross-zip-4.0.0.tgz#c29bfb2c001659a6d480ae9596f3bee83b48a230"
  integrity sha1-wpv7LAAWWabUgK6VlvO+6DtIojA=

crypto-js@^4.1.1:
  version "4.1.1"
  resolved "http://npm.htsc/crypto-js/download/crypto-js-4.1.1.tgz#9e485bcf03521041bd85844786b83fb7619736cf"
  integrity sha1-nkhbzwNSEEG9hYRHhrg/t2GXNs8=

css-functions-list@^3.1.0:
  version "3.1.0"
  resolved "http://npm.htsc/css-functions-list/download/css-functions-list-3.1.0.tgz#cf5b09f835ad91a00e5959bcfc627cd498e1321b"
  integrity sha1-z1sJ+DWtkaAOWVm8/GJ81JjhMhs=

css-loader@^6.0.0:
  version "6.8.1"
  resolved "http://npm.htsc/css-loader/download/css-loader-6.8.1.tgz#0f8f52699f60f5e679eab4ec0fcd68b8e8a50a88"
  integrity sha1-D49SaZ9g9eZ56rTsD81ouOilCog=
  dependencies:
    icss-utils "^5.1.0"
    postcss "^8.4.21"
    postcss-modules-extract-imports "^3.0.0"
    postcss-modules-local-by-default "^4.0.3"
    postcss-modules-scope "^3.0.0"
    postcss-modules-values "^4.0.0"
    postcss-value-parser "^4.2.0"
    semver "^7.3.8"

css-select@^4.1.3:
  version "4.3.0"
  resolved "http://npm.htsc/css-select/download/css-select-4.3.0.tgz#db7129b2846662fd8628cfc496abb2b59e41529b"
  integrity sha1-23EpsoRmYv2GKM/ElquytZ5BUps=
  dependencies:
    boolbase "^1.0.0"
    css-what "^6.0.1"
    domhandler "^4.3.1"
    domutils "^2.8.0"
    nth-check "^2.0.1"

css-what@^6.0.1:
  version "6.1.0"
  resolved "http://npm.htsc/css-what/download/css-what-6.1.0.tgz#fb5effcf76f1ddea2c81bdfaa4de44e79bac70f4"
  integrity sha1-+17/z3bx3eosgb36pN5E55uscPQ=

cssesc@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/cssesc/download/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

csstype@^3.0.2:
  version "3.1.2"
  resolved "http://npm.htsc/csstype/download/csstype-3.1.2.tgz#1d4bf9d572f11c14031f0436e1c10bc1f571f50b"
  integrity sha1-HUv51XLxHBQDHwQ24cELwfVx9Qs=

damerau-levenshtein@^1.0.8:
  version "1.0.8"
  resolved "http://npm.htsc/damerau-levenshtein/download/damerau-levenshtein-1.0.8.tgz#b43d286ccbd36bc5b2f7ed41caf2d0aba1f8a6e7"
  integrity sha1-tD0obMvTa8Wy9+1ByvLQq6H4puc=

date-fns@^2.30.0:
  version "2.30.0"
  resolved "http://npm.htsc/date-fns/download/date-fns-2.30.0.tgz#f367e644839ff57894ec6ac480de40cae4b0f4d0"
  integrity sha1-82fmRIOf9XiU7GrEgN5AyuSw9NA=
  dependencies:
    "@babel/runtime" "^7.21.0"

debug@2.6.9, debug@^2.2.0:
  version "2.6.9"
  resolved "http://npm.htsc/debug/download/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@4, debug@^4.0.0, debug@^4.0.1, debug@^4.1.0, debug@^4.1.1, debug@^4.2.0, debug@^4.3.1, debug@^4.3.2, debug@^4.3.3, debug@^4.3.4:
  version "4.3.4"
  resolved "http://npm.htsc/debug/download/debug-4.3.4.tgz#1319f6579357f2338d3337d2cdd4914bb5dcc865"
  integrity sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=
  dependencies:
    ms "2.1.2"

debug@^3.1.0, debug@^3.2.7:
  version "3.2.7"
  resolved "http://npm.htsc/debug/download/debug-3.2.7.tgz#72580b7e9145fb39b6676f9c5e5fb100b934179a"
  integrity sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=
  dependencies:
    ms "^2.1.1"

decamelize-keys@^1.1.0:
  version "1.1.1"
  resolved "http://npm.htsc/decamelize-keys/download/decamelize-keys-1.1.1.tgz#04a2d523b2f18d80d0158a43b895d56dff8d19d8"
  integrity sha1-BKLVI7LxjYDQFYpDuJXVbf+NGdg=
  dependencies:
    decamelize "^1.1.0"
    map-obj "^1.0.0"

decamelize@^1.1.0, decamelize@^1.2.0:
  version "1.2.0"
  resolved "http://npm.htsc/decamelize/download/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decompress-response@^6.0.0:
  version "6.0.0"
  resolved "http://npm.htsc/decompress-response/download/decompress-response-6.0.0.tgz#ca387612ddb7e104bd16d85aab00d5ecf09c66fc"
  integrity sha1-yjh2Et234QS9FthaqwDV7PCcZvw=
  dependencies:
    mimic-response "^3.1.0"

dedent@^0.7.0:
  version "0.7.0"
  resolved "http://npm.htsc/dedent/download/dedent-0.7.0.tgz#2495ddbaf6eb874abb0e1be9df22d2e5a544326c"
  integrity sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=

deep-is@^0.1.3:
  version "0.1.4"
  resolved "http://npm.htsc/deep-is/download/deep-is-0.1.4.tgz#a6f2dce612fadd2ef1f519b73551f17e85199831"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

deepmerge@^4.2.2:
  version "4.3.1"
  resolved "http://npm.htsc/deepmerge/download/deepmerge-4.3.1.tgz#44b5f2147cd3b00d4b56137685966f26fd25dd4a"
  integrity sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo=

default-gateway@^6.0.3:
  version "6.0.3"
  resolved "http://npm.htsc/default-gateway/download/default-gateway-6.0.3.tgz#819494c888053bdb743edbf343d6cdf7f2943a71"
  integrity sha1-gZSUyIgFO9t0PtvzQ9bN9/KUOnE=
  dependencies:
    execa "^5.0.0"

defaults@^1.0.3:
  version "1.0.4"
  resolved "http://npm.htsc/defaults/download/defaults-1.0.4.tgz#b0b02062c1e2aa62ff5d9528f0f98baa90978d7a"
  integrity sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=
  dependencies:
    clone "^1.0.2"

defer-to-connect@^2.0.0:
  version "2.0.1"
  resolved "http://npm.htsc/defer-to-connect/download/defer-to-connect-2.0.1.tgz#8016bdb4143e4632b77a3449c6236277de520587"
  integrity sha1-gBa9tBQ+RjK3ejRJxiNid95SBYc=

define-lazy-prop@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/define-lazy-prop/download/define-lazy-prop-2.0.0.tgz#3f7ae421129bcaaac9bc74905c98a0009ec9ee7f"
  integrity sha1-P3rkIRKbyqrJvHSQXJigAJ7J7n8=

define-properties@^1.1.3, define-properties@^1.1.4, define-properties@^1.2.0:
  version "1.2.0"
  resolved "http://npm.htsc/define-properties/download/define-properties-1.2.0.tgz#52988570670c9eacedd8064f4a990f2405849bd5"
  integrity sha1-UpiFcGcMnqzt2AZPSpkPJAWEm9U=
  dependencies:
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

delegates@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/delegates/download/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"
  integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=

depd@2.0.0, depd@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/depd/download/depd-2.0.0.tgz#b696163cc757560d09cf22cc8fad1571b79e76df"
  integrity sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=

depd@~1.1.2:
  version "1.1.2"
  resolved "http://npm.htsc/depd/download/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

dequal@^2.0.3:
  version "2.0.3"
  resolved "http://npm.htsc/dequal/download/dequal-2.0.3.tgz#2644214f1997d39ed0ee0ece72335490a7ac67be"
  integrity sha1-JkQhTxmX057Q7g7OcjNUkKesZ74=

destroy@1.2.0:
  version "1.2.0"
  resolved "http://npm.htsc/destroy/download/destroy-1.2.0.tgz#4803735509ad8be552934c67df614f94e66fa015"
  integrity sha1-SANzVQmti+VSk0xn32FPlOZvoBU=

detect-libc@^2.0.1:
  version "2.0.1"
  resolved "http://npm.htsc/detect-libc/download/detect-libc-2.0.1.tgz#e1897aa88fa6ad197862937fbc0441ef352ee0cd"
  integrity sha1-4Yl6qI+mrRl4YpN/vARB7zUu4M0=

detect-node@^2.0.4:
  version "2.1.0"
  resolved "http://npm.htsc/detect-node/download/detect-node-2.1.0.tgz#c9c70775a49c3d03bc2c06d9a73be550f978f8b1"
  integrity sha1-yccHdaScPQO8LAbZpzvlUPl4+LE=

diff@^4.0.1:
  version "4.0.2"
  resolved "http://npm.htsc/diff/download/diff-4.0.2.tgz#60f3aecb89d5fae520c11aa19efc2bb982aade7d"
  integrity sha1-YPOuy4nV+uUgwRqhnvwruYKq3n0=

dir-compare@^3.0.0:
  version "3.3.0"
  resolved "http://npm.htsc/dir-compare/download/dir-compare-3.3.0.tgz#2c749f973b5c4b5d087f11edaae730db31788416"
  integrity sha1-LHSflztcS10IfxHtqucw2zF4hBY=
  dependencies:
    buffer-equal "^1.0.0"
    minimatch "^3.0.4"

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "http://npm.htsc/dir-glob/download/dir-glob-3.0.1.tgz#56dbf73d992a4a93ba1584f4534063fd2e41717f"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

dns-equal@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/dns-equal/download/dns-equal-1.0.0.tgz#b39e7f1da6eb0a75ba9c17324b34753c47e0654d"
  integrity sha1-s55/HabrCnW6nBcySzR1PEfgZU0=

dns-packet@^5.2.2:
  version "5.6.0"
  resolved "http://npm.htsc/dns-packet/download/dns-packet-5.6.0.tgz#2202c947845c7a63c23ece58f2f70ff6ab4c2f7d"
  integrity sha1-IgLJR4RcemPCPs5Y8vcP9qtML30=
  dependencies:
    "@leichtgewicht/ip-codec" "^2.0.1"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/doctrine/download/doctrine-2.1.0.tgz#5cd01fc101621b42c4cd7f5d1a66243716d3f39d"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/doctrine/download/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-converter@^0.2.0:
  version "0.2.0"
  resolved "http://npm.htsc/dom-converter/download/dom-converter-0.2.0.tgz#6721a9daee2e293682955b6afe416771627bb768"
  integrity sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=
  dependencies:
    utila "~0.4"

dom-serializer@^1.0.1:
  version "1.4.1"
  resolved "http://npm.htsc/dom-serializer/download/dom-serializer-1.4.1.tgz#de5d41b1aea290215dc45a6dae8adcf1d32e2d30"
  integrity sha1-3l1Bsa6ikCFdxFptrorc8dMuLTA=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.2.0"
    entities "^2.0.0"

dom-serializer@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/dom-serializer/download/dom-serializer-2.0.0.tgz#e41b802e1eedf9f6cae183ce5e622d789d7d8e53"
  integrity sha1-5BuALh7t+fbK4YPOXmIteJ19jlM=
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.2"
    entities "^4.2.0"

domelementtype@^2.0.1, domelementtype@^2.2.0, domelementtype@^2.3.0:
  version "2.3.0"
  resolved "http://npm.htsc/domelementtype/download/domelementtype-2.3.0.tgz#5c45e8e869952626331d7aab326d01daf65d589d"
  integrity sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=

domhandler@^4.0.0, domhandler@^4.2.0, domhandler@^4.3.1:
  version "4.3.1"
  resolved "http://npm.htsc/domhandler/download/domhandler-4.3.1.tgz#8d792033416f59d68bc03a5aa7b018c1ca89279c"
  integrity sha1-jXkgM0FvWdaLwDpap7AYwcqJJ5w=
  dependencies:
    domelementtype "^2.2.0"

domhandler@^5.0.2, domhandler@^5.0.3:
  version "5.0.3"
  resolved "http://npm.htsc/domhandler/download/domhandler-5.0.3.tgz#cc385f7f751f1d1fc650c21374804254538c7d31"
  integrity sha1-zDhff3UfHR/GUMITdIBCVFOMfTE=
  dependencies:
    domelementtype "^2.3.0"

domutils@^2.5.2, domutils@^2.8.0:
  version "2.8.0"
  resolved "http://npm.htsc/domutils/download/domutils-2.8.0.tgz#4437def5db6e2d1f5d6ee859bd95ca7d02048135"
  integrity sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=
  dependencies:
    dom-serializer "^1.0.1"
    domelementtype "^2.2.0"
    domhandler "^4.2.0"

domutils@^3.0.1:
  version "3.1.0"
  resolved "http://npm.htsc/domutils/download/domutils-3.1.0.tgz#c47f551278d3dc4b0b1ab8cbb42d751a6f0d824e"
  integrity sha1-xH9VEnjT3EsLGrjLtC11Gm8Ngk4=
  dependencies:
    dom-serializer "^2.0.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.3"

dot-case@^3.0.4:
  version "3.0.4"
  resolved "http://npm.htsc/dot-case/download/dot-case-3.0.4.tgz#9b2b670d00a431667a8a75ba29cd1b98809ce751"
  integrity sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

ds-store@^0.1.5:
  version "0.1.6"
  resolved "http://npm.htsc/ds-store/download/ds-store-0.1.6.tgz#d1024ef746ed0c13f0f7fec85c7e858e8c4b7ca7"
  integrity sha1-0QJO90btDBPw9/7IXH6FjoxLfKc=
  dependencies:
    bplist-creator "~0.0.3"
    macos-alias "~0.2.5"
    tn1150 "^0.1.0"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "http://npm.htsc/eastasianwidth/download/eastasianwidth-0.2.0.tgz#696ce2ec0aa0e6ea93a397ffcf24aa7840c827cb"
  integrity sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=

ee-first@1.1.1:
  version "1.1.1"
  resolved "http://npm.htsc/ee-first/download/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

electron-installer-common@^0.10.2:
  version "0.10.3"
  resolved "http://npm.htsc/electron-installer-common/download/electron-installer-common-0.10.3.tgz#40f9db644ca60eb28673d545b67ee0113aef4444"
  integrity sha1-QPnbZEymDrKGc9VFtn7gETrvREQ=
  dependencies:
    "@malept/cross-spawn-promise" "^1.0.0"
    asar "^3.0.0"
    debug "^4.1.1"
    fs-extra "^9.0.0"
    glob "^7.1.4"
    lodash "^4.17.15"
    parse-author "^2.0.0"
    semver "^7.1.1"
    tmp-promise "^3.0.2"
  optionalDependencies:
    "@types/fs-extra" "^9.0.1"

electron-installer-debian@^3.0.0:
  version "3.1.0"
  resolved "http://npm.htsc/electron-installer-debian/download/electron-installer-debian-3.1.0.tgz#2f6107f559f6564c44e3832fb2942dd8de4d40cc"
  integrity sha1-L2EH9Vn2VkxE44MvspQt2N5NQMw=
  dependencies:
    "@malept/cross-spawn-promise" "^1.0.0"
    debug "^4.1.1"
    electron-installer-common "^0.10.2"
    fs-extra "^9.0.0"
    get-folder-size "^2.0.1"
    lodash "^4.17.4"
    word-wrap "^1.2.3"
    yargs "^15.0.1"

electron-installer-dmg@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/electron-installer-dmg/download/electron-installer-dmg-4.0.0.tgz#0520bcc8a928e559b3f16fc5cbc12b182a66ea1c"
  integrity sha1-BSC8yKko5Vmz8W/Fy8ErGCpm6hw=
  dependencies:
    debug "^4.3.2"
    minimist "^1.1.1"
  optionalDependencies:
    appdmg "^0.6.4"

electron-installer-redhat@^3.2.0:
  version "3.4.0"
  resolved "http://npm.htsc/electron-installer-redhat/download/electron-installer-redhat-3.4.0.tgz#4a7f8d67b48b7d5b23bd1eb074f4b684ae43b192"
  integrity sha1-Sn+NZ7SLfVsjvR6wdPS2hK5DsZI=
  dependencies:
    "@malept/cross-spawn-promise" "^1.0.0"
    debug "^4.1.1"
    electron-installer-common "^0.10.2"
    fs-extra "^9.0.0"
    lodash "^4.17.15"
    word-wrap "^1.2.3"
    yargs "^16.0.2"

electron-log@^4.4.8:
  version "4.4.8"
  resolved "http://npm.htsc/electron-log/download/electron-log-4.4.8.tgz#fcb9f714dbcaefb6ac7984c4683912c74730248a"
  integrity sha1-/Ln3FNvK77aseYTEaDkSx0cwJIo=

electron-packager@^17.1.0:
  version "17.1.2"
  resolved "http://registry.npm.htsc/electron-packager/-/electron-packager-17.1.2.tgz#18030b28024d242b706d0a8a67ed4cd1a57311aa"
  integrity sha512-XofXdikjYI7MVBcnXeoOvRR+yFFFHOLs3J7PF5KYQweigtgLshcH4W660PsvHr4lYZ03JBpLyEcUB8DzHZ+BNw==
  dependencies:
    "@electron/asar" "^3.2.1"
    "@electron/get" "^2.0.0"
    "@electron/notarize" "^1.2.3"
    "@electron/osx-sign" "^1.0.5"
    "@electron/universal" "^1.3.2"
    cross-spawn-windows-exe "^1.2.0"
    debug "^4.0.1"
    extract-zip "^2.0.0"
    filenamify "^4.1.0"
    fs-extra "^11.1.0"
    galactus "^1.0.0"
    get-package-info "^1.0.0"
    junk "^3.1.0"
    parse-author "^2.0.0"
    plist "^3.0.0"
    rcedit "^3.0.1"
    resolve "^1.1.6"
    semver "^7.1.3"
    yargs-parser "^21.1.1"

electron-packager@^17.1.1:
  version "17.1.1"
  resolved "http://npm.htsc/electron-packager/download/electron-packager-17.1.1.tgz#f156fc63d3a66f4e902e4b42992550a172982d59"
  integrity sha1-8Vb8Y9Omb06QLktCmSVQoXKYLVk=
  dependencies:
    "@electron/asar" "^3.2.1"
    "@electron/get" "^2.0.0"
    "@electron/notarize" "^1.2.3"
    "@electron/osx-sign" "^1.0.1"
    "@electron/universal" "^1.3.2"
    cross-spawn-windows-exe "^1.2.0"
    debug "^4.0.1"
    extract-zip "^2.0.0"
    filenamify "^4.1.0"
    fs-extra "^10.1.0"
    galactus "^0.2.1"
    get-package-info "^1.0.0"
    junk "^3.1.0"
    parse-author "^2.0.0"
    plist "^3.0.0"
    rcedit "^3.0.1"
    resolve "^1.1.6"
    semver "^7.1.3"
    yargs-parser "^21.1.1"

electron-squirrel-startup@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/electron-squirrel-startup/download/electron-squirrel-startup-1.0.0.tgz#19b4e55933fa0ef8f556784b9c660f772546a0b8"
  integrity sha1-GbTlWTP6Dvj1VnhLnGYPdyVGoLg=
  dependencies:
    debug "^2.2.0"

electron-to-chromium@^1.4.431:
  version "1.4.438"
  resolved "http://npm.htsc/electron-to-chromium/download/electron-to-chromium-1.4.438.tgz#425f0d51862d36f90817d6dfb7fa2a53ff6a0a73"
  integrity sha1-Ql8NUYYtNvkIF9bft/oqU/9qCnM=

electron-to-chromium@^1.4.535:
  version "1.4.554"
  resolved "http://npm.htsc/electron-to-chromium/download/electron-to-chromium-1.4.554.tgz#04e09c2ee31dc0f1546174033809b54cc372740b"
  integrity sha1-BOCcLuMdwPFUYXQDOAm1TMNydAs=

electron-updater@^5.3.0:
  version "5.3.0"
  resolved "http://npm.htsc/electron-updater/download/electron-updater-5.3.0.tgz#3ba0d20407911a2edc5a68bee45c5aa2023e9ff8"
  integrity sha1-O6DSBAeRGi7cWmi+5FxaogI+n/g=
  dependencies:
    "@types/semver" "^7.3.6"
    builder-util-runtime "9.1.1"
    fs-extra "^10.0.0"
    js-yaml "^4.1.0"
    lazy-val "^1.0.5"
    lodash.escaperegexp "^4.1.2"
    lodash.isequal "^4.5.0"
    semver "^7.3.5"
    typed-emitter "^2.1.0"

electron-winstaller@^5.0.0:
  version "5.1.0"
  resolved "http://npm.htsc/electron-winstaller/download/electron-winstaller-5.1.0.tgz#528ed08a65858dd678d9178e8ae24f82a4e12271"
  integrity sha1-Uo7QimWFjdZ42ReOiuJPgqThInE=
  dependencies:
    "@electron/asar" "^3.2.1"
    debug "^4.1.1"
    fs-extra "^7.0.1"
    lodash.template "^4.2.2"
    temp "^0.9.0"

electron-wix-msi@^5.0.0:
  version "5.1.3"
  resolved "http://registry.npm.htsc/electron-wix-msi/-/electron-wix-msi-5.1.3.tgz#ab85dc1145a7ce7ae7724ed3ca3f92c447988c9a"
  integrity sha512-EYj1cm1nZoVHmIIx3o0aKt784lxdEpJnXbEnyypklUCnglqSb7ni+1xi1Vp/gtrGS/mzIxnWBT+x5fIfuDjhvA==
  dependencies:
    "@electron/windows-sign" "^1.1.2"
    debug "^4.3.4"
    fs-extra "^10.1.0"
    klaw "^4.1.0"
    lodash "^4.17.21"
    rcedit "^4.0.1"
    rcinfo "^0.1.3"
    semver "^7.6.0"
  optionalDependencies:
    "@bitdisaster/exe-icon-extractor" "^1.0.10"

electron@^36.3.1:
  version "36.4.0"
  resolved "http://registry.npm.htsc/electron/-/electron-36.4.0.tgz#9463bf5fa7565ae7be3a274f7f6a46359bcfe74d"
  integrity sha512-LLOOZEuW5oqvnjC7HBQhIqjIIJAZCIFjQxltQGLfEC7XFsBoZgQ3u3iFj+Kzw68Xj97u1n57Jdt7P98qLvUibQ==
  dependencies:
    "@electron/get" "^2.0.0"
    "@types/node" "^22.7.7"
    extract-zip "^2.0.1"

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://npm.htsc/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "http://npm.htsc/emoji-regex/download/emoji-regex-9.2.2.tgz#840c8803b0d8047f4ff0cf963176b32d4ef3ed72"
  integrity sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/emojis-list/download/emojis-list-3.0.0.tgz#5570662046ad29e2e916e71aae260abdff4f6a78"
  integrity sha1-VXBmIEatKeLpFucariYKvf9Pang=

encode-utf8@^1.0.3:
  version "1.0.3"
  resolved "http://npm.htsc/encode-utf8/download/encode-utf8-1.0.3.tgz#f30fdd31da07fb596f281beb2f6b027851994cda"
  integrity sha1-8w/dMdoH+1lvKBvrL2sCeFGZTNo=

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/encodeurl/download/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

encoding@^0.1.13:
  version "0.1.13"
  resolved "http://npm.htsc/encoding/download/encoding-0.1.13.tgz#56574afdd791f54a8e9b2785c0582a2d26210fa9"
  integrity sha1-VldK/deR9UqOmyeFwFgqLSYhD6k=
  dependencies:
    iconv-lite "^0.6.2"

end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "http://npm.htsc/end-of-stream/download/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

enhanced-resolve@^0.9.1:
  version "0.9.1"
  resolved "http://npm.htsc/enhanced-resolve/download/enhanced-resolve-0.9.1.tgz#4d6e689b3725f86090927ccc86cd9f1635b89e2e"
  integrity sha1-TW5omzcl+GCQknzMhs2fFjW4ni4=
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.2.0"
    tapable "^0.1.8"

enhanced-resolve@^5.0.0, enhanced-resolve@^5.15.0:
  version "5.15.0"
  resolved "http://npm.htsc/enhanced-resolve/download/enhanced-resolve-5.15.0.tgz#1af946c7d93603eb88e9896cee4904dc012e9c35"
  integrity sha1-GvlGx9k2A+uI6Yls7kkE3AEunDU=
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

enquirer@^2.3.5, enquirer@^2.3.6:
  version "2.3.6"
  resolved "http://npm.htsc/enquirer/download/enquirer-2.3.6.tgz#2a7fe5dd634a1e4125a975ec994ff5456dc3734d"
  integrity sha1-Kn/l3WNKHkElqXXsmU/1RW3Dc00=
  dependencies:
    ansi-colors "^4.1.1"

entities@^2.0.0:
  version "2.2.0"
  resolved "http://npm.htsc/entities/download/entities-2.2.0.tgz#098dc90ebb83d8dffa089d55256b351d34c4da55"
  integrity sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=

entities@^4.2.0, entities@^4.4.0:
  version "4.5.0"
  resolved "http://npm.htsc/entities/download/entities-4.5.0.tgz#5d268ea5e7113ec74c4d033b79ea5a35a488fb48"
  integrity sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=

env-paths@^2.2.0:
  version "2.2.1"
  resolved "http://npm.htsc/env-paths/download/env-paths-2.2.1.tgz#420399d416ce1fbe9bc0a07c62fa68d67fd0f8f2"
  integrity sha1-QgOZ1BbOH76bwKB8Yvpo1n/Q+PI=

err-code@^2.0.2:
  version "2.0.3"
  resolved "http://npm.htsc/err-code/download/err-code-2.0.3.tgz#23c2f3b756ffdfc608d30e27c9a941024807e7f9"
  integrity sha1-I8Lzt1b/38YI0w4nyalBAkgH5/k=

error-ex@^1.2.0, error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://npm.htsc/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

es-abstract@^1.19.0, es-abstract@^1.20.4:
  version "1.21.2"
  resolved "http://npm.htsc/es-abstract/download/es-abstract-1.21.2.tgz#a56b9695322c8a185dc25975aa3b8ec31d0e7eff"
  integrity sha1-pWuWlTIsihhdwll1qjuOwx0Ofv8=
  dependencies:
    array-buffer-byte-length "^1.0.0"
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    es-set-tostringtag "^2.0.1"
    es-to-primitive "^1.2.1"
    function.prototype.name "^1.1.5"
    get-intrinsic "^1.2.0"
    get-symbol-description "^1.0.0"
    globalthis "^1.0.3"
    gopd "^1.0.1"
    has "^1.0.3"
    has-property-descriptors "^1.0.0"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    internal-slot "^1.0.5"
    is-array-buffer "^3.0.2"
    is-callable "^1.2.7"
    is-negative-zero "^2.0.2"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.2"
    is-string "^1.0.7"
    is-typed-array "^1.1.10"
    is-weakref "^1.0.2"
    object-inspect "^1.12.3"
    object-keys "^1.1.1"
    object.assign "^4.1.4"
    regexp.prototype.flags "^1.4.3"
    safe-regex-test "^1.0.0"
    string.prototype.trim "^1.2.7"
    string.prototype.trimend "^1.0.6"
    string.prototype.trimstart "^1.0.6"
    typed-array-length "^1.0.4"
    unbox-primitive "^1.0.2"
    which-typed-array "^1.1.9"

es-module-lexer@^1.2.1:
  version "1.3.0"
  resolved "http://npm.htsc/es-module-lexer/download/es-module-lexer-1.3.0.tgz#6be9c9e0b4543a60cd166ff6f8b4e9dae0b0c16f"
  integrity sha1-a+nJ4LRUOmDNFm/2+LTp2uCwwW8=

es-set-tostringtag@^2.0.1:
  version "2.0.1"
  resolved "http://npm.htsc/es-set-tostringtag/download/es-set-tostringtag-2.0.1.tgz#338d502f6f674301d710b80c8592de8a15f09cd8"
  integrity sha1-M41QL29nQwHXELgMhZLeihXwnNg=
  dependencies:
    get-intrinsic "^1.1.3"
    has "^1.0.3"
    has-tostringtag "^1.0.0"

es-shim-unscopables@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/es-shim-unscopables/download/es-shim-unscopables-1.0.0.tgz#702e632193201e3edf8713635d083d378e510241"
  integrity sha1-cC5jIZMgHj7fhxNjXQg9N45RAkE=
  dependencies:
    has "^1.0.3"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "http://npm.htsc/es-to-primitive/download/es-to-primitive-1.2.1.tgz#e55cd4c9cdc188bcefb03b366c736323fc5c898a"
  integrity sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

es6-error@^4.1.1:
  version "4.1.1"
  resolved "http://npm.htsc/es6-error/download/es6-error-4.1.1.tgz#9e3af407459deed47e9a91f9b885a84eb05c561d"
  integrity sha1-njr0B0Wd7tR+mpH5uIWoTrBcVh0=

escalade@^3.1.1:
  version "3.1.1"
  resolved "http://npm.htsc/escalade/download/escalade-3.1.1.tgz#d8cfdc7000965c5a0174b4a82eaa5c0552742e40"
  integrity sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=

escape-html@~1.0.3:
  version "1.0.3"
  resolved "http://npm.htsc/escape-html/download/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://npm.htsc/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz#14ba83a5d373e3d311e5afca29cf5bfad965bf34"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

eslint-config-airbnb-base@^14.2.1:
  version "14.2.1"
  resolved "http://npm.htsc/eslint-config-airbnb-base/download/eslint-config-airbnb-base-14.2.1.tgz#8a2eb38455dc5a312550193b319cdaeef042cd1e"
  integrity sha1-ii6zhFXcWjElUBk7MZza7vBCzR4=
  dependencies:
    confusing-browser-globals "^1.0.10"
    object.assign "^4.1.2"
    object.entries "^1.1.2"

eslint-config-airbnb@^18.2.1:
  version "18.2.1"
  resolved "http://npm.htsc/eslint-config-airbnb/download/eslint-config-airbnb-18.2.1.tgz#b7fe2b42f9f8173e825b73c8014b592e449c98d9"
  integrity sha1-t/4rQvn4Fz6CW3PIAUtZLkScmNk=
  dependencies:
    eslint-config-airbnb-base "^14.2.1"
    object.assign "^4.1.2"
    object.entries "^1.1.2"

eslint-config-prettier@^8.3.0:
  version "8.8.0"
  resolved "http://npm.htsc/eslint-config-prettier/download/eslint-config-prettier-8.8.0.tgz#bfda738d412adc917fd7b038857110efe98c9348"
  integrity sha1-v9pzjUEq3JF/17A4hXEQ7+mMk0g=

eslint-import-resolver-node@^0.3.7:
  version "0.3.7"
  resolved "http://npm.htsc/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.7.tgz#83b375187d412324a1963d84fa664377a23eb4d7"
  integrity sha1-g7N1GH1BIyShlj2E+mZDd6I+tNc=
  dependencies:
    debug "^3.2.7"
    is-core-module "^2.11.0"
    resolve "^1.22.1"

eslint-import-resolver-webpack@^0.13.1:
  version "0.13.2"
  resolved "http://npm.htsc/eslint-import-resolver-webpack/download/eslint-import-resolver-webpack-0.13.2.tgz#fc813df0d08b9265cc7072d22393bda5198bdc1e"
  integrity sha1-/IE98NCLkmXMcHLSI5O9pRmL3B4=
  dependencies:
    array-find "^1.0.0"
    debug "^3.2.7"
    enhanced-resolve "^0.9.1"
    find-root "^1.1.0"
    has "^1.0.3"
    interpret "^1.4.0"
    is-core-module "^2.7.0"
    is-regex "^1.1.4"
    lodash "^4.17.21"
    resolve "^1.20.0"
    semver "^5.7.1"

eslint-module-utils@^2.7.4:
  version "2.8.0"
  resolved "http://npm.htsc/eslint-module-utils/download/eslint-module-utils-2.8.0.tgz#e439fee65fc33f6bba630ff621efc38ec0375c49"
  integrity sha1-5Dn+5l/DP2u6Yw/2Ie/DjsA3XEk=
  dependencies:
    debug "^3.2.7"

eslint-plugin-es@^3.0.0:
  version "3.0.1"
  resolved "http://npm.htsc/eslint-plugin-es/download/eslint-plugin-es-3.0.1.tgz#75a7cdfdccddc0589934aeeb384175f221c57893"
  integrity sha1-dafN/czdwFiZNK7rOEF18iHFeJM=
  dependencies:
    eslint-utils "^2.0.0"
    regexpp "^3.0.0"

eslint-plugin-eslint-comments@^3.1.1:
  version "3.2.0"
  resolved "http://npm.htsc/eslint-plugin-eslint-comments/download/eslint-plugin-eslint-comments-3.2.0.tgz#9e1cd7b4413526abb313933071d7aba05ca12ffa"
  integrity sha1-nhzXtEE1JquzE5MwcderoFyhL/o=
  dependencies:
    escape-string-regexp "^1.0.5"
    ignore "^5.0.5"

eslint-plugin-filenames@^1.3.2:
  version "1.3.2"
  resolved "http://npm.htsc/eslint-plugin-filenames/download/eslint-plugin-filenames-1.3.2.tgz#7094f00d7aefdd6999e3ac19f72cea058e590cf7"
  integrity sha1-cJTwDXrv3WmZ46wZ9yzqBY5ZDPc=
  dependencies:
    lodash.camelcase "4.3.0"
    lodash.kebabcase "4.1.1"
    lodash.snakecase "4.1.1"
    lodash.upperfirst "4.3.1"

eslint-plugin-import@^2.22.1:
  version "2.27.5"
  resolved "http://npm.htsc/eslint-plugin-import/download/eslint-plugin-import-2.27.5.tgz#876a6d03f52608a3e5bb439c2550588e51dd6c65"
  integrity sha1-h2ptA/UmCKPlu0OcJVBYjlHdbGU=
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    array.prototype.flatmap "^1.3.1"
    debug "^3.2.7"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.7"
    eslint-module-utils "^2.7.4"
    has "^1.0.3"
    is-core-module "^2.11.0"
    is-glob "^4.0.3"
    minimatch "^3.1.2"
    object.values "^1.1.6"
    resolve "^1.22.1"
    semver "^6.3.0"
    tsconfig-paths "^3.14.1"

eslint-plugin-jsx-a11y@^6.4.1:
  version "6.7.1"
  resolved "http://npm.htsc/eslint-plugin-jsx-a11y/download/eslint-plugin-jsx-a11y-6.7.1.tgz#fca5e02d115f48c9a597a6894d5bcec2f7a76976"
  integrity sha1-/KXgLRFfSMmll6aJTVvOwvenaXY=
  dependencies:
    "@babel/runtime" "^7.20.7"
    aria-query "^5.1.3"
    array-includes "^3.1.6"
    array.prototype.flatmap "^1.3.1"
    ast-types-flow "^0.0.7"
    axe-core "^4.6.2"
    axobject-query "^3.1.1"
    damerau-levenshtein "^1.0.8"
    emoji-regex "^9.2.2"
    has "^1.0.3"
    jsx-ast-utils "^3.3.3"
    language-tags "=1.0.5"
    minimatch "^3.1.2"
    object.entries "^1.1.6"
    object.fromentries "^2.0.6"
    semver "^6.3.0"

eslint-plugin-markdown@^2.2.0:
  version "2.2.1"
  resolved "http://npm.htsc/eslint-plugin-markdown/download/eslint-plugin-markdown-2.2.1.tgz#76b8a970099fbffc6cc1ffcad9772b96911c027a"
  integrity sha1-dripcAmfv/xswf/K2XcrlpEcAno=
  dependencies:
    mdast-util-from-markdown "^0.8.5"

eslint-plugin-node@^11.1.0:
  version "11.1.0"
  resolved "http://npm.htsc/eslint-plugin-node/download/eslint-plugin-node-11.1.0.tgz#c95544416ee4ada26740a30474eefc5402dc671d"
  integrity sha1-yVVEQW7kraJnQKMEdO78VALcZx0=
  dependencies:
    eslint-plugin-es "^3.0.0"
    eslint-utils "^2.0.0"
    ignore "^5.1.1"
    minimatch "^3.0.4"
    resolve "^1.10.1"
    semver "^6.1.0"

eslint-plugin-prettier@^3.4.1:
  version "3.4.1"
  resolved "http://npm.htsc/eslint-plugin-prettier/download/eslint-plugin-prettier-3.4.1.tgz#e9ddb200efb6f3d05ffe83b1665a716af4a387e5"
  integrity sha1-6d2yAO+289Bf/oOxZlpxavSjh+U=
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-promise@^5.1.0:
  version "5.2.0"
  resolved "http://npm.htsc/eslint-plugin-promise/download/eslint-plugin-promise-5.2.0.tgz#a596acc32981627eb36d9d75f9666ac1a4564971"
  integrity sha1-pZaswymBYn6zbZ11+WZqwaRWSXE=

eslint-plugin-react-hooks@^4.2.0:
  version "4.6.0"
  resolved "http://npm.htsc/eslint-plugin-react-hooks/download/eslint-plugin-react-hooks-4.6.0.tgz#4c3e697ad95b77e93f8646aaa1630c1ba607edd3"
  integrity sha1-TD5petlbd+k/hkaqoWMMG6YH7dM=

eslint-plugin-react@^7.24.0:
  version "7.32.2"
  resolved "http://npm.htsc/eslint-plugin-react/download/eslint-plugin-react-7.32.2.tgz#e71f21c7c265ebce01bcbc9d0955170c55571f10"
  integrity sha1-5x8hx8Jl684BvLydCVUXDFVXHxA=
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flatmap "^1.3.1"
    array.prototype.tosorted "^1.1.1"
    doctrine "^2.1.0"
    estraverse "^5.3.0"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.6"
    object.fromentries "^2.0.6"
    object.hasown "^1.1.2"
    object.values "^1.1.6"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.4"
    semver "^6.3.0"
    string.prototype.matchall "^4.0.8"

eslint-plugin-vue@8.7.1:
  version "8.7.1"
  resolved "http://npm.htsc/eslint-plugin-vue/download/eslint-plugin-vue-8.7.1.tgz#f13c53547a0c9d64588a675cc5ecc6ccaf63703f"
  integrity sha1-8TxTVHoMnWRYimdcxezGzK9jcD8=
  dependencies:
    eslint-utils "^3.0.0"
    natural-compare "^1.4.0"
    nth-check "^2.0.1"
    postcss-selector-parser "^6.0.9"
    semver "^7.3.5"
    vue-eslint-parser "^8.0.1"

eslint-rule-composer@^0.3.0:
  version "0.3.0"
  resolved "http://npm.htsc/eslint-rule-composer/download/eslint-rule-composer-0.3.0.tgz#79320c927b0c5c0d3d3d2b76c8b4a488f25bbaf9"
  integrity sha1-eTIMknsMXA09PSt2yLSkiPJbuvk=

eslint-scope@5.1.1, eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "http://npm.htsc/eslint-scope/download/eslint-scope-5.1.1.tgz#e786e59a66cb92b3f6c1fb0d508aab174848f48c"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-scope@^7.0.0:
  version "7.2.2"
  resolved "http://npm.htsc/eslint-scope/download/eslint-scope-7.2.2.tgz#deb4f92563390f32006894af62a22dba1c46423f"
  integrity sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-utils@^2.0.0, eslint-utils@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/eslint-utils/download/eslint-utils-2.1.0.tgz#d2de5e03424e707dc10c74068ddedae708741b27"
  integrity sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-utils@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/eslint-utils/download/eslint-utils-3.0.0.tgz#8aebaface7345bb33559db0a1f13a1d2d48c3672"
  integrity sha1-iuuvrOc0W7M1WdsKHxOh0tSMNnI=
  dependencies:
    eslint-visitor-keys "^2.0.0"

eslint-visitor-keys@^1.1.0, eslint-visitor-keys@^1.3.0:
  version "1.3.0"
  resolved "http://npm.htsc/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz#30ebd1ef7c2fdff01c3a4f151044af25fab0523e"
  integrity sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=

eslint-visitor-keys@^2.0.0, eslint-visitor-keys@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz#f65328259305927392c938ed44eb0a5c9b2bd303"
  integrity sha1-9lMoJZMFknOSyTjtROsKXJsr0wM=

eslint-visitor-keys@^3.1.0:
  version "3.4.2"
  resolved "http://npm.htsc/eslint-visitor-keys/download/eslint-visitor-keys-3.4.2.tgz#8c2095440eca8c933bedcadf16fefa44dbe9ba5f"
  integrity sha1-jCCVRA7KjJM77crfFv76RNvpul8=

eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1:
  version "3.4.1"
  resolved "http://npm.htsc/eslint-visitor-keys/download/eslint-visitor-keys-3.4.1.tgz#c22c48f48942d08ca824cc526211ae400478a994"
  integrity sha1-wixI9IlC0IyoJMxSYhGuQAR4qZQ=

eslint@^7.32.0:
  version "7.32.0"
  resolved "http://npm.htsc/eslint/download/eslint-7.32.0.tgz#c6d328a14be3fb08c8d1d21e12c02fdb7a2a812d"
  integrity sha1-xtMooUvj+wjI0dIeEsAv23oqgS0=
  dependencies:
    "@babel/code-frame" "7.12.11"
    "@eslint/eslintrc" "^0.4.3"
    "@humanwhocodes/config-array" "^0.5.0"
    ajv "^6.10.0"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.0.1"
    doctrine "^3.0.0"
    enquirer "^2.3.5"
    escape-string-regexp "^4.0.0"
    eslint-scope "^5.1.1"
    eslint-utils "^2.1.0"
    eslint-visitor-keys "^2.0.0"
    espree "^7.3.1"
    esquery "^1.4.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.1.2"
    globals "^13.6.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.0.4"
    natural-compare "^1.4.0"
    optionator "^0.9.1"
    progress "^2.0.0"
    regexpp "^3.1.0"
    semver "^7.2.1"
    strip-ansi "^6.0.0"
    strip-json-comments "^3.1.0"
    table "^6.0.9"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^7.3.0, espree@^7.3.1:
  version "7.3.1"
  resolved "http://npm.htsc/espree/download/espree-7.3.1.tgz#f2df330b752c6f55019f8bd89b7660039c1bbbb6"
  integrity sha1-8t8zC3Usb1UBn4vYm3ZgA5wbu7Y=
  dependencies:
    acorn "^7.4.0"
    acorn-jsx "^5.3.1"
    eslint-visitor-keys "^1.3.0"

espree@^9.0.0:
  version "9.6.1"
  resolved "http://npm.htsc/espree/download/espree-9.6.1.tgz#a2a17b8e434690a5432f2f8018ce71d331a48c6f"
  integrity sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esprima@^4.0.0:
  version "4.0.1"
  resolved "http://npm.htsc/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.4.0:
  version "1.5.0"
  resolved "http://npm.htsc/esquery/download/esquery-1.5.0.tgz#6ce17738de8577694edd7361c57182ac8cb0db0b"
  integrity sha1-bOF3ON6Fd2lO3XNhxXGCrIyw2ws=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "http://npm.htsc/esrecurse/download/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "http://npm.htsc/estraverse/download/estraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"
  resolved "http://npm.htsc/estraverse/download/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://npm.htsc/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "http://npm.htsc/etag/download/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

eventemitter3@^4.0.0:
  version "4.0.7"
  resolved "http://npm.htsc/eventemitter3/download/eventemitter3-4.0.7.tgz#2de9b68f6528d5644ef5c59526a1b4a07306169f"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

events@^3.2.0:
  version "3.3.0"
  resolved "http://npm.htsc/events/download/events-3.3.0.tgz#31a95ad0a924e2d2c419a813aeb2c4e878ea7400"
  integrity sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=

execa@^0.8.0:
  version "0.8.0"
  resolved "http://npm.htsc/execa/download/execa-0.8.0.tgz#d8d76bbc1b55217ed190fd6dd49d3c774ecfc8da"
  integrity sha1-2NdrvBtVIX7RkP1t1J08d07PyNo=
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/execa/download/execa-1.0.0.tgz#c6236a5bb4df6d6f15e88e7f017798216749ddd8"
  integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^4.1.0:
  version "4.1.0"
  resolved "http://npm.htsc/execa/download/execa-4.1.0.tgz#4e5491ad1572f2f17a77d388c6c857135b22847a"
  integrity sha1-TlSRrRVy8vF6d9OIxshXE1sihHo=
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    human-signals "^1.1.1"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.0"
    onetime "^5.1.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

execa@^5.0.0:
  version "5.1.1"
  resolved "http://npm.htsc/execa/download/execa-5.1.1.tgz#f80ad9cbf4298f7bd1d4c9555c21e93741c411dd"
  integrity sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

expand-tilde@^2.0.0, expand-tilde@^2.0.2:
  version "2.0.2"
  resolved "http://npm.htsc/expand-tilde/download/expand-tilde-2.0.2.tgz#97e801aa052df02454de46b02bf621642cdc8502"
  integrity sha1-l+gBqgUt8CRU3kawK/YhZCzchQI=
  dependencies:
    homedir-polyfill "^1.0.1"

exponential-backoff@^3.1.1:
  version "3.1.1"
  resolved "http://npm.htsc/exponential-backoff/download/exponential-backoff-3.1.1.tgz#64ac7526fe341ab18a39016cd22c787d01e00bf6"
  integrity sha1-ZKx1Jv40GrGKOQFs0ix4fQHgC/Y=

express-ws@^5.0.2:
  version "5.0.2"
  resolved "http://npm.htsc/express-ws/download/express-ws-5.0.2.tgz#5b02d41b937d05199c6c266d7cc931c823bda8eb"
  integrity sha1-WwLUG5N9BRmcbCZtfMkxyCO9qOs=
  dependencies:
    ws "^7.4.6"

express@^4.17.1, express@^4.17.3:
  version "4.18.2"
  resolved "http://npm.htsc/express/download/express-4.18.2.tgz#3fabe08296e930c796c19e3c516979386ba9fd59"
  integrity sha1-P6vggpbpMMeWwZ48UWl5OGup/Vk=
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.1"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.5.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.2.0"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.7"
    qs "6.11.0"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.18.0"
    serve-static "1.15.0"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

extract-zip@^2.0.0, extract-zip@^2.0.1:
  version "2.0.1"
  resolved "http://npm.htsc/extract-zip/download/extract-zip-2.0.1.tgz#663dca56fe46df890d5f131ef4a06d22bb8ba13a"
  integrity sha1-Zj3KVv5G34kNXxMe9KBtIruLoTo=
  dependencies:
    debug "^4.1.1"
    get-stream "^5.1.0"
    yauzl "^2.10.0"
  optionalDependencies:
    "@types/yauzl" "^2.9.1"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "http://npm.htsc/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz#****************************************"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@^1.1.2:
  version "1.3.0"
  resolved "http://npm.htsc/fast-diff/download/fast-diff-1.3.0.tgz#ece407fa550a64d638536cd727e129c61616e0f0"
  integrity sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=

fast-glob@^3.2.12:
  version "3.3.0"
  resolved "http://npm.htsc/fast-glob/download/fast-glob-3.3.0.tgz#7c40cb491e1e2ed5664749e87bfb516dbe8727c0"
  integrity sha1-fEDLSR4eLtVmR0noe/tRbb6HJ8A=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-glob@^3.2.7, fast-glob@^3.2.9:
  version "3.2.12"
  resolved "http://npm.htsc/fast-glob/download/fast-glob-3.2.12.tgz#7f39ec99c2e6ab030337142da9e0c18f37afae80"
  integrity sha1-fznsmcLmqwMDNxQtqeDBjzevroA=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "http://npm.htsc/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "http://npm.htsc/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastest-levenshtein@^1.0.16:
  version "1.0.16"
  resolved "http://npm.htsc/fastest-levenshtein/download/fastest-levenshtein-1.0.16.tgz#210e61b6ff181de91ea9b3d1b84fdedd47e034e5"
  integrity sha1-IQ5htv8YHekeqbPRuE/e3UfgNOU=

fastq@^1.6.0:
  version "1.15.0"
  resolved "http://npm.htsc/fastq/download/fastq-1.15.0.tgz#d04d07c6a2a68fe4599fea8d2e103a937fae6b3a"
  integrity sha1-0E0HxqKmj+RZn+qNLhA6k3+uazo=
  dependencies:
    reusify "^1.0.4"

faye-websocket@^0.11.3:
  version "0.11.4"
  resolved "http://npm.htsc/faye-websocket/download/faye-websocket-0.11.4.tgz#7f0d9275cfdd86a1c963dc8b65fcc451edcbb1da"
  integrity sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=
  dependencies:
    websocket-driver ">=0.5.1"

fd-slicer@~1.1.0:
  version "1.1.0"
  resolved "http://npm.htsc/fd-slicer/download/fd-slicer-1.1.0.tgz#25c7c89cb1f9077f8891bbe61d8f390eae256f1e"
  integrity sha1-JcfInLH5B3+IkbvmHY85Dq4lbx4=
  dependencies:
    pend "~1.2.0"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "http://npm.htsc/file-entry-cache/download/file-entry-cache-6.0.1.tgz#211b2dd9659cb0394b073e7323ac3c933d522027"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

filename-reserved-regex@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/filename-reserved-regex/download/filename-reserved-regex-2.0.0.tgz#abf73dfab735d045440abfea2d91f389ebbfa229"
  integrity sha1-q/c9+rc10EVECr/qLZHzieu/oik=

filenamify@^4.1.0:
  version "4.3.0"
  resolved "http://npm.htsc/filenamify/download/filenamify-4.3.0.tgz#62391cb58f02b09971c9d4f9d63b3cf9aba03106"
  integrity sha1-YjkctY8CsJlxydT51js8+augMQY=
  dependencies:
    filename-reserved-regex "^2.0.0"
    strip-outer "^1.0.1"
    trim-repeated "^1.0.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "http://npm.htsc/fill-range/download/fill-range-7.0.1.tgz#1919a6a7c75fe38b2c7c77e5198535da9acdda40"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@1.2.0:
  version "1.2.0"
  resolved "http://npm.htsc/finalhandler/download/finalhandler-1.2.0.tgz#7d23fe5731b207b4640e4fcd00aec1f9207a7b32"
  integrity sha1-fSP+VzGyB7RkDk/NAK7B+SB6ezI=
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

find-root@^1.1.0:
  version "1.1.0"
  resolved "http://npm.htsc/find-root/download/find-root-1.1.0.tgz#abcfc8ba76f708c42a97b3d685b7e9450bfb9ce4"
  integrity sha1-q8/Iunb3CMQql7PWhbfpRQv7nOQ=

find-up@^2.0.0:
  version "2.1.0"
  resolved "http://npm.htsc/find-up/download/find-up-2.1.0.tgz#45d1b7e506c717ddd482775a2b77920a3c0c57a7"
  integrity sha1-RdG35QbHF93UgndaK3eSCjwMV6c=
  dependencies:
    locate-path "^2.0.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "http://npm.htsc/find-up/download/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0:
  version "5.0.0"
  resolved "http://npm.htsc/find-up/download/find-up-5.0.0.tgz#4c92819ecb7083561e4f4a240a86be5198f536fc"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.0.4"
  resolved "http://npm.htsc/flat-cache/download/flat-cache-3.0.4.tgz#61b0338302b2fe9f957dcc32fc2a87f1c3048b11"
  integrity sha1-YbAzgwKy/p+Vfcwy/CqH8cMEixE=
  dependencies:
    flatted "^3.1.0"
    rimraf "^3.0.2"

flatted@^3.1.0:
  version "3.2.7"
  resolved "http://npm.htsc/flatted/download/flatted-3.2.7.tgz#609f39207cb614b89d0765b477cb2d437fbf9787"
  integrity sha1-YJ85IHy2FLidB2W0d8stQ3+/l4c=

flora-colossus@^1.0.0:
  version "1.0.1"
  resolved "http://npm.htsc/flora-colossus/download/flora-colossus-1.0.1.tgz#aba198425a8185341e64f9d2a6a96fd9a3cbdb93"
  integrity sha1-q6GYQlqBhTQeZPnSpqlv2aPL25M=
  dependencies:
    debug "^4.1.1"
    fs-extra "^7.0.0"

flora-colossus@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.htsc/flora-colossus/-/flora-colossus-2.0.0.tgz#af1e85db0a8256ef05f3fb531c1235236c97220a"
  integrity sha512-dz4HxH6pOvbUzZpZ/yXhafjbR2I8cenK5xL0KtBFb7U2ADsR+OwXifnxZjij/pZWF775uSCMzWVd+jDik2H2IA==
  dependencies:
    debug "^4.3.4"
    fs-extra "^10.1.0"

fmix@^0.1.0:
  version "0.1.0"
  resolved "http://npm.htsc/fmix/download/fmix-0.1.0.tgz#c7bbf124dec42c9d191cfb947d0a9778dd986c0c"
  integrity sha1-x7vxJN7ELJ0ZHPuUfQqXeN2YbAw=
  dependencies:
    imul "^1.0.0"

follow-redirects@^1.0.0, follow-redirects@^1.14.9, follow-redirects@^1.15.0:
  version "1.15.2"
  resolved "http://npm.htsc/follow-redirects/download/follow-redirects-1.15.2.tgz#b460864144ba63f2681096f274c4e57026da2c13"
  integrity sha1-tGCGQUS6Y/JoEJbydMTlcCbaLBM=

for-each@^0.3.3:
  version "0.3.3"
  resolved "http://npm.htsc/for-each/download/for-each-0.3.3.tgz#69b447e88a0a5d32c3e7084f3f1710034b21376e"
  integrity sha1-abRH6IoKXTLD5whPPxcQA0shN24=
  dependencies:
    is-callable "^1.1.3"

foreground-child@^3.1.0:
  version "3.1.1"
  resolved "http://npm.htsc/foreground-child/download/foreground-child-3.1.1.tgz#1d173e776d75d2772fed08efe4a0de1ea1b12d0d"
  integrity sha1-HRc+d2110ncv7Qjv5KDeHqGxLQ0=
  dependencies:
    cross-spawn "^7.0.0"
    signal-exit "^4.0.1"

fork-ts-checker-webpack-plugin@^7.2.13:
  version "7.3.0"
  resolved "http://npm.htsc/fork-ts-checker-webpack-plugin/download/fork-ts-checker-webpack-plugin-7.3.0.tgz#a9c984a018493962360d7c7e77a67b44a2d5f3aa"
  integrity sha1-qcmEoBhJOWI2DXx+d6Z7RKLV86o=
  dependencies:
    "@babel/code-frame" "^7.16.7"
    chalk "^4.1.2"
    chokidar "^3.5.3"
    cosmiconfig "^7.0.1"
    deepmerge "^4.2.2"
    fs-extra "^10.0.0"
    memfs "^3.4.1"
    minimatch "^3.0.4"
    node-abort-controller "^3.0.1"
    schema-utils "^3.1.1"
    semver "^7.3.5"
    tapable "^2.2.1"

form-data@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/form-data/download/form-data-4.0.0.tgz#93919daeaf361ee529584b9b31664dc12c9fa452"
  integrity sha1-k5Gdrq82HuUpWEubMWZNwSyfpFI=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

forwarded@0.2.0:
  version "0.2.0"
  resolved "http://npm.htsc/forwarded/download/forwarded-0.2.0.tgz#2269936428aad4c15c7ebe9779a84bf0b2a81811"
  integrity sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=

fresh@0.5.2:
  version "0.5.2"
  resolved "http://npm.htsc/fresh/download/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

fs-extra@^10.0.0, fs-extra@^10.1.0:
  version "10.1.0"
  resolved "http://npm.htsc/fs-extra/download/fs-extra-10.1.0.tgz#02873cfbc4084dde127eaa5f9905eef2325d1abf"
  integrity sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-extra@^11.1.0:
  version "11.3.0"
  resolved "http://registry.npm.htsc/fs-extra/-/fs-extra-11.3.0.tgz#0daced136bbaf65a555a326719af931adc7a314d"
  integrity sha512-Z4XaCL6dUDHfP/jT25jJKMmtxvuwbkrD1vNSMFlo9lNLY2c5FHYSQgHPRZUjAB26TpDEoW9HCOgplrdbaPV/ew==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-extra@^11.1.1:
  version "11.1.1"
  resolved "http://npm.htsc/fs-extra/download/fs-extra-11.1.1.tgz#da69f7c39f3b002378b0954bb6ae7efdc0876e2d"
  integrity sha1-2mn3w587ACN4sJVLtq5+/cCHbi0=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-extra@^4.0.0:
  version "4.0.3"
  resolved "http://npm.htsc/fs-extra/download/fs-extra-4.0.3.tgz#0d852122e5bc5beb453fb028e9c0c9bf36340c94"
  integrity sha1-DYUhIuW8W+tFP7Ao6cDJvzY0DJQ=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-extra@^7.0.0, fs-extra@^7.0.1:
  version "7.0.1"
  resolved "http://npm.htsc/fs-extra/download/fs-extra-7.0.1.tgz#4f189c44aa123b895f722804f55ea23eadc348e9"
  integrity sha1-TxicRKoSO4lfcigE9V6iPq3DSOk=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-extra@^8.1.0:
  version "8.1.0"
  resolved "http://npm.htsc/fs-extra/download/fs-extra-8.1.0.tgz#49d43c45a88cd9677668cb7be1b46efdb8d2e1c0"
  integrity sha1-SdQ8RaiM2Wd2aMt74bRu/bjS4cA=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-extra@^9.0.0, fs-extra@^9.0.1:
  version "9.1.0"
  resolved "http://npm.htsc/fs-extra/download/fs-extra-9.1.0.tgz#5954460c764a8da2094ba3554bf839e6b9a7c86d"
  integrity sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0=
  dependencies:
    at-least-node "^1.0.0"
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-minipass@^2.0.0:
  version "2.1.0"
  resolved "http://npm.htsc/fs-minipass/download/fs-minipass-2.1.0.tgz#7f5036fdbf12c63c169190cbe4199c852271f9fb"
  integrity sha1-f1A2/b8SxjwWkZDL5BmchSJx+fs=
  dependencies:
    minipass "^3.0.0"

fs-minipass@^3.0.0:
  version "3.0.2"
  resolved "http://npm.htsc/fs-minipass/download/fs-minipass-3.0.2.tgz#5b383858efa8c1eb8c33b39e994f7e8555b8b3a3"
  integrity sha1-Wzg4WO+oweuMM7OemU9+hVW4s6M=
  dependencies:
    minipass "^5.0.0"

fs-monkey@^1.0.4:
  version "1.0.4"
  resolved "http://npm.htsc/fs-monkey/download/fs-monkey-1.0.4.tgz#ee8c1b53d3fe8bb7e5d2c5c5dfc0168afdd2f747"
  integrity sha1-7owbU9P+i7fl0sXF38AWiv3S90c=

fs-temp@^1.0.0:
  version "1.2.1"
  resolved "http://npm.htsc/fs-temp/download/fs-temp-1.2.1.tgz#ffd136ef468177accc3c267d4510f6ce3b2b9697"
  integrity sha1-/9E270aBd6zMPCZ9RRD2zjsrlpc=
  dependencies:
    random-path "^0.1.0"

fs-xattr@^0.3.0:
  version "0.3.1"
  resolved "http://npm.htsc/fs-xattr/download/fs-xattr-0.3.1.tgz#a23d88571031f6c56f26d59e0bab7d2e12f49f77"
  integrity sha1-oj2IVxAx9sVvJtWeC6t9LhL0n3c=

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@~2.3.2:
  version "2.3.2"
  resolved "http://npm.htsc/fsevents/download/fsevents-2.3.2.tgz#8a526f78b8fdf4623b709e0b975c52c24c02fd1a"
  integrity sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=

function-bind@^1.1.1:
  version "1.1.1"
  resolved "http://npm.htsc/function-bind/download/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "http://npm.htsc/function-bind/download/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

function.prototype.name@^1.1.5:
  version "1.1.5"
  resolved "http://npm.htsc/function.prototype.name/download/function.prototype.name-1.1.5.tgz#cce0505fe1ffb80503e6f9e46cc64e46a12a9621"
  integrity sha1-zOBQX+H/uAUD5vnkbMZORqEqliE=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.0"
    functions-have-names "^1.2.2"

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

functions-have-names@^1.2.2, functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "http://npm.htsc/functions-have-names/download/functions-have-names-1.2.3.tgz#0404fe4ee2ba2f607f0e0ec3c80bae994133b834"
  integrity sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=

galactus@^0.2.1:
  version "0.2.1"
  resolved "http://npm.htsc/galactus/download/galactus-0.2.1.tgz#cbed2d20a40c1f5679a35908e2b9415733e78db9"
  integrity sha1-y+0tIKQMH1Z5o1kI4rlBVzPnjbk=
  dependencies:
    debug "^3.1.0"
    flora-colossus "^1.0.0"
    fs-extra "^4.0.0"

galactus@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.htsc/galactus/-/galactus-1.0.0.tgz#c2615182afa0c6d0859b92e56ae36d052827db7e"
  integrity sha512-R1fam6D4CyKQGNlvJne4dkNF+PvUUl7TAJInvTGa9fti9qAv95quQz29GXapA4d8Ec266mJJxFVh82M4GIIGDQ==
  dependencies:
    debug "^4.3.4"
    flora-colossus "^2.0.0"
    fs-extra "^10.1.0"

gar@^1.0.4:
  version "1.0.4"
  resolved "http://npm.htsc/gar/download/gar-1.0.4.tgz#f777bc7db425c0572fdeb52676172ca1ae9888b8"
  integrity sha1-93e8fbQlwFcv3rUmdhcsoa6YiLg=

gauge@^4.0.3:
  version "4.0.4"
  resolved "http://npm.htsc/gauge/download/gauge-4.0.4.tgz#52ff0652f2bbf607a989793d53b751bef2328dce"
  integrity sha1-Uv8GUvK79gepiXk9U7dRvvIyjc4=
  dependencies:
    aproba "^1.0.3 || ^2.0.0"
    color-support "^1.1.3"
    console-control-strings "^1.1.0"
    has-unicode "^2.0.1"
    signal-exit "^3.0.7"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"
    wide-align "^1.1.5"

generate-function@^2.0.0:
  version "2.3.1"
  resolved "http://npm.htsc/generate-function/download/generate-function-2.3.1.tgz#f069617690c10c868e73b8465746764f97c3479f"
  integrity sha1-8GlhdpDBDIaOc7hGV0Z2T5fDR58=
  dependencies:
    is-property "^1.0.2"

generate-object-property@^1.1.0:
  version "1.2.0"
  resolved "http://npm.htsc/generate-object-property/download/generate-object-property-1.2.0.tgz#9c0e1c40308ce804f4783618b937fa88f99d50d0"
  integrity sha1-nA4cQDCM6AT0eDYYuTf6iPmdUNA=
  dependencies:
    is-property "^1.0.0"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "http://npm.htsc/gensync/download/gensync-1.0.0-beta.2.tgz#32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.1, get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "http://npm.htsc/get-caller-file/download/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-folder-size@^2.0.1:
  version "2.0.1"
  resolved "http://npm.htsc/get-folder-size/download/get-folder-size-2.0.1.tgz#3fe0524dd3bad05257ef1311331417bcd020a497"
  integrity sha1-P+BSTdO60FJX7xMRMxQXvNAgpJc=
  dependencies:
    gar "^1.0.4"
    tiny-each-async "2.0.3"

get-installed-path@^2.0.3:
  version "2.1.1"
  resolved "http://npm.htsc/get-installed-path/download/get-installed-path-2.1.1.tgz#a1f33dc6b8af542c9331084e8edbe37fe2634152"
  integrity sha1-ofM9xrivVCyTMQhOjtvjf+JjQVI=
  dependencies:
    global-modules "1.0.0"

get-intrinsic@^1.0.2, get-intrinsic@^1.1.1, get-intrinsic@^1.1.3, get-intrinsic@^1.2.0:
  version "1.2.0"
  resolved "http://npm.htsc/get-intrinsic/download/get-intrinsic-1.2.0.tgz#7ad1dc0535f3a2904bba075772763e5051f6d05f"
  integrity sha1-etHcBTXzopBLugdXcnY+UFH20F8=
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.3"

get-own-enumerable-property-symbols@^3.0.0:
  version "3.0.2"
  resolved "http://npm.htsc/get-own-enumerable-property-symbols/download/get-own-enumerable-property-symbols-3.0.2.tgz#b5fde77f22cbe35f390b4e089922c50bce6ef664"
  integrity sha1-tf3nfyLL4185C04ImSLFC85u9mQ=

get-package-info@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/get-package-info/download/get-package-info-1.0.0.tgz#6432796563e28113cd9474dbbd00052985a4999c"
  integrity sha1-ZDJ5ZWPigRPNlHTbvQAFKYWkmZw=
  dependencies:
    bluebird "^3.1.1"
    debug "^2.2.0"
    lodash.get "^4.0.0"
    read-pkg-up "^2.0.0"

get-stream@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/get-stream/download/get-stream-3.0.0.tgz#8e943d1358dc37555054ecbe2edb05aa174ede14"
  integrity sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=

get-stream@^4.0.0:
  version "4.1.0"
  resolved "http://npm.htsc/get-stream/download/get-stream-4.1.0.tgz#c1b255575f3dc21d59bfc79cd3d2b46b1c3a54b5"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-stream@^5.0.0, get-stream@^5.1.0:
  version "5.2.0"
  resolved "http://npm.htsc/get-stream/download/get-stream-5.2.0.tgz#4966a1795ee5ace65e706c4b7beb71257d6e22d3"
  integrity sha1-SWaheV7lrOZecGxLe+txJX1uItM=
  dependencies:
    pump "^3.0.0"

get-stream@^6.0.0:
  version "6.0.1"
  resolved "http://npm.htsc/get-stream/download/get-stream-6.0.1.tgz#a262d8eef67aced57c2852ad6167526a43cbf7b7"
  integrity sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=

get-symbol-description@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/get-symbol-description/download/get-symbol-description-1.0.0.tgz#7fdb81c900101fbd564dd5f1a30af5aadc1e58d6"
  integrity sha1-f9uByQAQH71WTdXxowr1qtweWNY=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.1"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "http://npm.htsc/glob-parent/download/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.4.1:
  version "0.4.1"
  resolved "http://npm.htsc/glob-to-regexp/download/glob-to-regexp-0.4.1.tgz#c75297087c851b9a578bd217dd59a92f59fe546e"
  integrity sha1-x1KXCHyFG5pXi9IX3VmpL1n+VG4=

glob@^10.2.2:
  version "10.3.1"
  resolved "http://npm.htsc/glob/download/glob-10.3.1.tgz#9789cb1b994515bedb811a6deca735b5c37d2bf4"
  integrity sha1-l4nLG5lFFb7bgRpt7Kc1tcN9K/Q=
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^2.0.3"
    minimatch "^9.0.1"
    minipass "^5.0.0 || ^6.0.2"
    path-scurry "^1.10.0"

glob@^7.1.3, glob@^7.1.4, glob@^7.1.6:
  version "7.2.3"
  resolved "http://npm.htsc/glob/download/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-agent@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/global-agent/download/global-agent-3.0.0.tgz#ae7cd31bd3583b93c5a16437a1afe27cc33a1ab6"
  integrity sha1-rnzTG9NYO5PFoWQ3oa/ifMM6GrY=
  dependencies:
    boolean "^3.0.1"
    es6-error "^4.1.1"
    matcher "^3.0.0"
    roarr "^2.15.3"
    semver "^7.3.2"
    serialize-error "^7.0.1"

global-modules@1.0.0, global-modules@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/global-modules/download/global-modules-1.0.0.tgz#6d770f0eb523ac78164d72b5e71a8877265cc3ea"
  integrity sha1-bXcPDrUjrHgWTXK15xqIdyZcw+o=
  dependencies:
    global-prefix "^1.0.1"
    is-windows "^1.0.1"
    resolve-dir "^1.0.0"

global-modules@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/global-modules/download/global-modules-2.0.0.tgz#997605ad2345f27f51539bea26574421215c7780"
  integrity sha1-mXYFrSNF8n9RU5vqJldEISFcd4A=
  dependencies:
    global-prefix "^3.0.0"

global-prefix@^1.0.1:
  version "1.0.2"
  resolved "http://npm.htsc/global-prefix/download/global-prefix-1.0.2.tgz#dbf743c6c14992593c655568cb66ed32c0122ebe"
  integrity sha1-2/dDxsFJklk8ZVVoy2btMsASLr4=
  dependencies:
    expand-tilde "^2.0.2"
    homedir-polyfill "^1.0.1"
    ini "^1.3.4"
    is-windows "^1.0.1"
    which "^1.2.14"

global-prefix@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/global-prefix/download/global-prefix-3.0.0.tgz#fc85f73064df69f50421f47f883fe5b913ba9b97"
  integrity sha1-/IX3MGTfafUEIfR/iD/luRO6m5c=
  dependencies:
    ini "^1.3.5"
    kind-of "^6.0.2"
    which "^1.3.1"

globals@^11.1.0:
  version "11.12.0"
  resolved "http://npm.htsc/globals/download/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^13.6.0, globals@^13.9.0:
  version "13.20.0"
  resolved "http://npm.htsc/globals/download/globals-13.20.0.tgz#ea276a1e508ffd4f1612888f9d1bad1e2717bf82"
  integrity sha1-6idqHlCP/U8WEoiPnRutHicXv4I=
  dependencies:
    type-fest "^0.20.2"

globalthis@^1.0.1, globalthis@^1.0.3:
  version "1.0.3"
  resolved "http://npm.htsc/globalthis/download/globalthis-1.0.3.tgz#5852882a52b80dc301b0660273e1ed082f0b6ccf"
  integrity sha1-WFKIKlK4DcMBsGYCc+HtCC8LbM8=
  dependencies:
    define-properties "^1.1.3"

globby@^11.1.0:
  version "11.1.0"
  resolved "http://npm.htsc/globby/download/globby-11.1.0.tgz#bd4be98bb042f83d796f7e3811991fbe82a0d34b"
  integrity sha1-vUvpi7BC+D15b344EZkfvoKg00s=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

globjoin@^0.1.4:
  version "0.1.4"
  resolved "http://npm.htsc/globjoin/download/globjoin-0.1.4.tgz#2f4494ac8919e3767c5cbb691e9f463324285d43"
  integrity sha1-L0SUrIkZ43Z8XLtpHp9GMyQoXUM=

gopd@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/gopd/download/gopd-1.0.1.tgz#29ff76de69dac7489b7c0918a5788e56477c332c"
  integrity sha1-Kf923mnax0ibfAkYpXiOVkd8Myw=
  dependencies:
    get-intrinsic "^1.1.3"

got@^11.7.0, got@^11.8.5:
  version "11.8.6"
  resolved "http://npm.htsc/got/download/got-11.8.6.tgz#276e827ead8772eddbcfc97170590b841823233a"
  integrity sha1-J26Cfq2Hcu3bz8lxcFkLhBgjIzo=
  dependencies:
    "@sindresorhus/is" "^4.0.0"
    "@szmarczak/http-timer" "^4.0.5"
    "@types/cacheable-request" "^6.0.1"
    "@types/responselike" "^1.0.0"
    cacheable-lookup "^5.0.3"
    cacheable-request "^7.0.2"
    decompress-response "^6.0.0"
    http2-wrapper "^1.0.0-beta.5.2"
    lowercase-keys "^2.0.0"
    p-cancelable "^2.0.0"
    responselike "^2.0.0"

graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.4, graceful-fs@^4.2.6, graceful-fs@^4.2.9:
  version "4.2.11"
  resolved "http://npm.htsc/graceful-fs/download/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3"
  integrity sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=

graphemer@^1.4.0:
  version "1.4.0"
  resolved "http://npm.htsc/graphemer/download/graphemer-1.4.0.tgz#fb2f1d55e0e3a1849aeffc90c4fa0dd53a0e66c6"
  integrity sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "http://npm.htsc/handle-thing/download/handle-thing-2.0.1.tgz#857f79ce359580c340d43081cc648970d0bb234e"
  integrity sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=

hard-rejection@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/hard-rejection/download/hard-rejection-2.1.0.tgz#1c6eda5c1685c63942766d79bb40ae773cecd883"
  integrity sha1-HG7aXBaFxjlCdm15u0Cudzzs2IM=

has-bigints@^1.0.1, has-bigints@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/has-bigints/download/has-bigints-1.0.2.tgz#0871bd3e3d51626f6ca0966668ba35d5602d6eaa"
  integrity sha1-CHG9Pj1RYm9soJZmaLo11WAtbqo=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-property-descriptors@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/has-property-descriptors/download/has-property-descriptors-1.0.0.tgz#610708600606d36961ed04c196193b6a607fa861"
  integrity sha1-YQcIYAYG02lh7QTBlhk7amB/qGE=
  dependencies:
    get-intrinsic "^1.1.1"

has-proto@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/has-proto/download/has-proto-1.0.1.tgz#1885c1305538958aff469fef37937c22795408e0"
  integrity sha1-GIXBMFU4lYr/Rp/vN5N8InlUCOA=

has-symbols@^1.0.2, has-symbols@^1.0.3:
  version "1.0.3"
  resolved "http://npm.htsc/has-symbols/download/has-symbols-1.0.3.tgz#bb7b2c4349251dce87b125f7bdf874aa7c8b39f8"
  integrity sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg=

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/has-tostringtag/download/has-tostringtag-1.0.0.tgz#7e133818a7d394734f941e73c3d3f9291e658b25"
  integrity sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=
  dependencies:
    has-symbols "^1.0.2"

has-unicode@^2.0.1:
  version "2.0.1"
  resolved "http://npm.htsc/has-unicode/download/has-unicode-2.0.1.tgz#e0e6fe6a28cf51138855e086d1691e771de2a8b9"
  integrity sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=

has@^1.0.3:
  version "1.0.3"
  resolved "http://npm.htsc/has/download/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

hasown@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/hasown/download/hasown-2.0.0.tgz#f4c513d454a57b7c7e1650778de226b11700546c"
  integrity sha1-9MUT1FSle3x+FlB3jeImsRcAVGw=
  dependencies:
    function-bind "^1.1.2"

he@^1.2.0:
  version "1.2.0"
  resolved "http://npm.htsc/he/download/he-1.2.0.tgz#84ae65fa7eafb165fddb61566ae14baf05664f0f"
  integrity sha1-hK5l+n6vsWX922FWauFLrwVmTw8=

homedir-polyfill@^1.0.1:
  version "1.0.3"
  resolved "http://npm.htsc/homedir-polyfill/download/homedir-polyfill-1.0.3.tgz#743298cef4e5af3e194161fbadcc2151d3a058e8"
  integrity sha1-dDKYzvTlrz4ZQWH7rcwhUdOgWOg=
  dependencies:
    parse-passwd "^1.0.0"

hosted-git-info@^2.1.4:
  version "2.8.9"
  resolved "http://npm.htsc/hosted-git-info/download/hosted-git-info-2.8.9.tgz#dffc0bf9a21c02209090f2aa69429e1414daf3f9"
  integrity sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=

hosted-git-info@^4.0.1:
  version "4.1.0"
  resolved "http://npm.htsc/hosted-git-info/download/hosted-git-info-4.1.0.tgz#827b82867e9ff1c8d0c4d9d53880397d2c86d224"
  integrity sha1-gnuChn6f8cjQxNnVOIA5fSyG0iQ=
  dependencies:
    lru-cache "^6.0.0"

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "http://npm.htsc/hpack.js/download/hpack.js-2.1.6.tgz#87774c0949e513f42e84575b3c45681fade2a0b2"
  integrity sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

html-entities@^2.3.2:
  version "2.3.6"
  resolved "http://npm.htsc/html-entities/download/html-entities-2.3.6.tgz#966391d58e5737c77bca4025e31721b496ab7454"
  integrity sha1-lmOR1Y5XN8d7ykAl4xchtJardFQ=

html-minifier-terser@^6.0.2:
  version "6.1.0"
  resolved "http://npm.htsc/html-minifier-terser/download/html-minifier-terser-6.1.0.tgz#bfc818934cc07918f6b3669f5774ecdfd48f32ab"
  integrity sha1-v8gYk0zAeRj2s2afV3Ts39SPMqs=
  dependencies:
    camel-case "^4.1.2"
    clean-css "^5.2.2"
    commander "^8.3.0"
    he "^1.2.0"
    param-case "^3.0.4"
    relateurl "^0.2.7"
    terser "^5.10.0"

html-tags@^3.2.0:
  version "3.3.1"
  resolved "http://npm.htsc/html-tags/download/html-tags-3.3.1.tgz#a04026a18c882e4bba8a01a3d39cfe465d40b5ce"
  integrity sha1-oEAmoYyILku6igGj05z+Rl1Atc4=

html-webpack-plugin@^5.3.1:
  version "5.5.3"
  resolved "http://npm.htsc/html-webpack-plugin/download/html-webpack-plugin-5.5.3.tgz#72270f4a78e222b5825b296e5e3e1328ad525a3e"
  integrity sha1-cicPSnjiIrWCWyluXj4TKK1SWj4=
  dependencies:
    "@types/html-minifier-terser" "^6.0.0"
    html-minifier-terser "^6.0.2"
    lodash "^4.17.21"
    pretty-error "^4.0.0"
    tapable "^2.0.0"

htmlparser2@^6.1.0:
  version "6.1.0"
  resolved "http://npm.htsc/htmlparser2/download/htmlparser2-6.1.0.tgz#c4d762b6c3371a05dbe65e94ae43a9f845fb8fb7"
  integrity sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.0.0"
    domutils "^2.5.2"
    entities "^2.0.0"

htmlparser2@^8.0.0:
  version "8.0.2"
  resolved "http://npm.htsc/htmlparser2/download/htmlparser2-8.0.2.tgz#f002151705b383e62433b5cf466f5b716edaec21"
  integrity sha1-8AIVFwWzg+YkM7XPRm9bcW7a7CE=
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.3"
    domutils "^3.0.1"
    entities "^4.4.0"

http-cache-semantics@^4.0.0, http-cache-semantics@^4.1.1:
  version "4.1.1"
  resolved "http://npm.htsc/http-cache-semantics/download/http-cache-semantics-4.1.1.tgz#abe02fcb2985460bf0323be664436ec3476a6d5a"
  integrity sha1-q+AvyymFRgvwMjvmZENuw0dqbVo=

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "http://npm.htsc/http-deceiver/download/http-deceiver-1.2.7.tgz#fa7168944ab9a519d337cb0bec7284dc3e723d87"
  integrity sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=

http-errors@2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/http-errors/download/http-errors-2.0.0.tgz#b7774a1486ef73cf7667ac9ae0858c012c57b9d3"
  integrity sha1-t3dKFIbvc892Z6ya4IWMASxXudM=
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "http://npm.htsc/http-errors/download/http-errors-1.6.3.tgz#8b55680bb4be283a0b5bf4ea2e38580be1d9320d"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-parser-js@>=0.5.1:
  version "0.5.8"
  resolved "http://npm.htsc/http-parser-js/download/http-parser-js-0.5.8.tgz#af23090d9ac4e24573de6f6aecc9d84a48bf20e3"
  integrity sha1-ryMJDZrE4kVz3m9q7MnYSki/IOM=

http-proxy-agent@^5.0.0:
  version "5.0.0"
  resolved "http://npm.htsc/http-proxy-agent/download/http-proxy-agent-5.0.0.tgz#5129800203520d434f142bc78ff3c170800f2b43"
  integrity sha1-USmAAgNSDUNPFCvHj/PBcIAPK0M=
  dependencies:
    "@tootallnate/once" "2"
    agent-base "6"
    debug "4"

http-proxy-middleware@^2.0.3:
  version "2.0.6"
  resolved "http://npm.htsc/http-proxy-middleware/download/http-proxy-middleware-2.0.6.tgz#e1a4dd6979572c7ab5a4e4b55095d1f32a74963f"
  integrity sha1-4aTdaXlXLHq1pOS1UJXR8yp0lj8=
  dependencies:
    "@types/http-proxy" "^1.17.8"
    http-proxy "^1.18.1"
    is-glob "^4.0.1"
    is-plain-obj "^3.0.0"
    micromatch "^4.0.2"

http-proxy@^1.18.1:
  version "1.18.1"
  resolved "http://npm.htsc/http-proxy/download/http-proxy-1.18.1.tgz#401541f0534884bbf95260334e72f88ee3976549"
  integrity sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

http2-wrapper@^1.0.0-beta.5.2:
  version "1.0.3"
  resolved "http://npm.htsc/http2-wrapper/download/http2-wrapper-1.0.3.tgz#b8f55e0c1f25d4ebd08b3b0c2c079f9590800b3d"
  integrity sha1-uPVeDB8l1OvQizsMLAeflZCACz0=
  dependencies:
    quick-lru "^5.1.1"
    resolve-alpn "^1.0.0"

https-proxy-agent@^5.0.0:
  version "5.0.1"
  resolved "http://npm.htsc/https-proxy-agent/download/https-proxy-agent-5.0.1.tgz#c59ef224a04fe8b754f3db0063a25ea30d0005d6"
  integrity sha1-xZ7yJKBP6LdU89sAY6Jeow0ABdY=
  dependencies:
    agent-base "6"
    debug "4"

human-signals@^1.1.1:
  version "1.1.1"
  resolved "http://npm.htsc/human-signals/download/human-signals-1.1.1.tgz#c5b1cd14f50aeae09ab6c59fe63ba3395fe4dfa3"
  integrity sha1-xbHNFPUK6uCatsWf5jujOV/k36M=

human-signals@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/human-signals/download/human-signals-2.1.0.tgz#dc91fcba42e4d06e4abaed33b3e7a3c02f514ea0"
  integrity sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=

humanize-ms@^1.2.1:
  version "1.2.1"
  resolved "http://npm.htsc/humanize-ms/download/humanize-ms-1.2.1.tgz#c46e3159a293f6b896da29316d8b6fe8bb79bbed"
  integrity sha1-xG4xWaKT9riW2ikxbYtv6Lt5u+0=
  dependencies:
    ms "^2.0.0"

husky@7.0.4:
  version "7.0.4"
  resolved "http://npm.htsc/husky/download/husky-7.0.4.tgz#242048245dc49c8fb1bf0cc7cfb98dd722531535"
  integrity sha1-JCBIJF3EnI+xvwzHz7mN1yJTFTU=

iconv-lite@0.4.24:
  version "0.4.24"
  resolved "http://npm.htsc/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.2:
  version "0.6.3"
  resolved "http://npm.htsc/iconv-lite/download/iconv-lite-0.6.3.tgz#a52f80bf38da1952eb5c681790719871a1a72501"
  integrity sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

icss-utils@^5.0.0, icss-utils@^5.1.0:
  version "5.1.0"
  resolved "http://npm.htsc/icss-utils/download/icss-utils-5.1.0.tgz#c6be6858abd013d768e98366ae47e25d5887b1ae"
  integrity sha1-xr5oWKvQE9do6YNmrkfiXViHsa4=

ieee754@^1.1.13:
  version "1.2.1"
  resolved "http://npm.htsc/ieee754/download/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

ignore@^4.0.6:
  version "4.0.6"
  resolved "http://npm.htsc/ignore/download/ignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

ignore@^5.0.5, ignore@^5.1.1, ignore@^5.2.0, ignore@^5.2.1:
  version "5.2.4"
  resolved "http://npm.htsc/ignore/download/ignore-5.2.4.tgz#a291c0c6178ff1b960befe47fcdec301674a6324"
  integrity sha1-opHAxheP8blgvv5H/N7DAWdKYyQ=

image-size@^0.7.4:
  version "0.7.5"
  resolved "http://npm.htsc/image-size/download/image-size-0.7.5.tgz#269f357cf5797cb44683dfa99790e54c705ead04"
  integrity sha1-Jp81fPV5fLRGg9+pl5DlTHBerQQ=

import-fresh@^3.0.0, import-fresh@^3.2.1:
  version "3.3.0"
  resolved "http://npm.htsc/import-fresh/download/import-fresh-3.3.0.tgz#37162c25fcb9ebaa2e6e53d5b4d88ce17d9e0c2b"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-lazy@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/import-lazy/download/import-lazy-4.0.0.tgz#e8eb627483a0a43da3c03f3e35548be5cb0cc153"
  integrity sha1-6OtidIOgpD2jwD8+NVSL5csMwVM=

imul@^1.0.0:
  version "1.0.1"
  resolved "http://npm.htsc/imul/download/imul-1.0.1.tgz#9d5867161e8b3de96c2c38d5dc7cb102f35e2ac9"
  integrity sha1-nVhnFh6LPelsLDjV3HyxAvNeKsk=

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://npm.htsc/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/indent-string/download/indent-string-4.0.0.tgz#624f8f4497d619b2d9768531d58f4122854d7251"
  integrity sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://npm.htsc/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3:
  version "2.0.4"
  resolved "http://npm.htsc/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.3:
  version "2.0.3"
  resolved "http://npm.htsc/inherits/download/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

ini@^1.3.4, ini@^1.3.5:
  version "1.3.8"
  resolved "http://npm.htsc/ini/download/ini-1.3.8.tgz#a29da425b48806f34767a4efce397269af28432c"
  integrity sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=

internal-slot@^1.0.3, internal-slot@^1.0.5:
  version "1.0.5"
  resolved "http://npm.htsc/internal-slot/download/internal-slot-1.0.5.tgz#f2a2ee21f668f8627a4667f309dc0f4fb6674986"
  integrity sha1-8qLuIfZo+GJ6RmfzCdwPT7ZnSYY=
  dependencies:
    get-intrinsic "^1.2.0"
    has "^1.0.3"
    side-channel "^1.0.4"

interpret@^1.4.0:
  version "1.4.0"
  resolved "http://npm.htsc/interpret/download/interpret-1.4.0.tgz#665ab8bc4da27a774a40584e812e3e0fa45b1a1e"
  integrity sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=

interpret@^3.1.1:
  version "3.1.1"
  resolved "http://npm.htsc/interpret/download/interpret-3.1.1.tgz#5be0ceed67ca79c6c4bc5cf0d7ee843dcea110c4"
  integrity sha1-W+DO7WfKecbEvFzw1+6EPc6hEMQ=

ip@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/ip/download/ip-2.0.0.tgz#4cf4ab182fee2314c75ede1276f8c80b479936da"
  integrity sha1-TPSrGC/uIxTHXt4SdvjIC0eZNto=

ip@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.htsc/ip/-/ip-2.0.1.tgz#e8f3595d33a3ea66490204234b77636965307105"
  integrity sha512-lJUL9imLTNi1ZfXT+DU6rBBdbiKGBuay9B6xGSPVjUeQwaH1RIGqef8RZkUtHioLmSNpPR5M4HVKJGm1j8FWVQ==

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "http://npm.htsc/ipaddr.js/download/ipaddr.js-1.9.1.tgz#bff38543eeb8984825079ff3a2a8e6cbd46781b3"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

ipaddr.js@^2.0.1:
  version "2.1.0"
  resolved "http://npm.htsc/ipaddr.js/download/ipaddr.js-2.1.0.tgz#2119bc447ff8c257753b196fc5f1ce08a4cdf39f"
  integrity sha1-IRm8RH/4wld1OxlvxfHOCKTN858=

is-alphabetical@^1.0.0:
  version "1.0.4"
  resolved "http://npm.htsc/is-alphabetical/download/is-alphabetical-1.0.4.tgz#9e7d6b94916be22153745d184c298cbf986a686d"
  integrity sha1-nn1rlJFr4iFTdF0YTCmMv5hqaG0=

is-alphanumerical@^1.0.0:
  version "1.0.4"
  resolved "http://npm.htsc/is-alphanumerical/download/is-alphanumerical-1.0.4.tgz#7eb9a2431f855f6b1ef1a78e326df515696c4dbf"
  integrity sha1-frmiQx+FX2se8aeOMm31FWlsTb8=
  dependencies:
    is-alphabetical "^1.0.0"
    is-decimal "^1.0.0"

is-array-buffer@^3.0.1, is-array-buffer@^3.0.2:
  version "3.0.2"
  resolved "http://npm.htsc/is-array-buffer/download/is-array-buffer-3.0.2.tgz#f2653ced8412081638ecb0ebbd0c41c6e0aecbbe"
  integrity sha1-8mU87YQSCBY47LDrvQxBxuCuy74=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.0"
    is-typed-array "^1.1.10"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://npm.htsc/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-bigint@^1.0.1:
  version "1.0.4"
  resolved "http://npm.htsc/is-bigint/download/is-bigint-1.0.4.tgz#08147a1875bc2b32005d41ccd8291dffc6691df3"
  integrity sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM=
  dependencies:
    has-bigints "^1.0.1"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/is-binary-path/download/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.1.0:
  version "1.1.2"
  resolved "http://npm.htsc/is-boolean-object/download/is-boolean-object-1.1.2.tgz#5c6dc200246dd9321ae4b885a114bb1f75f63719"
  integrity sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-callable@^1.1.3, is-callable@^1.1.4, is-callable@^1.2.7:
  version "1.2.7"
  resolved "http://npm.htsc/is-callable/download/is-callable-1.2.7.tgz#3bc2a85ea742d9e36205dcacdd72ca1fdc51b055"
  integrity sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=

is-ci@^1.0.10:
  version "1.2.1"
  resolved "http://npm.htsc/is-ci/download/is-ci-1.2.1.tgz#e3779c8ee17fccf428488f6e281187f2e632841c"
  integrity sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw=
  dependencies:
    ci-info "^1.5.0"

is-core-module@^2.11.0:
  version "2.12.0"
  resolved "http://npm.htsc/is-core-module/download/is-core-module-2.12.0.tgz#36ad62f6f73c8253fd6472517a12483cf03e7ec4"
  integrity sha1-Nq1i9vc8glP9ZHJRehJIPPA+fsQ=
  dependencies:
    has "^1.0.3"

is-core-module@^2.13.0:
  version "2.13.1"
  resolved "http://npm.htsc/is-core-module/download/is-core-module-2.13.1.tgz#ad0d7532c6fea9da1ebdc82742d74525c6273384"
  integrity sha1-rQ11Msb+qdoevcgnQtdFJcYnM4Q=
  dependencies:
    hasown "^2.0.0"

is-core-module@^2.5.0, is-core-module@^2.7.0, is-core-module@^2.9.0:
  version "2.12.1"
  resolved "http://npm.htsc/is-core-module/download/is-core-module-2.12.1.tgz#0c0b6885b6f80011c71541ce15c8d66cf5a4f9fd"
  integrity sha1-DAtohbb4ABHHFUHOFcjWbPWk+f0=
  dependencies:
    has "^1.0.3"

is-date-object@^1.0.1:
  version "1.0.5"
  resolved "http://npm.htsc/is-date-object/download/is-date-object-1.0.5.tgz#0841d5536e724c25597bf6ea62e1bd38298df31f"
  integrity sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8=
  dependencies:
    has-tostringtag "^1.0.0"

is-decimal@^1.0.0:
  version "1.0.4"
  resolved "http://npm.htsc/is-decimal/download/is-decimal-1.0.4.tgz#65a3a5958a1c5b63a706e1b333d7cd9f630d3fa5"
  integrity sha1-ZaOllYocW2OnBuGzM9fNn2MNP6U=

is-docker@^2.0.0, is-docker@^2.1.1:
  version "2.2.1"
  resolved "http://npm.htsc/is-docker/download/is-docker-2.2.1.tgz#33eeabe23cfe86f14bde4408a02c0cfb853acdaa"
  integrity sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://npm.htsc/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "http://npm.htsc/is-glob/download/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-hexadecimal@^1.0.0:
  version "1.0.4"
  resolved "http://npm.htsc/is-hexadecimal/download/is-hexadecimal-1.0.4.tgz#cc35c97588da4bd49a8eedd6bc4082d44dcb23a7"
  integrity sha1-zDXJdYjaS9Saju3WvECC1E3LI6c=

is-interactive@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/is-interactive/download/is-interactive-1.0.0.tgz#cea6e6ae5c870a7b0a0004070b7b587e0252912e"
  integrity sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4=

is-lambda@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/is-lambda/download/is-lambda-1.0.1.tgz#3d9877899e6a53efc0160504cde15f82e6f061d5"
  integrity sha1-PZh3iZ5qU+/AFgUEzeFfgubwYdU=

is-my-ip-valid@^1.0.0:
  version "1.0.1"
  resolved "http://npm.htsc/is-my-ip-valid/download/is-my-ip-valid-1.0.1.tgz#f7220d1146257c98672e6fba097a9f3f2d348442"
  integrity sha1-9yINEUYlfJhnLm+6CXqfPy00hEI=

is-my-json-valid@^2.20.0:
  version "2.20.6"
  resolved "http://npm.htsc/is-my-json-valid/download/is-my-json-valid-2.20.6.tgz#a9d89e56a36493c77bda1440d69ae0dc46a08387"
  integrity sha1-qdieVqNkk8d72hRA1prg3Eagg4c=
  dependencies:
    generate-function "^2.0.0"
    generate-object-property "^1.1.0"
    is-my-ip-valid "^1.0.0"
    jsonpointer "^5.0.0"
    xtend "^4.0.0"

is-negative-zero@^2.0.2:
  version "2.0.2"
  resolved "http://npm.htsc/is-negative-zero/download/is-negative-zero-2.0.2.tgz#7bf6f03a28003b8b3965de3ac26f664d765f3150"
  integrity sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA=

is-number-object@^1.0.4:
  version "1.0.7"
  resolved "http://npm.htsc/is-number-object/download/is-number-object-1.0.7.tgz#59d50ada4c45251784e9904f5246c742f07a42fc"
  integrity sha1-WdUK2kxFJReE6ZBPUkbHQvB6Qvw=
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://npm.htsc/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-obj@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/is-obj/download/is-obj-1.0.1.tgz#3e4729ac1f5fde025cd7d83a896dab9f4f67db0f"
  integrity sha1-PkcprB9f3gJc19g6iW2rn09n2w8=

is-plain-obj@^1.1.0:
  version "1.1.0"
  resolved "http://npm.htsc/is-plain-obj/download/is-plain-obj-1.1.0.tgz#71a50c8429dfca773c92a390a4a03b39fcd51d3e"
  integrity sha1-caUMhCnfync8kqOQpKA7OfzVHT4=

is-plain-obj@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/is-plain-obj/download/is-plain-obj-3.0.0.tgz#af6f2ea14ac5a646183a5bbdb5baabbc156ad9d7"
  integrity sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc=

is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "http://npm.htsc/is-plain-object/download/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-plain-object@^5.0.0:
  version "5.0.0"
  resolved "http://npm.htsc/is-plain-object/download/is-plain-object-5.0.0.tgz#4427f50ab3429e9025ea7d52e9043a9ef4159344"
  integrity sha1-RCf1CrNCnpAl6n1S6QQ6nvQVk0Q=

is-property@^1.0.0, is-property@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/is-property/download/is-property-1.0.2.tgz#57fe1c4e48474edd65b09911f26b1cd4095dda84"
  integrity sha1-V/4cTkhHTt1lsJkR8msc1Ald2oQ=

is-regex@^1.1.4:
  version "1.1.4"
  resolved "http://npm.htsc/is-regex/download/is-regex-1.1.4.tgz#eef5663cd59fa4c0ae339505323df6854bb15958"
  integrity sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-regexp@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/is-regexp/download/is-regexp-1.0.0.tgz#fd2d883545c46bac5a633e7b9a09e87fa2cb5069"
  integrity sha1-/S2INUXEa6xaYz57mgnof6LLUGk=

is-shared-array-buffer@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/is-shared-array-buffer/download/is-shared-array-buffer-1.0.2.tgz#8f259c573b60b6a32d4058a1a07430c0a7344c79"
  integrity sha1-jyWcVztgtqMtQFihoHQwwKc0THk=
  dependencies:
    call-bind "^1.0.2"

is-stream@^1.1.0:
  version "1.1.0"
  resolved "http://npm.htsc/is-stream/download/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-stream@^2.0.0:
  version "2.0.1"
  resolved "http://npm.htsc/is-stream/download/is-stream-2.0.1.tgz#fac1e3d53b97ad5a9d0ae9cef2389f5810a5c077"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  resolved "http://npm.htsc/is-string/download/is-string-1.0.7.tgz#0dd12bf2006f255bb58f695110eff7491eebc0fd"
  integrity sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0=
  dependencies:
    has-tostringtag "^1.0.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  resolved "http://npm.htsc/is-symbol/download/is-symbol-1.0.4.tgz#a6dac93b635b063ca6872236de88910a57af139c"
  integrity sha1-ptrJO2NbBjymhyI23oiRClevE5w=
  dependencies:
    has-symbols "^1.0.2"

is-typed-array@^1.1.10, is-typed-array@^1.1.9:
  version "1.1.10"
  resolved "http://npm.htsc/is-typed-array/download/is-typed-array-1.1.10.tgz#36a5b5cb4189b575d1a3e4b08536bfb485801e3f"
  integrity sha1-NqW1y0GJtXXRo+SwhTa/tIWAHj8=
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.0"

is-unicode-supported@^0.1.0:
  version "0.1.0"
  resolved "http://npm.htsc/is-unicode-supported/download/is-unicode-supported-0.1.0.tgz#3f26c76a809593b52bfa2ecb5710ed2779b522a7"
  integrity sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc=

is-weakref@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/is-weakref/download/is-weakref-1.0.2.tgz#9529f383a9338205e89765e0392efc2f100f06f2"
  integrity sha1-lSnzg6kzggXol2XgOS78LxAPBvI=
  dependencies:
    call-bind "^1.0.2"

is-windows@^1.0.1:
  version "1.0.2"
  resolved "http://npm.htsc/is-windows/download/is-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^2.2.0:
  version "2.2.0"
  resolved "http://npm.htsc/is-wsl/download/is-wsl-2.2.0.tgz#74a4c76e77ca9fd3f932f290c17ea326cd157271"
  integrity sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=
  dependencies:
    is-docker "^2.0.0"

isarray@~1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isbinaryfile@^4.0.8:
  version "4.0.10"
  resolved "http://npm.htsc/isbinaryfile/download/isbinaryfile-4.0.10.tgz#0c5b5e30c2557a2f06febd37b7322946aaee42b3"
  integrity sha1-DFteMMJVei8G/r03tzIpRqruQrM=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^3.0.1:
  version "3.0.1"
  resolved "http://npm.htsc/isobject/download/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

jackspeak@^2.0.3:
  version "2.2.1"
  resolved "http://npm.htsc/jackspeak/download/jackspeak-2.2.1.tgz#655e8cf025d872c9c03d3eb63e8f0c024fef16a6"
  integrity sha1-ZV6M8CXYcsnAPT62Po8MAk/vFqY=
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jest-worker@^27.4.5:
  version "27.5.1"
  resolved "http://npm.htsc/jest-worker/download/jest-worker-27.5.1.tgz#8d146f0900e8973b106b6f73cc1e9a8cb86f8db0"
  integrity sha1-jRRvCQDolzsQa29zzB6ajLhvjbA=
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

joi@^17.7.0:
  version "17.9.2"
  resolved "http://npm.htsc/joi/download/joi-17.9.2.tgz#8b2e4724188369f55451aebd1d0b1d9482470690"
  integrity sha1-iy5HJBiDafVUUa69HQsdlIJHBpA=
  dependencies:
    "@hapi/hoek" "^9.0.0"
    "@hapi/topo" "^5.0.0"
    "@sideway/address" "^4.1.3"
    "@sideway/formula" "^3.0.1"
    "@sideway/pinpoint" "^2.0.0"

js-md5@^0.7.3:
  version "0.7.3"
  resolved "http://npm.htsc/js-md5/download/js-md5-0.7.3.tgz#b4f2fbb0b327455f598d6727e38ec272cd09c3f2"
  integrity sha1-tPL7sLMnRV9ZjWcn447Ccs0Jw/I=

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-tokens@^8.0.0:
  version "8.0.1"
  resolved "http://npm.htsc/js-tokens/download/js-tokens-8.0.1.tgz#f068fde9bd2f9f4a24ad78f3b4fa787216b433e3"
  integrity sha1-8Gj96b0vn0okrXjztPp4cha0M+M=

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "http://npm.htsc/js-yaml/download/js-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "http://npm.htsc/js-yaml/download/js-yaml-4.1.0.tgz#c1fb65f8f5017901cdd2c951864ba18458a10602"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

jsesc@^2.5.1:
  version "2.5.2"
  resolved "http://npm.htsc/jsesc/download/jsesc-2.5.2.tgz#80564d2e483dacf6e8ef209650a67df3f0c283a4"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

jsesc@~0.5.0:
  version "0.5.0"
  resolved "http://npm.htsc/jsesc/download/jsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-buffer@3.0.1:
  version "3.0.1"
  resolved "http://npm.htsc/json-buffer/download/json-buffer-3.0.1.tgz#9338802a30d3b6605fbe0613e094008ca8c05a13"
  integrity sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=

json-parse-even-better-errors@^2.3.0, json-parse-even-better-errors@^2.3.1:
  version "2.3.1"
  resolved "http://npm.htsc/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz#7c47805a94319928e05777405dc12e1f7a4ee02d"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://npm.htsc/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz#ae7bcb3656ab77a73ba5c49bf654f38e6b6860e2"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stringify-safe@^5.0.1:
  version "5.0.1"
  resolved "http://npm.htsc/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json5@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/json5/download/json5-1.0.2.tgz#63d98d60f21b313b77c4d6da18bfa69d80e1d593"
  integrity sha1-Y9mNYPIbMTt3xNbaGL+mnYDh1ZM=
  dependencies:
    minimist "^1.2.0"

json5@^2.1.2, json5@^2.2.3:
  version "2.2.3"
  resolved "http://npm.htsc/json5/download/json5-2.2.3.tgz#78cd6f1a19bdc12b73db5ad0c61efd66c1e29283"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/jsonfile/download/jsonfile-4.0.0.tgz#8771aae0799b64076b76640fca058f9c10e33ecb"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "http://npm.htsc/jsonfile/download/jsonfile-6.1.0.tgz#bc55b2634793c679ec6403094eb13698a6ec0aae"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonpointer@^5.0.0:
  version "5.0.1"
  resolved "http://npm.htsc/jsonpointer/download/jsonpointer-5.0.1.tgz#2110e0af0900fd37467b5907ecd13a7884a1b559"
  integrity sha1-IRDgrwkA/TdGe1kH7NE6eIShtVk=

"jsx-ast-utils@^2.4.1 || ^3.0.0", jsx-ast-utils@^3.3.3:
  version "3.3.4"
  resolved "http://npm.htsc/jsx-ast-utils/download/jsx-ast-utils-3.3.4.tgz#b896535fed5b867650acce5a9bd4135ffc7b3bf9"
  integrity sha1-uJZTX+1bhnZQrM5am9QTX/x7O/k=
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    object.assign "^4.1.4"
    object.values "^1.1.6"

junk@^3.1.0:
  version "3.1.0"
  resolved "http://npm.htsc/junk/download/junk-3.1.0.tgz#31499098d902b7e98c5d9b9c80f43457a88abfa1"
  integrity sha1-MUmQmNkCt+mMXZucgPQ0V6iKv6E=

keyv@^4.0.0:
  version "4.5.2"
  resolved "http://npm.htsc/keyv/download/keyv-4.5.2.tgz#0e310ce73bf7851ec702f2eaf46ec4e3805cce56"
  integrity sha1-DjEM5zv3hR7HAvLq9G7E44BczlY=
  dependencies:
    json-buffer "3.0.1"

kind-of@^6.0.2, kind-of@^6.0.3:
  version "6.0.3"
  resolved "http://npm.htsc/kind-of/download/kind-of-6.0.3.tgz#07c05034a6c349fa06e24fa35aa76db4580ce4dd"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

klaw@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.htsc/klaw/-/klaw-4.1.0.tgz#5df608067d8cb62bbfb24374f8e5d956323338f3"
  integrity sha512-1zGZ9MF9H22UnkpVeuaGKOjfA2t6WrfdrJmGjy16ykcjnKQDmHVX+KI477rpbGevz/5FD4MC3xf1oxylBgcaQw==

known-css-properties@^0.26.0:
  version "0.26.0"
  resolved "http://npm.htsc/known-css-properties/download/known-css-properties-0.26.0.tgz#008295115abddc045a9f4ed7e2a84dc8b3a77649"
  integrity sha1-AIKVEVq93ARan07X4qhNyLOndkk=

koffi@2.8.0:
  version "2.8.0"
  resolved "http://registry.npm.htsc/koffi/-/koffi-2.8.0.tgz#26cb3a608ef8ce4684ff6ed7096fffeec21da7cb"
  integrity sha512-EXhiH9Ya4f+o4+24+uV4vFAMyPEskARVUaY8VHbIYWqkQVPTDyYJCBNfxp0Kxw6WdhaMwXeR8xIUyz8R2H8Rew==

language-subtag-registry@~0.3.2:
  version "0.3.22"
  resolved "http://npm.htsc/language-subtag-registry/download/language-subtag-registry-0.3.22.tgz#2e1500861b2e457eba7e7ae86877cbd08fa1fd1d"
  integrity sha1-LhUAhhsuRX66fnroaHfL0I+h/R0=

language-tags@=1.0.5:
  version "1.0.5"
  resolved "http://npm.htsc/language-tags/download/language-tags-1.0.5.tgz#d321dbc4da30ba8bf3024e040fa5c14661f9193a"
  integrity sha1-0yHbxNowuovzAk4ED6XBRmH5GTo=
  dependencies:
    language-subtag-registry "~0.3.2"

launch-editor@^2.6.0:
  version "2.6.0"
  resolved "http://npm.htsc/launch-editor/download/launch-editor-2.6.0.tgz#4c0c1a6ac126c572bd9ff9a30da1d2cae66defd7"
  integrity sha1-TAwaasEmxXK9n/mjDaHSyuZt79c=
  dependencies:
    picocolors "^1.0.0"
    shell-quote "^1.7.3"

lazy-val@^1.0.5:
  version "1.0.5"
  resolved "http://npm.htsc/lazy-val/download/lazy-val-1.0.5.tgz#6cf3b9f5bc31cee7ee3e369c0832b7583dcd923d"
  integrity sha1-bPO59bwxzufuPjacCDK3WD3Nkj0=

levn@^0.4.1:
  version "0.4.1"
  resolved "http://npm.htsc/levn/download/levn-0.4.1.tgz#ae4562c007473b932a6200d403268dd2fffc6ade"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "http://npm.htsc/lines-and-columns/download/lines-and-columns-1.2.4.tgz#eca284f75d2965079309dc0ad9255abb2ebc1632"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

lint-staged@^10.0.7:
  version "10.5.4"
  resolved "http://npm.htsc/lint-staged/download/lint-staged-10.5.4.tgz#cd153b5f0987d2371fc1d2847a409a2fe705b665"
  integrity sha1-zRU7XwmH0jcfwdKEekCaL+cFtmU=
  dependencies:
    chalk "^4.1.0"
    cli-truncate "^2.1.0"
    commander "^6.2.0"
    cosmiconfig "^7.0.0"
    debug "^4.2.0"
    dedent "^0.7.0"
    enquirer "^2.3.6"
    execa "^4.1.0"
    listr2 "^3.2.2"
    log-symbols "^4.0.0"
    micromatch "^4.0.2"
    normalize-path "^3.0.0"
    please-upgrade-node "^3.2.0"
    string-argv "0.3.1"
    stringify-object "^3.3.0"

listr2@^3.2.2:
  version "3.14.0"
  resolved "http://npm.htsc/listr2/download/listr2-3.14.0.tgz#23101cc62e1375fd5836b248276d1d2b51fdbe9e"
  integrity sha1-IxAcxi4Tdf1YNrJIJ20dK1H9vp4=
  dependencies:
    cli-truncate "^2.1.0"
    colorette "^2.0.16"
    log-update "^4.0.0"
    p-map "^4.0.0"
    rfdc "^1.3.0"
    rxjs "^7.5.1"
    through "^2.3.8"
    wrap-ansi "^7.0.0"

listr2@^5.0.3:
  version "5.0.8"
  resolved "http://npm.htsc/listr2/download/listr2-5.0.8.tgz#a9379ffeb4bd83a68931a65fb223a11510d6ba23"
  integrity sha1-qTef/rS9g6aJMaZfsiOhFRDWuiM=
  dependencies:
    cli-truncate "^2.1.0"
    colorette "^2.0.19"
    log-update "^4.0.0"
    p-map "^4.0.0"
    rfdc "^1.3.0"
    rxjs "^7.8.0"
    through "^2.3.8"
    wrap-ansi "^7.0.0"

load-json-file@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/load-json-file/download/load-json-file-2.0.0.tgz#7947e42149af80d696cbf797bcaabcfe1fe29ca8"
  integrity sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    strip-bom "^3.0.0"

loader-runner@^4.2.0:
  version "4.3.0"
  resolved "http://npm.htsc/loader-runner/download/loader-runner-4.3.0.tgz#c1b4a163b99f614830353b16755e7149ac2314e1"
  integrity sha1-wbShY7mfYUgwNTsWdV5xSawjFOE=

loader-utils@^2.0.0:
  version "2.0.4"
  resolved "http://npm.htsc/loader-utils/download/loader-utils-2.0.4.tgz#8b5cb38b5c34a9a018ee1fc0e6a066d1dfcc528c"
  integrity sha1-i1yzi1w0qaAY7h/A5qBm0d/MUow=
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/locate-path/download/locate-path-2.0.0.tgz#2b568b265eec944c6d9c0de9c3dbbbca0354cd8e"
  integrity sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "http://npm.htsc/locate-path/download/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "http://npm.htsc/locate-path/download/locate-path-6.0.0.tgz#55321eb309febbc59c4801d931a72452a681d286"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash._reinterpolate@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/lodash._reinterpolate/download/lodash._reinterpolate-3.0.0.tgz#0ccf2d89166af03b3663c796538b75ac6e114d9d"
  integrity sha1-DM8tiRZq8Ds2Y8eWU4t1rG4RTZ0=

lodash.camelcase@4.3.0:
  version "4.3.0"
  resolved "http://npm.htsc/lodash.camelcase/download/lodash.camelcase-4.3.0.tgz#b28aa6288a2b9fc651035c7711f65ab6190331a6"
  integrity sha1-soqmKIorn8ZRA1x3EfZathkDMaY=

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "http://npm.htsc/lodash.debounce/download/lodash.debounce-4.0.8.tgz#82d79bff30a67c4005ffd5e2515300ad9ca4d7af"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.escaperegexp@^4.1.2:
  version "4.1.2"
  resolved "http://npm.htsc/lodash.escaperegexp/download/lodash.escaperegexp-4.1.2.tgz#64762c48618082518ac3df4ccf5d5886dae20347"
  integrity sha1-ZHYsSGGAglGKw99Mz11YhtriA0c=

lodash.get@^4.0.0:
  version "4.4.2"
  resolved "http://npm.htsc/lodash.get/download/lodash.get-4.4.2.tgz#2d177f652fa31e939b4438d5341499dfa3825e99"
  integrity sha1-LRd/ZS+jHpObRDjVNBSZ36OCXpk=

lodash.isequal@^4.5.0:
  version "4.5.0"
  resolved "http://npm.htsc/lodash.isequal/download/lodash.isequal-4.5.0.tgz#415c4478f2bcc30120c22ce10ed3226f7d3e18e0"
  integrity sha1-QVxEePK8wwEgwizhDtMib30+GOA=

lodash.kebabcase@4.1.1:
  version "4.1.1"
  resolved "http://npm.htsc/lodash.kebabcase/download/lodash.kebabcase-4.1.1.tgz#8489b1cb0d29ff88195cceca448ff6d6cc295c36"
  integrity sha1-hImxyw0p/4gZXM7KRI/21swpXDY=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "http://npm.htsc/lodash.merge/download/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.snakecase@4.1.1:
  version "4.1.1"
  resolved "http://npm.htsc/lodash.snakecase/download/lodash.snakecase-4.1.1.tgz#39d714a35357147837aefd64b5dcbb16becd8f8d"
  integrity sha1-OdcUo1NXFHg3rv1ktdy7Fr7Nj40=

lodash.template@^4.2.2:
  version "4.5.0"
  resolved "http://npm.htsc/lodash.template/download/lodash.template-4.5.0.tgz#f976195cf3f347d0d5f52483569fe8031ccce8ab"
  integrity sha1-+XYZXPPzR9DV9SSDVp/oAxzM6Ks=
  dependencies:
    lodash._reinterpolate "^3.0.0"
    lodash.templatesettings "^4.0.0"

lodash.templatesettings@^4.0.0:
  version "4.2.0"
  resolved "http://npm.htsc/lodash.templatesettings/download/lodash.templatesettings-4.2.0.tgz#e481310f049d3cf6d47e912ad09313b154f0fb33"
  integrity sha1-5IExDwSdPPbUfpEq0JMTsVTw+zM=
  dependencies:
    lodash._reinterpolate "^3.0.0"

lodash.truncate@^4.4.2:
  version "4.4.2"
  resolved "http://npm.htsc/lodash.truncate/download/lodash.truncate-4.4.2.tgz#5a350da0b1113b837ecfffd5812cbe58d6eae193"
  integrity sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM=

lodash.upperfirst@4.3.1:
  version "4.3.1"
  resolved "http://npm.htsc/lodash.upperfirst/download/lodash.upperfirst-4.3.1.tgz#1365edf431480481ef0d1c68957a5ed99d49f7ce"
  integrity sha1-E2Xt9DFIBIHvDRxolXpe2Z1J984=

lodash@^4.17.15, lodash@^4.17.20, lodash@^4.17.21, lodash@^4.17.4:
  version "4.17.21"
  resolved "http://npm.htsc/lodash/download/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

log-symbols@^4.0.0, log-symbols@^4.1.0:
  version "4.1.0"
  resolved "http://npm.htsc/log-symbols/download/log-symbols-4.1.0.tgz#3fbdbb95b4683ac9fc785111e792e558d4abd503"
  integrity sha1-P727lbRoOsn8eFER55LlWNSr1QM=
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

log-update@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/log-update/download/log-update-4.0.0.tgz#589ecd352471f2a1c0c570287543a64dfd20e0a1"
  integrity sha1-WJ7NNSRx8qHAxXAodUOmTf0g4KE=
  dependencies:
    ansi-escapes "^4.3.0"
    cli-cursor "^3.1.0"
    slice-ansi "^4.0.0"
    wrap-ansi "^6.2.0"

loose-envify@^1.4.0:
  version "1.4.0"
  resolved "http://npm.htsc/loose-envify/download/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lowdb@^6.0.1:
  version "6.0.1"
  resolved "http://npm.htsc/lowdb/download/lowdb-6.0.1.tgz#2c84ae74340fa81ace9c8f17b2fd7fbf9544e7d4"
  integrity sha1-LISudDQPqBrOnI8Xsv1/v5VE59Q=
  dependencies:
    steno "^3.0.0"

lower-case@^2.0.2:
  version "2.0.2"
  resolved "http://npm.htsc/lower-case/download/lower-case-2.0.2.tgz#6fa237c63dbdc4a82ca0fd882e4722dc5e634e28"
  integrity sha1-b6I3xj29xKgsoP2ILkci3F5jTig=
  dependencies:
    tslib "^2.0.3"

lowercase-keys@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/lowercase-keys/download/lowercase-keys-2.0.0.tgz#2603e78b7b4b0006cbca2fbcc8a3202558ac9479"
  integrity sha1-JgPni3tLAAbLyi+8yKMgJVislHk=

lru-cache@^4.0.1:
  version "4.1.5"
  resolved "http://npm.htsc/lru-cache/download/lru-cache-4.1.5.tgz#8bbe50ea85bed59bc9e33dcab8235ee9bcf443cd"
  integrity sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "http://npm.htsc/lru-cache/download/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "http://npm.htsc/lru-cache/download/lru-cache-6.0.0.tgz#6d6fe6570ebd96aaf90fcad1dafa3b2566db3a94"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

lru-cache@^7.7.1:
  version "7.18.3"
  resolved "http://npm.htsc/lru-cache/download/lru-cache-7.18.3.tgz#f793896e0fd0e954a59dfdd82f0773808df6aa89"
  integrity sha1-95OJbg/Q6VSlnf3YLwdzgI32qok=

"lru-cache@^9.1.1 || ^10.0.0":
  version "10.0.0"
  resolved "http://npm.htsc/lru-cache/download/lru-cache-10.0.0.tgz#b9e2a6a72a129d81ab317202d93c7691df727e61"
  integrity sha1-ueKmpyoSnYGrMXIC2Tx2kd9yfmE=

macos-alias@~0.2.5:
  version "0.2.11"
  resolved "http://npm.htsc/macos-alias/download/macos-alias-0.2.11.tgz#feeea6c13ba119814a43fc43c470b31e59ef718a"
  integrity sha1-/u6mwTuhGYFKQ/xDxHCzHlnvcYo=
  dependencies:
    nan "^2.4.0"

make-error@^1.1.1:
  version "1.3.6"
  resolved "http://npm.htsc/make-error/download/make-error-1.3.6.tgz#2eb2e37ea9b67c4891f684a1394799af484cf7a2"
  integrity sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=

make-fetch-happen@^11.0.3:
  version "11.1.1"
  resolved "http://npm.htsc/make-fetch-happen/download/make-fetch-happen-11.1.1.tgz#85ceb98079584a9523d4bf71d32996e7e208549f"
  integrity sha1-hc65gHlYSpUj1L9x0ymW5+IIVJ8=
  dependencies:
    agentkeepalive "^4.2.1"
    cacache "^17.0.0"
    http-cache-semantics "^4.1.1"
    http-proxy-agent "^5.0.0"
    https-proxy-agent "^5.0.0"
    is-lambda "^1.0.1"
    lru-cache "^7.7.1"
    minipass "^5.0.0"
    minipass-fetch "^3.0.0"
    minipass-flush "^1.0.5"
    minipass-pipeline "^1.2.4"
    negotiator "^0.6.3"
    promise-retry "^2.0.1"
    socks-proxy-agent "^7.0.0"
    ssri "^10.0.0"

map-age-cleaner@^0.1.1:
  version "0.1.3"
  resolved "http://npm.htsc/map-age-cleaner/download/map-age-cleaner-0.1.3.tgz#7d583a7306434c055fe474b0f45078e6e1b4b92a"
  integrity sha1-fVg6cwZDTAVf5HSw9FB45uG0uSo=
  dependencies:
    p-defer "^1.0.0"

map-obj@^1.0.0:
  version "1.0.1"
  resolved "http://npm.htsc/map-obj/download/map-obj-1.0.1.tgz#d933ceb9205d82bdcf4886f6742bdc2b4dea146d"
  integrity sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=

map-obj@^4.0.0:
  version "4.3.0"
  resolved "http://npm.htsc/map-obj/download/map-obj-4.3.0.tgz#9304f906e93faae70880da102a9f1df0ea8bb05a"
  integrity sha1-kwT5Buk/qucIgNoQKp8d8OqLsFo=

matcher@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/matcher/download/matcher-3.0.0.tgz#bd9060f4c5b70aa8041ccc6f80368760994f30ca"
  integrity sha1-vZBg9MW3CqgEHMxvgDaHYJlPMMo=
  dependencies:
    escape-string-regexp "^4.0.0"

mathml-tag-names@^2.1.3:
  version "2.1.3"
  resolved "http://npm.htsc/mathml-tag-names/download/mathml-tag-names-2.1.3.tgz#4ddadd67308e780cf16a47685878ee27b736a0a3"
  integrity sha1-TdrdZzCOeAzxakdoWHjuJ7c2oKM=

mdast-util-from-markdown@^0.8.5:
  version "0.8.5"
  resolved "http://npm.htsc/mdast-util-from-markdown/download/mdast-util-from-markdown-0.8.5.tgz#d1ef2ca42bc377ecb0463a987910dae89bd9a28c"
  integrity sha1-0e8spCvDd+ywRjqYeRDa6JvZoow=
  dependencies:
    "@types/mdast" "^3.0.0"
    mdast-util-to-string "^2.0.0"
    micromark "~2.11.0"
    parse-entities "^2.0.0"
    unist-util-stringify-position "^2.0.0"

mdast-util-to-string@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/mdast-util-to-string/download/mdast-util-to-string-2.0.0.tgz#b8cfe6a713e1091cb5b728fc48885a4767f8b97b"
  integrity sha1-uM/mpxPhCRy1tyj8SIhaR2f4uXs=

media-typer@0.3.0:
  version "0.3.0"
  resolved "http://npm.htsc/media-typer/download/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

mem@^4.3.0:
  version "4.3.0"
  resolved "http://npm.htsc/mem/download/mem-4.3.0.tgz#461af497bc4ae09608cdb2e60eefb69bff744178"
  integrity sha1-Rhr0l7xK4JYIzbLmDu+2m/90QXg=
  dependencies:
    map-age-cleaner "^0.1.1"
    mimic-fn "^2.0.0"
    p-is-promise "^2.0.0"

memfs@^3.4.1, memfs@^3.4.3:
  version "3.6.0"
  resolved "http://npm.htsc/memfs/download/memfs-3.6.0.tgz#d7a2110f86f79dd950a8b6df6d57bc984aa185f6"
  integrity sha1-16IRD4b3ndlQqLbfbVe8mEqhhfY=
  dependencies:
    fs-monkey "^1.0.4"

memory-fs@^0.2.0:
  version "0.2.0"
  resolved "http://npm.htsc/memory-fs/download/memory-fs-0.2.0.tgz#f2bb25368bc121e391c2520de92969caee0a0290"
  integrity sha1-8rslNovBIeORwlIN6Slpyu4KApA=

meow@^9.0.0:
  version "9.0.0"
  resolved "http://npm.htsc/meow/download/meow-9.0.0.tgz#cd9510bc5cac9dee7d03c73ee1f9ad959f4ea364"
  integrity sha1-zZUQvFysne59A8c+4fmtlZ9Oo2Q=
  dependencies:
    "@types/minimist" "^1.2.0"
    camelcase-keys "^6.2.2"
    decamelize "^1.2.0"
    decamelize-keys "^1.1.0"
    hard-rejection "^2.1.0"
    minimist-options "4.1.0"
    normalize-package-data "^3.0.0"
    read-pkg-up "^7.0.1"
    redent "^3.0.0"
    trim-newlines "^3.0.0"
    type-fest "^0.18.0"
    yargs-parser "^20.2.3"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/merge-descriptors/download/merge-descriptors-1.0.1.tgz#b00aaa556dd8b44568150ec9d1b953f3f90cbb61"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/merge-stream/download/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "http://npm.htsc/merge2/download/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

methods@~1.1.2:
  version "1.1.2"
  resolved "http://npm.htsc/methods/download/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromark@~2.11.0:
  version "2.11.4"
  resolved "http://npm.htsc/micromark/download/micromark-2.11.4.tgz#d13436138eea826383e822449c9a5c50ee44665a"
  integrity sha1-0TQ2E47qgmOD6CJEnJpcUO5EZlo=
  dependencies:
    debug "^4.0.0"
    parse-entities "^2.0.0"

micromatch@^4.0.0, micromatch@^4.0.2, micromatch@^4.0.4, micromatch@^4.0.5:
  version "4.0.5"
  resolved "http://npm.htsc/micromatch/download/micromatch-4.0.5.tgz#bc8999a7cbbf77cdc89f132f6e467051b49090c6"
  integrity sha1-vImZp8u/d83InxMvbkZwUbSQkMY=
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

mime-db@1.52.0, "mime-db@>= 1.43.0 < 2":
  version "1.52.0"
  resolved "http://npm.htsc/mime-db/download/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-types@^2.1.12, mime-types@^2.1.27, mime-types@^2.1.31, mime-types@~2.1.17, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "http://npm.htsc/mime-types/download/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mime@1.6.0:
  version "1.6.0"
  resolved "http://npm.htsc/mime/download/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mimic-fn@^2.0.0, mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/mimic-fn/download/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

mimic-response@^1.0.0:
  version "1.0.1"
  resolved "http://npm.htsc/mimic-response/download/mimic-response-1.0.1.tgz#4923538878eef42063cb8a3e3b0798781487ab1b"
  integrity sha1-SSNTiHju9CBjy4o+OweYeBSHqxs=

mimic-response@^3.1.0:
  version "3.1.0"
  resolved "http://npm.htsc/mimic-response/download/mimic-response-3.1.0.tgz#2d1d59af9c1b129815accc2c46a022a5ce1fa3c9"
  integrity sha1-LR1Zr5wbEpgVrMwsRqAipc4fo8k=

min-indent@^1.0.0:
  version "1.0.1"
  resolved "http://npm.htsc/min-indent/download/min-indent-1.0.1.tgz#a63f681673b30571fbe8bc25686ae746eefa9869"
  integrity sha1-pj9oFnOzBXH76LwlaGrnRu76mGk=

minimalistic-assert@^1.0.0:
  version "1.0.1"
  resolved "http://npm.htsc/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz#2e194de044626d4a10e7f7fbc00ce73e83e4d5c7"
  integrity sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=

minimatch@^3.0.4, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "http://npm.htsc/minimatch/download/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.1:
  version "9.0.2"
  resolved "http://npm.htsc/minimatch/download/minimatch-9.0.2.tgz#397e387fff22f6795844d00badc903a3d5de7057"
  integrity sha1-OX44f/8i9nlYRNALrckDo9XecFc=
  dependencies:
    brace-expansion "^2.0.1"

minimist-options@4.1.0:
  version "4.1.0"
  resolved "http://npm.htsc/minimist-options/download/minimist-options-4.1.0.tgz#c0655713c53a8a2ebd77ffa247d342c40f010619"
  integrity sha1-wGVXE8U6ii69d/+iR9NCxA8BBhk=
  dependencies:
    arrify "^1.0.1"
    is-plain-obj "^1.1.0"
    kind-of "^6.0.3"

minimist@^1.1.1, minimist@^1.1.3, minimist@^1.2.0, minimist@^1.2.6, minimist@^1.2.7, minimist@^1.2.8:
  version "1.2.8"
  resolved "http://npm.htsc/minimist/download/minimist-1.2.8.tgz#c1a464e7693302e082a075cee0c057741ac4772c"
  integrity sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=

minipass-collect@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/minipass-collect/download/minipass-collect-1.0.2.tgz#22b813bf745dc6edba2576b940022ad6edc8c617"
  integrity sha1-IrgTv3Rdxu26JXa5QAIq1u3Ixhc=
  dependencies:
    minipass "^3.0.0"

minipass-fetch@^3.0.0:
  version "3.0.3"
  resolved "http://npm.htsc/minipass-fetch/download/minipass-fetch-3.0.3.tgz#d9df70085609864331b533c960fd4ffaa78d15ce"
  integrity sha1-2d9wCFYJhkMxtTPJYP1P+qeNFc4=
  dependencies:
    minipass "^5.0.0"
    minipass-sized "^1.0.3"
    minizlib "^2.1.2"
  optionalDependencies:
    encoding "^0.1.13"

minipass-flush@^1.0.5:
  version "1.0.5"
  resolved "http://npm.htsc/minipass-flush/download/minipass-flush-1.0.5.tgz#82e7135d7e89a50ffe64610a787953c4c4cbb373"
  integrity sha1-gucTXX6JpQ/+ZGEKeHlTxMTLs3M=
  dependencies:
    minipass "^3.0.0"

minipass-pipeline@^1.2.4:
  version "1.2.4"
  resolved "http://npm.htsc/minipass-pipeline/download/minipass-pipeline-1.2.4.tgz#68472f79711c084657c067c5c6ad93cddea8214c"
  integrity sha1-aEcveXEcCEZXwGfFxq2Tzd6oIUw=
  dependencies:
    minipass "^3.0.0"

minipass-sized@^1.0.3:
  version "1.0.3"
  resolved "http://npm.htsc/minipass-sized/download/minipass-sized-1.0.3.tgz#70ee5a7c5052070afacfbc22977ea79def353b70"
  integrity sha1-cO5afFBSBwr6z7wil36nne81O3A=
  dependencies:
    minipass "^3.0.0"

minipass@^3.0.0:
  version "3.3.6"
  resolved "http://npm.htsc/minipass/download/minipass-3.3.6.tgz#7bba384db3a1520d18c9c0e5251c3444e95dd94a"
  integrity sha1-e7o4TbOhUg0YycDlJRw0ROld2Uo=
  dependencies:
    yallist "^4.0.0"

minipass@^5.0.0:
  version "5.0.0"
  resolved "http://npm.htsc/minipass/download/minipass-5.0.0.tgz#3e9788ffb90b694a5d0ec94479a45b5d8738133d"
  integrity sha1-PpeI/7kLaUpdDslEeaRbXYc4Ez0=

"minipass@^5.0.0 || ^6.0.2":
  version "6.0.2"
  resolved "http://npm.htsc/minipass/download/minipass-6.0.2.tgz#542844b6c4ce95b202c0995b0a471f1229de4c81"
  integrity sha1-VChEtsTOlbICwJlbCkcfEineTIE=

minizlib@^2.1.1, minizlib@^2.1.2:
  version "2.1.2"
  resolved "http://npm.htsc/minizlib/download/minizlib-2.1.2.tgz#e90d3466ba209b932451508a11ce3d3632145931"
  integrity sha1-6Q00Zrogm5MkUVCKEc49NjIUWTE=
  dependencies:
    minipass "^3.0.0"
    yallist "^4.0.0"

mkdirp@^0.5.1:
  version "0.5.6"
  resolved "http://npm.htsc/mkdirp/download/mkdirp-0.5.6.tgz#7def03d2432dcae4ba1d611445c48396062255f6"
  integrity sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=
  dependencies:
    minimist "^1.2.6"

mkdirp@^1.0.3:
  version "1.0.4"
  resolved "http://npm.htsc/mkdirp/download/mkdirp-1.0.4.tgz#3eb5ed62622756d79a5f0e2a221dfebad75c2f7e"
  integrity sha1-PrXtYmInVteaXw4qIh3+utdcL34=

moment@^2.29.4:
  version "2.29.4"
  resolved "http://npm.htsc/moment/download/moment-2.29.4.tgz#3dbe052889fe7c1b2ed966fcb3a77328964ef108"
  integrity sha1-Pb4FKIn+fBsu2Wb8s6dzKJZO8Qg=

ms@2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.2:
  version "2.1.2"
  resolved "http://npm.htsc/ms/download/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@2.1.3, ms@^2.0.0, ms@^2.1.1:
  version "2.1.3"
  resolved "http://npm.htsc/ms/download/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

multicast-dns@^7.2.5:
  version "7.2.5"
  resolved "http://npm.htsc/multicast-dns/download/multicast-dns-7.2.5.tgz#77eb46057f4d7adbd16d9290fa7299f6fa64cced"
  integrity sha1-d+tGBX9NetvRbZKQ+nKZ9vpkzO0=
  dependencies:
    dns-packet "^5.2.2"
    thunky "^1.0.2"

"murmur-32@^0.1.0 || ^0.2.0":
  version "0.2.0"
  resolved "http://npm.htsc/murmur-32/download/murmur-32-0.2.0.tgz#bf42b7567880db13cd92ca0c2c72eeea884f44c7"
  integrity sha1-v0K3VniA2xPNksoMLHLu6ohPRMc=
  dependencies:
    encode-utf8 "^1.0.3"
    fmix "^0.1.0"
    imul "^1.0.0"

nan@^2.4.0:
  version "2.17.0"
  resolved "http://npm.htsc/nan/download/nan-2.17.0.tgz#c0150a2368a182f033e9aa5195ec76ea41a199cb"
  integrity sha1-wBUKI2ihgvAz6apRlex26kGhmcs=

nanoid@^3.3.6:
  version "3.3.6"
  resolved "http://npm.htsc/nanoid/download/nanoid-3.3.6.tgz#443380c856d6e9f9824267d960b4236ad583ea4c"
  integrity sha1-RDOAyFbW6fmCQmfZYLQjatWD6kw=

natural-compare-lite@^1.4.0:
  version "1.4.0"
  resolved "http://npm.htsc/natural-compare-lite/download/natural-compare-lite-1.4.0.tgz#17b09581988979fddafe0201e931ba933c96cbb4"
  integrity sha1-F7CVgZiJef3a/gIB6TG6kzyWy7Q=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://npm.htsc/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@0.6.3, negotiator@^0.6.3:
  version "0.6.3"
  resolved "http://npm.htsc/negotiator/download/negotiator-0.6.3.tgz#58e323a72fedc0d6f9cd4d31fe49f51479590ccd"
  integrity sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=

neo-async@^2.6.2:
  version "2.6.2"
  resolved "http://npm.htsc/neo-async/download/neo-async-2.6.2.tgz#b4aafb93e3aeb2d8174ca53cf163ab7d7308305f"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "http://npm.htsc/nice-try/download/nice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

no-case@^3.0.4:
  version "3.0.4"
  resolved "http://npm.htsc/no-case/download/no-case-3.0.4.tgz#d361fd5c9800f558551a8369fc0dcd4662b6124d"
  integrity sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=
  dependencies:
    lower-case "^2.0.2"
    tslib "^2.0.3"

node-abi@^3.0.0:
  version "3.40.0"
  resolved "http://npm.htsc/node-abi/download/node-abi-3.40.0.tgz#51d8ed44534f70ff1357dfbc3a89717b1ceac1b4"
  integrity sha1-UdjtRFNPcP8TV9+8OolxexzqwbQ=
  dependencies:
    semver "^7.3.5"

node-abort-controller@^3.0.1:
  version "3.1.1"
  resolved "http://npm.htsc/node-abort-controller/download/node-abort-controller-3.1.1.tgz#a94377e964a9a37ac3976d848cb5c765833b8548"
  integrity sha1-qUN36WSpo3rDl22EjLXHZYM7hUg=

node-api-version@^0.1.4:
  version "0.1.4"
  resolved "http://npm.htsc/node-api-version/download/node-api-version-0.1.4.tgz#1ed46a485e462d55d66b5aa1fe2821720dedf080"
  integrity sha1-HtRqSF5GLVXWa1qh/ighcg3t8IA=
  dependencies:
    semver "^7.3.5"

node-fetch@^2.6.7:
  version "2.6.11"
  resolved "http://npm.htsc/node-fetch/download/node-fetch-2.6.11.tgz#cde7fc71deef3131ef80a738919f999e6edfff25"
  integrity sha1-zef8cd7vMTHvgKc4kZ+Znm7f/yU=
  dependencies:
    whatwg-url "^5.0.0"

node-forge@^1:
  version "1.3.1"
  resolved "http://npm.htsc/node-forge/download/node-forge-1.3.1.tgz#be8da2af243b2417d5f646a770663a92b7e9ded3"
  integrity sha1-vo2iryQ7JBfV9kancGY6krfp3tM=

node-gyp@^9.0.0:
  version "9.4.0"
  resolved "http://npm.htsc/node-gyp/download/node-gyp-9.4.0.tgz#2a7a91c7cba4eccfd95e949369f27c9ba704f369"
  integrity sha1-KnqRx8uk7M/ZXpSTafJ8m6cE82k=
  dependencies:
    env-paths "^2.2.0"
    exponential-backoff "^3.1.1"
    glob "^7.1.4"
    graceful-fs "^4.2.6"
    make-fetch-happen "^11.0.3"
    nopt "^6.0.0"
    npmlog "^6.0.0"
    rimraf "^3.0.2"
    semver "^7.3.5"
    tar "^6.1.2"
    which "^2.0.2"

node-loader@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/node-loader/download/node-loader-2.0.0.tgz#9109a6d828703fd3e0aa03c1baec12a798071562"
  integrity sha1-kQmm2ChwP9PgqgPBuuwSp5gHFWI=
  dependencies:
    loader-utils "^2.0.0"

node-releases@^2.0.12:
  version "2.0.12"
  resolved "http://npm.htsc/node-releases/download/node-releases-2.0.12.tgz#35627cc224a23bfb06fb3380f2b3afaaa7eb1039"
  integrity sha1-NWJ8wiSiO/sG+zOA8rOvqqfrEDk=

node-releases@^2.0.13:
  version "2.0.13"
  resolved "http://npm.htsc/node-releases/download/node-releases-2.0.13.tgz#d5ed1627c23e3461e819b02e57b75e4899b1c81d"
  integrity sha1-1e0WJ8I+NGHoGbAuV7deSJmxyB0=

nopt@^6.0.0:
  version "6.0.0"
  resolved "http://npm.htsc/nopt/download/nopt-6.0.0.tgz#245801d8ebf409c6df22ab9d95b65e1309cdb16d"
  integrity sha1-JFgB2Ov0CcbfIqudlbZeEwnNsW0=
  dependencies:
    abbrev "^1.0.0"

normalize-package-data@^2.3.2, normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "http://npm.htsc/normalize-package-data/download/normalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-package-data@^3.0.0:
  version "3.0.3"
  resolved "http://npm.htsc/normalize-package-data/download/normalize-package-data-3.0.3.tgz#dbcc3e2da59509a0983422884cd172eefdfa525e"
  integrity sha1-28w+LaWVCaCYNCKITNFy7v36Ul4=
  dependencies:
    hosted-git-info "^4.0.1"
    is-core-module "^2.5.0"
    semver "^7.3.4"
    validate-npm-package-license "^3.0.1"

normalize-path@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/normalize-path/download/normalize-path-1.0.0.tgz#32d0e472f91ff345701c15a8311018d3b0a90379"
  integrity sha1-MtDkcvkf80VwHBWoMRAY07CpA3k=

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/normalize-path/download/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

normalize-url@^6.0.1:
  version "6.1.0"
  resolved "http://npm.htsc/normalize-url/download/normalize-url-6.1.0.tgz#40d0885b535deffe3f3147bec877d05fe4c5668a"
  integrity sha1-QNCIW1Nd7/4/MUe+yHfQX+TFZoo=

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "http://npm.htsc/npm-run-path/download/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

npm-run-path@^4.0.0, npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "http://npm.htsc/npm-run-path/download/npm-run-path-4.0.1.tgz#b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

npmlog@^6.0.0:
  version "6.0.2"
  resolved "http://npm.htsc/npmlog/download/npmlog-6.0.2.tgz#c8166017a42f2dea92d6453168dd865186a70830"
  integrity sha1-yBZgF6QvLeqS1kUxaN2GUYanCDA=
  dependencies:
    are-we-there-yet "^3.0.0"
    console-control-strings "^1.1.0"
    gauge "^4.0.3"
    set-blocking "^2.0.0"

nth-check@^2.0.1:
  version "2.1.1"
  resolved "http://npm.htsc/nth-check/download/nth-check-2.1.1.tgz#c9eab428effce36cd6b92c924bdb000ef1f1ed1d"
  integrity sha1-yeq0KO/842zWuSySS9sADvHx7R0=
  dependencies:
    boolbase "^1.0.0"

object-assign@^4.1.1:
  version "4.1.1"
  resolved "http://npm.htsc/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-inspect@^1.12.3, object-inspect@^1.9.0:
  version "1.12.3"
  resolved "http://npm.htsc/object-inspect/download/object-inspect-1.12.3.tgz#ba62dffd67ee256c8c086dfae69e016cd1f198b9"
  integrity sha1-umLf/WfuJWyMCG365p4BbNHxmLk=

object-keys@^1.1.1:
  version "1.1.1"
  resolved "http://npm.htsc/object-keys/download/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object.assign@^4.1.2, object.assign@^4.1.4:
  version "4.1.4"
  resolved "http://npm.htsc/object.assign/download/object.assign-4.1.4.tgz#9673c7c7c351ab8c4d0b516f4343ebf4dfb7799f"
  integrity sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

object.entries@^1.1.2, object.entries@^1.1.6:
  version "1.1.6"
  resolved "http://npm.htsc/object.entries/download/object.entries-1.1.6.tgz#9737d0e5b8291edd340a3e3264bb8a3b00d5fa23"
  integrity sha1-lzfQ5bgpHt00Cj4yZLuKOwDV+iM=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

object.fromentries@^2.0.6:
  version "2.0.6"
  resolved "http://npm.htsc/object.fromentries/download/object.fromentries-2.0.6.tgz#cdb04da08c539cffa912dcd368b886e0904bfa73"
  integrity sha1-zbBNoIxTnP+pEtzTaLiG4JBL+nM=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

object.hasown@^1.1.2:
  version "1.1.2"
  resolved "http://npm.htsc/object.hasown/download/object.hasown-1.1.2.tgz#f919e21fad4eb38a57bc6345b3afd496515c3f92"
  integrity sha1-+RniH61Os4pXvGNFs6/UllFcP5I=
  dependencies:
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

object.values@^1.1.6:
  version "1.1.6"
  resolved "http://npm.htsc/object.values/download/object.values-1.1.6.tgz#4abbaa71eba47d63589d402856f908243eea9b1d"
  integrity sha1-SruqceukfWNYnUAoVvkIJD7qmx0=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "http://npm.htsc/obuf/download/obuf-1.1.2.tgz#09bea3343d41859ebd446292d11c9d4db619084e"
  integrity sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=

on-finished@2.4.1:
  version "2.4.1"
  resolved "http://npm.htsc/on-finished/download/on-finished-2.4.1.tgz#58c8c44116e54845ad57f14ab10b03533184ac3f"
  integrity sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/on-headers/download/on-headers-1.0.2.tgz#772b0ae6aaa525c399e489adfad90c403eb3c28f"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "http://npm.htsc/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "http://npm.htsc/onetime/download/onetime-5.1.2.tgz#d0e96ebb56b07476df1dd9c4806e5237985ca45e"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

open@^8.0.9:
  version "8.4.2"
  resolved "http://npm.htsc/open/download/open-8.4.2.tgz#5b5ffe2a8f793dcd2aad73e550cb87b59cb084f9"
  integrity sha1-W1/+Ko95Pc0qrXPlUMuHtZywhPk=
  dependencies:
    define-lazy-prop "^2.0.0"
    is-docker "^2.1.1"
    is-wsl "^2.2.0"

optionator@^0.9.1:
  version "0.9.1"
  resolved "http://npm.htsc/optionator/download/optionator-0.9.1.tgz#4f236a6373dae0566a6d43e1326674f50c291499"
  integrity sha1-TyNqY3Pa4FZqbUPhMmZ09QwpFJk=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.3"

ora@^5.0.0, ora@^5.1.0:
  version "5.4.1"
  resolved "http://npm.htsc/ora/download/ora-5.4.1.tgz#1b2678426af4ac4a509008e5e4ac9e9959db9e18"
  integrity sha1-GyZ4Qmr0rEpQkAjl5KyemVnbnhg=
  dependencies:
    bl "^4.1.0"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    is-unicode-supported "^0.1.0"
    log-symbols "^4.1.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

p-cancelable@^2.0.0:
  version "2.1.1"
  resolved "http://npm.htsc/p-cancelable/download/p-cancelable-2.1.1.tgz#aab7fbd416582fa32a3db49859c122487c5ed2cf"
  integrity sha1-qrf71BZYL6MqPbSYWcEiSHxe0s8=

p-defer@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/p-defer/download/p-defer-1.0.0.tgz#9f6eb182f6c9aa8cd743004a7d4f96b196b0fb0c"
  integrity sha1-n26xgvbJqozXQwBKfU+WsZaw+ww=

p-finally@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/p-finally/download/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-is-promise@^2.0.0:
  version "2.1.0"
  resolved "http://npm.htsc/p-is-promise/download/p-is-promise-2.1.0.tgz#918cebaea248a62cf7ffab8e3bca8c5f882fc42e"
  integrity sha1-kYzrrqJIpiz3/6uOO8qMX4gvxC4=

p-limit@^1.1.0:
  version "1.3.0"
  resolved "http://npm.htsc/p-limit/download/p-limit-1.3.0.tgz#b86bd5f0c25690911c7590fcbfc2010d54b3ccb8"
  integrity sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=
  dependencies:
    p-try "^1.0.0"

p-limit@^2.2.0:
  version "2.3.0"
  resolved "http://npm.htsc/p-limit/download/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2:
  version "3.1.0"
  resolved "http://npm.htsc/p-limit/download/p-limit-3.1.0.tgz#e1daccbe78d0d1388ca18c64fea38e3e57e3706b"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/p-locate/download/p-locate-2.0.0.tgz#20a0103b222a70c8fd39cc2e580680f3dde5ec43"
  integrity sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=
  dependencies:
    p-limit "^1.1.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "http://npm.htsc/p-locate/download/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "http://npm.htsc/p-locate/download/p-locate-5.0.0.tgz#83c8315c6785005e3bd021839411c9e110e6d834"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

p-map@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/p-map/download/p-map-4.0.0.tgz#bb2f95a5eda2ec168ec9274e06a747c3e2904d2b"
  integrity sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs=
  dependencies:
    aggregate-error "^3.0.0"

p-retry@^4.5.0:
  version "4.6.2"
  resolved "http://npm.htsc/p-retry/download/p-retry-4.6.2.tgz#9baae7184057edd4e17231cee04264106e092a16"
  integrity sha1-m6rnGEBX7dThcjHO4EJkEG4JKhY=
  dependencies:
    "@types/retry" "0.12.0"
    retry "^0.13.1"

p-try@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/p-try/download/p-try-1.0.0.tgz#cbc79cdbaf8fd4228e13f621f2b1a237c1b207b3"
  integrity sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=

p-try@^2.0.0:
  version "2.2.0"
  resolved "http://npm.htsc/p-try/download/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

param-case@^3.0.4:
  version "3.0.4"
  resolved "http://npm.htsc/param-case/download/param-case-3.0.4.tgz#7d17fe4aa12bde34d4a77d91acfb6219caad01c5"
  integrity sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://npm.htsc/parent-module/download/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-author@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/parse-author/download/parse-author-2.0.0.tgz#d3460bf1ddd0dfaeed42da754242e65fb684a81f"
  integrity sha1-00YL8d3Q367tQtp1QkLmX7aEqB8=
  dependencies:
    author-regex "^1.0.0"

parse-color@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/parse-color/download/parse-color-1.0.0.tgz#7b748b95a83f03f16a94f535e52d7f3d94658619"
  integrity sha1-e3SLlag/A/FqlPU15S1/PZRlhhk=
  dependencies:
    color-convert "~0.5.0"

parse-entities@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/parse-entities/download/parse-entities-2.0.0.tgz#53c6eb5b9314a1f4ec99fa0fdf7ce01ecda0cbe8"
  integrity sha1-U8brW5MUofTsmfoP33zgHs2gy+g=
  dependencies:
    character-entities "^1.0.0"
    character-entities-legacy "^1.0.0"
    character-reference-invalid "^1.0.0"
    is-alphanumerical "^1.0.0"
    is-decimal "^1.0.0"
    is-hexadecimal "^1.0.0"

parse-json@^2.2.0:
  version "2.2.0"
  resolved "http://npm.htsc/parse-json/download/parse-json-2.2.0.tgz#f480f40434ef80741f8469099f8dea18f55a4dc9"
  integrity sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=
  dependencies:
    error-ex "^1.2.0"

parse-json@^5.0.0:
  version "5.2.0"
  resolved "http://npm.htsc/parse-json/download/parse-json-5.2.0.tgz#c76fc66dee54231c962b22bcc8a72cf2f99753cd"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse-ms@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.htsc/parse-ms/-/parse-ms-2.1.0.tgz#348565a753d4391fa524029956b172cb7753097d"
  integrity sha512-kHt7kzLoS9VBZfUsiKjv43mr91ea+U05EyKkEtqp7vNbHxmaVuEqN7XxeEVnGrMtYOAxGrDElSi96K7EgO1zCA==

parse-passwd@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/parse-passwd/download/parse-passwd-1.0.0.tgz#6d5b934a456993b23d37f40a382d6f1666a8e5c6"
  integrity sha1-bVuTSkVpk7I9N/QKOC1vFmao5cY=

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "http://npm.htsc/parseurl/download/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascal-case@^3.1.2:
  version "3.1.2"
  resolved "http://npm.htsc/pascal-case/download/pascal-case-3.1.2.tgz#b48e0ef2b98e205e7c1dae747d0b1508237660eb"
  integrity sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/path-exists/download/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://npm.htsc/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "http://npm.htsc/path-key/download/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "http://npm.htsc/path-key/download/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "http://npm.htsc/path-parse/download/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-scurry@^1.10.0:
  version "1.10.0"
  resolved "http://npm.htsc/path-scurry/download/path-scurry-1.10.0.tgz#0ffbd4c1f7de9600f98a1405507d9f9acb438ab3"
  integrity sha1-D/vUwffelgD5ihQFUH2fmstDirM=
  dependencies:
    lru-cache "^9.1.1 || ^10.0.0"
    minipass "^5.0.0 || ^6.0.2"

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "http://npm.htsc/path-to-regexp/download/path-to-regexp-0.1.7.tgz#df604178005f522f15eb4490e7247a1bfaa67f8c"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-type@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/path-type/download/path-type-2.0.0.tgz#f012ccb8415b7096fc2daa1054c3d72389594c73"
  integrity sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM=
  dependencies:
    pify "^2.0.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/path-type/download/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pend@~1.2.0:
  version "1.2.0"
  resolved "http://npm.htsc/pend/download/pend-1.2.0.tgz#7a57eb550a6783f9115331fcf4663d5c8e007a50"
  integrity sha1-elfrVQpng/kRUzH89GY9XI4AelA=

picocolors@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/picocolors/download/picocolors-1.0.0.tgz#cb5bdc74ff3f51892236eaf79d68bc44564ab81c"
  integrity sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"
  resolved "http://npm.htsc/picomatch/download/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

pify@^2.0.0:
  version "2.3.0"
  resolved "http://npm.htsc/pify/download/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "http://npm.htsc/pkg-dir/download/pkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

please-upgrade-node@^3.2.0:
  version "3.2.0"
  resolved "http://npm.htsc/please-upgrade-node/download/please-upgrade-node-3.2.0.tgz#aeddd3f994c933e4ad98b99d9a556efa0e2fe942"
  integrity sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI=
  dependencies:
    semver-compare "^1.0.0"

plist@^3.0.0, plist@^3.0.4, plist@^3.0.5:
  version "3.0.6"
  resolved "http://npm.htsc/plist/download/plist-3.0.6.tgz#7cfb68a856a7834bca6dbfe3218eb9c7740145d3"
  integrity sha1-fPtoqFang0vKbb/jIY65x3QBRdM=
  dependencies:
    base64-js "^1.5.1"
    xmlbuilder "^15.1.1"

postcss-html@^1.5.0:
  version "1.5.0"
  resolved "http://npm.htsc/postcss-html/download/postcss-html-1.5.0.tgz#57a43bc9e336f516ecc448a37d2e8c2290170a6f"
  integrity sha1-V6Q7yeM29RbsxEijfS6MIpAXCm8=
  dependencies:
    htmlparser2 "^8.0.0"
    js-tokens "^8.0.0"
    postcss "^8.4.0"
    postcss-safe-parser "^6.0.0"

postcss-less@^6.0.0:
  version "6.0.0"
  resolved "http://npm.htsc/postcss-less/download/postcss-less-6.0.0.tgz#463b34c60f53b648c237f569aeb2e09149d85af4"
  integrity sha1-Rjs0xg9TtkjCN/VprrLgkUnYWvQ=

postcss-media-query-parser@^0.2.3:
  version "0.2.3"
  resolved "http://npm.htsc/postcss-media-query-parser/download/postcss-media-query-parser-0.2.3.tgz#27b39c6f4d94f81b1a73b8f76351c609e5cef244"
  integrity sha1-J7Ocb02U+Bsac7j3Y1HGCeXO8kQ=

postcss-modules-extract-imports@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/postcss-modules-extract-imports/download/postcss-modules-extract-imports-3.0.0.tgz#cda1f047c0ae80c97dbe28c3e76a43b88025741d"
  integrity sha1-zaHwR8CugMl9vijD52pDuIAldB0=

postcss-modules-local-by-default@^4.0.3:
  version "4.0.3"
  resolved "http://npm.htsc/postcss-modules-local-by-default/download/postcss-modules-local-by-default-4.0.3.tgz#b08eb4f083050708998ba2c6061b50c2870ca524"
  integrity sha1-sI608IMFBwiZi6LGBhtQwocMpSQ=
  dependencies:
    icss-utils "^5.0.0"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.1.0"

postcss-modules-scope@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/postcss-modules-scope/download/postcss-modules-scope-3.0.0.tgz#9ef3151456d3bbfa120ca44898dfca6f2fa01f06"
  integrity sha1-nvMVFFbTu/oSDKRImN/Kby+gHwY=
  dependencies:
    postcss-selector-parser "^6.0.4"

postcss-modules-values@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/postcss-modules-values/download/postcss-modules-values-4.0.0.tgz#d7c5e7e68c3bb3c9b27cbf48ca0bb3ffb4602c9c"
  integrity sha1-18Xn5ow7s8myfL9Iyguz/7RgLJw=
  dependencies:
    icss-utils "^5.0.0"

postcss-resolve-nested-selector@^0.1.1:
  version "0.1.1"
  resolved "http://npm.htsc/postcss-resolve-nested-selector/download/postcss-resolve-nested-selector-0.1.1.tgz#29ccbc7c37dedfac304e9fff0bf1596b3f6a0e4e"
  integrity sha1-Kcy8fDfe36wwTp//C/FZaz9qDk4=

postcss-safe-parser@^6.0.0:
  version "6.0.0"
  resolved "http://npm.htsc/postcss-safe-parser/download/postcss-safe-parser-6.0.0.tgz#bb4c29894171a94bc5c996b9a30317ef402adaa1"
  integrity sha1-u0wpiUFxqUvFyZa5owMX70Aq2qE=

postcss-selector-parser@^6.0.11, postcss-selector-parser@^6.0.2, postcss-selector-parser@^6.0.4, postcss-selector-parser@^6.0.9:
  version "6.0.13"
  resolved "http://npm.htsc/postcss-selector-parser/download/postcss-selector-parser-6.0.13.tgz#d05d8d76b1e8e173257ef9d60b706a8e5e99bf1b"
  integrity sha1-0F2NdrHo4XMlfvnWC3Bqjl6Zvxs=
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.1.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "http://npm.htsc/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz#723c09920836ba6d3e5af019f92bc0971c02e514"
  integrity sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=

postcss@^8.4.0, postcss@^8.4.16, postcss@^8.4.19:
  version "8.4.27"
  resolved "http://npm.htsc/postcss/download/postcss-8.4.27.tgz#234d7e4b72e34ba5a92c29636734349e0d9c3057"
  integrity sha1-I01+S3LjS6WpLCljZzQ0ng2cMFc=
  dependencies:
    nanoid "^3.3.6"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

postcss@^8.4.21:
  version "8.4.24"
  resolved "http://npm.htsc/postcss/download/postcss-8.4.24.tgz#f714dba9b2284be3cc07dbd2fc57ee4dc972d2df"
  integrity sha1-9xTbqbIoS+PMB9vS/FfuTcly0t8=
  dependencies:
    nanoid "^3.3.6"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

postject@^1.0.0-alpha.6:
  version "1.0.0-alpha.6"
  resolved "http://registry.npm.htsc/postject/-/postject-1.0.0-alpha.6.tgz#9d022332272e2cfce8dea4cfce1ee6dd1b2ee135"
  integrity sha512-b9Eb8h2eVqNE8edvKdwqkrY6O7kAwmI8kcnBv1NScolYJbo59XUF0noFq+lxbC1yN20bmC0WBEbDC5H/7ASb0A==
  dependencies:
    commander "^9.4.0"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "http://npm.htsc/prelude-ls/download/prelude-ls-1.2.1.tgz#debc6489d7a6e6b0e7611888cec880337d316396"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz#d23d41fe1375646de2d0104d3454a3008802cf7b"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

prettier@^2.7.1:
  version "2.8.8"
  resolved "http://npm.htsc/prettier/download/prettier-2.8.8.tgz#e8c5d7e98a4305ffe3de2e1fc4aca1a71c28b1da"
  integrity sha1-6MXX6YpDBf/j3i4fxKyhpxwosdo=

pretty-error@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/pretty-error/download/pretty-error-4.0.0.tgz#90a703f46dd7234adb46d0f84823e9d1cb8f10d6"
  integrity sha1-kKcD9G3XI0rbRtD4SCPp0cuPENY=
  dependencies:
    lodash "^4.17.20"
    renderkid "^3.0.0"

pretty-ms@^7.0.0:
  version "7.0.1"
  resolved "http://registry.npm.htsc/pretty-ms/-/pretty-ms-7.0.1.tgz#7d903eaab281f7d8e03c66f867e239dc32fb73e8"
  integrity sha512-973driJZvxiGOQ5ONsFhOF/DtzPMOMtgC11kCpUrPGMTgqp2q/1gwzCquocrN33is0VZ5GFHXZYMM9l6h67v2Q==
  dependencies:
    parse-ms "^2.1.0"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "http://npm.htsc/process-nextick-args/download/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

progress@^2.0.0, progress@^2.0.3:
  version "2.0.3"
  resolved "http://npm.htsc/progress/download/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

promise-retry@^2.0.1:
  version "2.0.1"
  resolved "http://npm.htsc/promise-retry/download/promise-retry-2.0.1.tgz#ff747a13620ab57ba688f5fc67855410c370da22"
  integrity sha1-/3R6E2IKtXumiPX8Z4VUEMNw2iI=
  dependencies:
    err-code "^2.0.2"
    retry "^0.12.0"

prop-types@^15.8.1:
  version "15.8.1"
  resolved "http://npm.htsc/prop-types/download/prop-types-15.8.1.tgz#67d87bf1a694f48435cf332c24af10214a3140b5"
  integrity sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "http://npm.htsc/proxy-addr/download/proxy-addr-2.0.7.tgz#f19fe69ceab311eeb94b42e70e8c2070f9ba1025"
  integrity sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "http://npm.htsc/proxy-from-env/download/proxy-from-env-1.1.0.tgz#e102f16ca355424865755d2c9e8ea4f24d58c3e2"
  integrity sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/pseudomap/download/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"
  integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=

pump@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/pump/download/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^2.1.0:
  version "2.3.0"
  resolved "http://npm.htsc/punycode/download/punycode-2.3.0.tgz#f67fa67c94da8f4d0cfff981aee4118064199b8f"
  integrity sha1-9n+mfJTaj00M//mBruQRgGQZm48=

qs@6.11.0:
  version "6.11.0"
  resolved "http://npm.htsc/qs/download/qs-6.11.0.tgz#fd0d963446f7a65e1367e01abd85429453f0c37a"
  integrity sha1-/Q2WNEb3pl4TZ+AavYVClFPww3o=
  dependencies:
    side-channel "^1.0.4"

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "http://npm.htsc/queue-microtask/download/queue-microtask-1.2.3.tgz#4929228bbc724dfac43e0efb058caf7b6cfb6243"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

quick-lru@^4.0.1:
  version "4.0.1"
  resolved "http://npm.htsc/quick-lru/download/quick-lru-4.0.1.tgz#5b8878f113a58217848c6482026c73e1ba57727f"
  integrity sha1-W4h48ROlgheEjGSCAmxz4bpXcn8=

quick-lru@^5.1.1:
  version "5.1.1"
  resolved "http://npm.htsc/quick-lru/download/quick-lru-5.1.1.tgz#366493e6b3e42a3a6885e2e99d18f80fb7a8c932"
  integrity sha1-NmST5rPkKjpoheLpnRj4D7eoyTI=

random-path@^0.1.0:
  version "0.1.2"
  resolved "http://npm.htsc/random-path/download/random-path-0.1.2.tgz#78b7f1570e2a09f66a4e2e0113a98ed588e85da9"
  integrity sha1-eLfxVw4qCfZqTi4BE6mO1YjoXak=
  dependencies:
    base32-encode "^0.1.0 || ^1.0.0"
    murmur-32 "^0.1.0 || ^0.2.0"

randombytes@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/randombytes/download/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "http://npm.htsc/range-parser/download/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@2.5.1:
  version "2.5.1"
  resolved "http://npm.htsc/raw-body/download/raw-body-2.5.1.tgz#fe1b1628b181b700215e5fd42389f98b71392857"
  integrity sha1-/hsWKLGBtwAhXl/UI4n5i3E5KFc=
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

rcedit@^3.0.1:
  version "3.0.1"
  resolved "http://npm.htsc/rcedit/download/rcedit-3.0.1.tgz#ae21b43e49c075f4d84df1929832a12c302f3c90"
  integrity sha1-riG0PknAdfTYTfGSmDKhLDAvPJA=
  dependencies:
    cross-spawn-windows-exe "^1.1.0"

rcedit@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.htsc/rcedit/-/rcedit-4.0.1.tgz#892ac47a19204a380f49e00ea38ce070443343c2"
  integrity sha512-bZdaQi34krFWhrDn+O53ccBDw0MkAT2Vhu75SqhtvhQu4OPyFM4RoVheyYiVQYdjhUi6EJMVWQ0tR6bCIYVkUg==
  dependencies:
    cross-spawn-windows-exe "^1.1.0"

rcinfo@^0.1.3:
  version "0.1.3"
  resolved "http://registry.npm.htsc/rcinfo/-/rcinfo-0.1.3.tgz#ac36832d1f1e5970c6379e571480ea5826511fc6"
  integrity sha512-c2XV2aYgY7x3BscO+/B/nCTtMvnclZ8w5D7R6zgK4sGOQnE0MjlXhOPynno7yp6Iw1RPNSXBwXwB1svZVRfcSw==

react-is@^16.13.1:
  version "16.13.1"
  resolved "http://npm.htsc/react-is/download/react-is-16.13.1.tgz#789729a4dc36de2999dc156dd6c1d9c18cea56a4"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

read-pkg-up@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/read-pkg-up/download/read-pkg-up-2.0.0.tgz#6b72a8048984e0c41e79510fd5e9fa99b3b549be"
  integrity sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4=
  dependencies:
    find-up "^2.0.0"
    read-pkg "^2.0.0"

read-pkg-up@^7.0.1:
  version "7.0.1"
  resolved "http://npm.htsc/read-pkg-up/download/read-pkg-up-7.0.1.tgz#f3a6135758459733ae2b95638056e1854e7ef507"
  integrity sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc=
  dependencies:
    find-up "^4.1.0"
    read-pkg "^5.2.0"
    type-fest "^0.8.1"

read-pkg@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/read-pkg/download/read-pkg-2.0.0.tgz#8ef1c0623c6a6db0dc6713c4bfac46332b2368f8"
  integrity sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg=
  dependencies:
    load-json-file "^2.0.0"
    normalize-package-data "^2.3.2"
    path-type "^2.0.0"

read-pkg@^5.2.0:
  version "5.2.0"
  resolved "http://npm.htsc/read-pkg/download/read-pkg-5.2.0.tgz#7bf295438ca5a33e56cd30e053b34ee7250c93cc"
  integrity sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

readable-stream@^2.0.1:
  version "2.3.8"
  resolved "http://npm.htsc/readable-stream/download/readable-stream-2.3.8.tgz#91125e8042bba1b9887f49345f6277027ce8be9b"
  integrity sha1-kRJegEK7obmIf0k0X2J3Anzovps=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6, readable-stream@^3.4.0, readable-stream@^3.6.0:
  version "3.6.2"
  resolved "http://npm.htsc/readable-stream/download/readable-stream-3.6.2.tgz#56a9b36ea965c00c5a93ef31eb111a0f11056967"
  integrity sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "http://npm.htsc/readdirp/download/readdirp-3.6.0.tgz#74a370bd857116e245b29cc97340cd431a02a6c7"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.8.0:
  version "0.8.0"
  resolved "http://npm.htsc/rechoir/download/rechoir-0.8.0.tgz#49f866e0d32146142da3ad8f0eff352b3215ff22"
  integrity sha1-Sfhm4NMhRhQto62PDv81KzIV/yI=
  dependencies:
    resolve "^1.20.0"

redent@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/redent/download/redent-3.0.0.tgz#e557b7998316bb53c9f1f56fa626352c6963059f"
  integrity sha1-5Ve3mYMWu1PJ8fVvpiY1LGljBZ8=
  dependencies:
    indent-string "^4.0.0"
    strip-indent "^3.0.0"

regenerate-unicode-properties@^10.1.0:
  version "10.1.1"
  resolved "http://npm.htsc/regenerate-unicode-properties/download/regenerate-unicode-properties-10.1.1.tgz#6b0e05489d9076b04c436f318d9b067bba459480"
  integrity sha1-aw4FSJ2QdrBMQ28xjZsGe7pFlIA=
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "http://npm.htsc/regenerate/download/regenerate-1.4.2.tgz#b9346d8827e8f5a32f7ba29637d398b69014848a"
  integrity sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=

regenerator-runtime@^0.13.11:
  version "0.13.11"
  resolved "http://npm.htsc/regenerator-runtime/download/regenerator-runtime-0.13.11.tgz#f6dca3e7ceec20590d07ada785636a90cdca17f9"
  integrity sha1-9tyj587sIFkNB62nhWNqkM3KF/k=

regenerator-runtime@^0.14.0:
  version "0.14.0"
  resolved "http://npm.htsc/regenerator-runtime/download/regenerator-runtime-0.14.0.tgz#5e19d68eb12d486f797e15a3c6a918f7cec5eb45"
  integrity sha1-XhnWjrEtSG95fhWjxqkY987F60U=

regenerator-transform@^0.15.2:
  version "0.15.2"
  resolved "http://npm.htsc/regenerator-transform/download/regenerator-transform-0.15.2.tgz#5bbae58b522098ebdf09bca2f83838929001c7a4"
  integrity sha1-W7rli1IgmOvfCbyi+Dg4kpABx6Q=
  dependencies:
    "@babel/runtime" "^7.8.4"

regexp.prototype.flags@^1.4.3:
  version "1.5.0"
  resolved "http://npm.htsc/regexp.prototype.flags/download/regexp.prototype.flags-1.5.0.tgz#fe7ce25e7e4cca8db37b6634c8a2c7009199b9cb"
  integrity sha1-/nziXn5Myo2ze2Y0yKLHAJGZucs=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    functions-have-names "^1.2.3"

regexpp@^3.0.0, regexpp@^3.1.0:
  version "3.2.0"
  resolved "http://npm.htsc/regexpp/download/regexpp-3.2.0.tgz#0425a2768d8f23bad70ca4b90461fa2f1213e1b2"
  integrity sha1-BCWido2PI7rXDKS5BGH6LxIT4bI=

regexpu-core@^5.3.1:
  version "5.3.2"
  resolved "http://npm.htsc/regexpu-core/download/regexpu-core-5.3.2.tgz#11a2b06884f3527aec3e93dbbf4a3b958a95546b"
  integrity sha1-EaKwaITzUnrsPpPbv0o7lYqVVGs=
  dependencies:
    "@babel/regjsgen" "^0.8.0"
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.1.0"
    regjsparser "^0.9.1"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.1.0"

regjsparser@^0.9.1:
  version "0.9.1"
  resolved "http://npm.htsc/regjsparser/download/regjsparser-0.9.1.tgz#272d05aa10c7c1f67095b1ff0addae8442fc5709"
  integrity sha1-Jy0FqhDHwfZwlbH/Ct2uhEL8Vwk=
  dependencies:
    jsesc "~0.5.0"

relateurl@^0.2.7:
  version "0.2.7"
  resolved "http://npm.htsc/relateurl/download/relateurl-0.2.7.tgz#54dbf377e51440aca90a4cd274600d3ff2d888a9"
  integrity sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=

renderkid@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/renderkid/download/renderkid-3.0.0.tgz#5fd823e4d6951d37358ecc9a58b1f06836b6268a"
  integrity sha1-X9gj5NaVHTc1jsyaWLHwaDa2Joo=
  dependencies:
    css-select "^4.1.3"
    dom-converter "^0.2.0"
    htmlparser2 "^6.1.0"
    lodash "^4.17.21"
    strip-ansi "^6.0.1"

repeat-string@^1.5.4:
  version "1.6.1"
  resolved "http://npm.htsc/repeat-string/download/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

require-directory@^2.1.1:
  version "2.1.1"
  resolved "http://npm.htsc/require-directory/download/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "http://npm.htsc/require-from-string/download/require-from-string-2.0.2.tgz#89a7fdd938261267318eafe14f9c32e598c36909"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/require-main-filename/download/require-main-filename-2.0.0.tgz#d0b329ecc7cc0f61649f62215be69af54aa8989b"
  integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/requires-port/download/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resolve-alpn@^1.0.0:
  version "1.2.1"
  resolved "http://npm.htsc/resolve-alpn/download/resolve-alpn-1.2.1.tgz#b7adbdac3546aaaec20b45e7d8265927072726f9"
  integrity sha1-t629rDVGqq7CC0Xn2CZZJwcnJvk=

resolve-dir@^1.0.0:
  version "1.0.1"
  resolved "http://npm.htsc/resolve-dir/download/resolve-dir-1.0.1.tgz#79a40644c362be82f26effe739c9bb5382046f43"
  integrity sha1-eaQGRMNivoLybv/nOcm7U4IEb0M=
  dependencies:
    expand-tilde "^2.0.0"
    global-modules "^1.0.0"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "http://npm.htsc/resolve-from/download/resolve-from-5.0.0.tgz#c35225843df8f776df21c57557bc087e9dfdfc69"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve-package@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/resolve-package/download/resolve-package-1.0.1.tgz#686f70b188bd7d675f5bbc4282ccda060abb9d27"
  integrity sha1-aG9wsYi9fWdfW7xCgszaBgq7nSc=
  dependencies:
    get-installed-path "^2.0.3"

resolve@^1.1.6, resolve@^1.10.0, resolve@^1.10.1, resolve@^1.20.0, resolve@^1.22.1:
  version "1.22.2"
  resolved "http://npm.htsc/resolve/download/resolve-1.22.2.tgz#0ed0943d4e301867955766c9f3e1ae6d01c6845f"
  integrity sha1-DtCUPU4wGGeVV2bJ8+GubQHGhF8=
  dependencies:
    is-core-module "^2.11.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^1.14.2, resolve@^1.22.0:
  version "1.22.8"
  resolved "http://npm.htsc/resolve/download/resolve-1.22.8.tgz#b6c87a9f2aa06dfab52e3d70ac8cde321fa5a48d"
  integrity sha1-tsh6nyqgbfq1Lj1wrIzeMh+lpI0=
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.4:
  version "2.0.0-next.4"
  resolved "http://npm.htsc/resolve/download/resolve-2.0.0-next.4.tgz#3d37a113d6429f496ec4752d2a2e58efb1fd4660"
  integrity sha1-PTehE9ZCn0luxHUtKi5Y77H9RmA=
  dependencies:
    is-core-module "^2.9.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

responselike@^2.0.0:
  version "2.0.1"
  resolved "http://npm.htsc/responselike/download/responselike-2.0.1.tgz#9a0bc8fdc252f3fb1cca68b016591059ba1422bc"
  integrity sha1-mgvI/cJS8/scymiwFlkQWboUIrw=
  dependencies:
    lowercase-keys "^2.0.0"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://npm.htsc/restore-cursor/download/restore-cursor-3.1.0.tgz#39f67c54b3a7a58cea5236d95cf0034239631f7e"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

retry@^0.12.0:
  version "0.12.0"
  resolved "http://npm.htsc/retry/download/retry-0.12.0.tgz#1b42a6266a21f07421d1b0b54b7dc167b01c013b"
  integrity sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=

retry@^0.13.1:
  version "0.13.1"
  resolved "http://npm.htsc/retry/download/retry-0.13.1.tgz#185b1587acf67919d63b357349e03537b2484658"
  integrity sha1-GFsVh6z2eRnWOzVzSeA1N7JIRlg=

reusify@^1.0.4:
  version "1.0.4"
  resolved "http://npm.htsc/reusify/download/reusify-1.0.4.tgz#90da382b1e126efc02146e90845a88db12925d76"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

rfdc@^1.3.0:
  version "1.3.0"
  resolved "http://npm.htsc/rfdc/download/rfdc-1.3.0.tgz#d0b7c441ab2720d05dc4cf26e01c89631d9da08b"
  integrity sha1-0LfEQasnINBdxM8m4ByJYx2doIs=

rimraf@^3.0.0, rimraf@^3.0.2:
  version "3.0.2"
  resolved "http://npm.htsc/rimraf/download/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

rimraf@~2.6.2:
  version "2.6.3"
  resolved "http://npm.htsc/rimraf/download/rimraf-2.6.3.tgz#b2d104fe0d8fb27cf9e0a1cda8262dd3833c6cab"
  integrity sha1-stEE/g2Psnz54KHNqCYt04M8bKs=
  dependencies:
    glob "^7.1.3"

roarr@^2.15.3:
  version "2.15.4"
  resolved "http://npm.htsc/roarr/download/roarr-2.15.4.tgz#f5fe795b7b838ccfe35dc608e0282b9eba2e7afd"
  integrity sha1-9f55W3uDjM/jXcYI4Cgrnrouev0=
  dependencies:
    boolean "^3.0.1"
    detect-node "^2.0.4"
    globalthis "^1.0.1"
    json-stringify-safe "^5.0.1"
    semver-compare "^1.0.0"
    sprintf-js "^1.1.2"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "http://npm.htsc/run-parallel/download/run-parallel-1.2.0.tgz#66d1368da7bdf921eb9d95bd1a9229e7f21a43ee"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

rxjs@^7.5.1, rxjs@^7.5.2, rxjs@^7.8.0, rxjs@^7.8.1:
  version "7.8.1"
  resolved "http://npm.htsc/rxjs/download/rxjs-7.8.1.tgz#6f6f3d99ea8044291efd92e7c7fcf562c4057543"
  integrity sha1-b289meqARCke/ZLnx/z1YsQFdUM=
  dependencies:
    tslib "^2.1.0"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://npm.htsc/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@5.2.1, safe-buffer@>=5.1.0, safe-buffer@^5.1.0, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "http://npm.htsc/safe-buffer/download/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-regex-test@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/safe-regex-test/download/safe-regex-test-1.0.0.tgz#793b874d524eb3640d1873aad03596db2d4f2295"
  integrity sha1-eTuHTVJOs2QNGHOq0DWW2y1PIpU=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.3"
    is-regex "^1.1.4"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "http://npm.htsc/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sax@^1.2.4:
  version "1.2.4"
  resolved "http://npm.htsc/sax/download/sax-1.2.4.tgz#2816234e2378bddc4e5354fab5caa895df7100d9"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

schema-utils@^3.1.1, schema-utils@^3.2.0:
  version "3.3.0"
  resolved "http://npm.htsc/schema-utils/download/schema-utils-3.3.0.tgz#f50a88877c3c01652a15b622ae9e9795df7a60fe"
  integrity sha1-9QqIh3w8AWUqFbYirp6Xld96YP4=
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

schema-utils@^4.0.0:
  version "4.2.0"
  resolved "http://npm.htsc/schema-utils/download/schema-utils-4.2.0.tgz#70d7c93e153a273a805801882ebd3bff20d89c8b"
  integrity sha1-cNfJPhU6JzqAWAGILr07/yDYnIs=
  dependencies:
    "@types/json-schema" "^7.0.9"
    ajv "^8.9.0"
    ajv-formats "^2.1.1"
    ajv-keywords "^5.1.0"

select-hose@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/select-hose/download/select-hose-2.0.0.tgz#625d8658f865af43ec962bfc376a37359a4994ca"
  integrity sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=

selfsigned@^2.1.1:
  version "2.1.1"
  resolved "http://npm.htsc/selfsigned/download/selfsigned-2.1.1.tgz#18a7613d714c0cd3385c48af0075abf3f266af61"
  integrity sha1-GKdhPXFMDNM4XEivAHWr8/Jmr2E=
  dependencies:
    node-forge "^1"

semver-compare@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/semver-compare/download/semver-compare-1.0.0.tgz#0dee216a1c941ab37e9efb1788f6afc5ff5537fc"
  integrity sha1-De4hahyUGrN+nvsXiPavxf9VN/w=

"semver@2 || 3 || 4 || 5", semver@^5.5.0:
  version "5.7.1"
  resolved "http://npm.htsc/semver/download/semver-5.7.1.tgz#a954f931aeba508d307bbf069eff0c01c96116f7"
  integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=

semver@^5.7.1:
  version "5.7.2"
  resolved "http://npm.htsc/semver/download/semver-5.7.2.tgz#48d55db737c3287cd4835e17fa13feace1c41ef8"
  integrity sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=

semver@^6.1.0, semver@^6.3.1:
  version "6.3.1"
  resolved "http://npm.htsc/semver/download/semver-6.3.1.tgz#556d2ef8689146e46dcea4bfdd095f3434dffcb4"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^6.2.0, semver@^6.3.0:
  version "6.3.0"
  resolved "http://npm.htsc/semver/download/semver-6.3.0.tgz#ee0a64c8af5e8ceea67687b133761e1becbd1d3d"
  integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=

semver@^7.1.1, semver@^7.1.3, semver@^7.2.1, semver@^7.3.2, semver@^7.3.4, semver@^7.3.5, semver@^7.3.7, semver@^7.3.8:
  version "7.5.3"
  resolved "http://npm.htsc/semver/download/semver-7.5.3.tgz#161ce8c2c6b4b3bdca6caadc9fa3317a4c4fe88e"
  integrity sha1-Fhzowsa0s73KbKrcn6MxekxP6I4=
  dependencies:
    lru-cache "^6.0.0"

semver@^7.6.0:
  version "7.7.2"
  resolved "http://registry.npm.htsc/semver/-/semver-7.7.2.tgz#67d99fdcd35cec21e6f8b87a7fd515a33f982b58"
  integrity sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==

send@0.18.0:
  version "0.18.0"
  resolved "http://npm.htsc/send/download/send-0.18.0.tgz#670167cc654b05f5aa4a767f9113bb371bc706be"
  integrity sha1-ZwFnzGVLBfWqSnZ/kRO7NxvHBr4=
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serialize-error@^7.0.1:
  version "7.0.1"
  resolved "http://npm.htsc/serialize-error/download/serialize-error-7.0.1.tgz#f1360b0447f61ffb483ec4157c737fab7d778e18"
  integrity sha1-8TYLBEf2H/tIPsQVfHN/q313jhg=
  dependencies:
    type-fest "^0.13.1"

serialize-javascript@^6.0.1:
  version "6.0.1"
  resolved "http://npm.htsc/serialize-javascript/download/serialize-javascript-6.0.1.tgz#b206efb27c3da0b0ab6b52f48d170b7996458e5c"
  integrity sha1-sgbvsnw9oLCra1L0jRcLeZZFjlw=
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.9.1:
  version "1.9.1"
  resolved "http://npm.htsc/serve-index/download/serve-index-1.9.1.tgz#d3768d69b1e7d82e5ce050fff5b453bea12a9239"
  integrity sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.15.0:
  version "1.15.0"
  resolved "http://npm.htsc/serve-static/download/serve-static-1.15.0.tgz#faaef08cffe0a1a62f60cad0c4e513cff0ac9540"
  integrity sha1-+q7wjP/goaYvYMrQxOUTz/CslUA=
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.18.0"

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/set-blocking/download/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "http://npm.htsc/setprototypeof/download/setprototypeof-1.1.0.tgz#d0bd85536887b6fe7c0d818cb962d9d91c54e656"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "http://npm.htsc/setprototypeof/download/setprototypeof-1.2.0.tgz#66c9a24a73f9fc28cbe66b09fed3d33dcaf1b424"
  integrity sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=

shallow-clone@^3.0.0:
  version "3.0.1"
  resolved "http://npm.htsc/shallow-clone/download/shallow-clone-3.0.1.tgz#8f2981ad92531f55035b01fb230769a40e02efa3"
  integrity sha1-jymBrZJTH1UDWwH7IwdppA4C76M=
  dependencies:
    kind-of "^6.0.2"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "http://npm.htsc/shebang-command/download/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/shebang-command/download/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/shebang-regex/download/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/shebang-regex/download/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shell-quote@^1.7.3, shell-quote@^1.8.1:
  version "1.8.1"
  resolved "http://npm.htsc/shell-quote/download/shell-quote-1.8.1.tgz#6dbf4db75515ad5bac63b4f1894c3a154c766680"
  integrity sha1-bb9Nt1UVrVusY7TxiUw6FUx2ZoA=

side-channel@^1.0.4:
  version "1.0.4"
  resolved "http://npm.htsc/side-channel/download/side-channel-1.0.4.tgz#efce5c8fdc104ee751b25c58d4290011fa5ea2cf"
  integrity sha1-785cj9wQTudRslxY1CkAEfpeos8=
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

signal-exit@^3.0.0, signal-exit@^3.0.2, signal-exit@^3.0.3, signal-exit@^3.0.7:
  version "3.0.7"
  resolved "http://npm.htsc/signal-exit/download/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

signal-exit@^4.0.1:
  version "4.0.2"
  resolved "http://npm.htsc/signal-exit/download/signal-exit-4.0.2.tgz#ff55bb1d9ff2114c13b400688fa544ac63c36967"
  integrity sha1-/1W7HZ/yEUwTtABoj6VErGPDaWc=

slash@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/slash/download/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/slice-ansi/download/slice-ansi-3.0.0.tgz#31ddc10930a1b7e0b67b08c96c2f49b77a789787"
  integrity sha1-Md3BCTCht+C2ewjJbC9Jt3p4l4c=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/slice-ansi/download/slice-ansi-4.0.0.tgz#500e8dd0fd55b05815086255b3195adf2a45fe6b"
  integrity sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

smart-buffer@^4.2.0:
  version "4.2.0"
  resolved "http://npm.htsc/smart-buffer/download/smart-buffer-4.2.0.tgz#6e1d71fa4f18c05f7d0ff216dd16a481d0e8d9ae"
  integrity sha1-bh1x+k8YwF99D/IW3RakgdDo2a4=

sockjs@^0.3.24:
  version "0.3.24"
  resolved "http://npm.htsc/sockjs/download/sockjs-0.3.24.tgz#c9bc8995f33a111bea0395ec30aa3206bdb5ccce"
  integrity sha1-ybyJlfM6ERvqA5XsMKoyBr21zM4=
  dependencies:
    faye-websocket "^0.11.3"
    uuid "^8.3.2"
    websocket-driver "^0.7.4"

socks-proxy-agent@^7.0.0:
  version "7.0.0"
  resolved "http://npm.htsc/socks-proxy-agent/download/socks-proxy-agent-7.0.0.tgz#dc069ecf34436621acb41e3efa66ca1b5fed15b6"
  integrity sha1-3AaezzRDZiGstB4++mbKG1/tFbY=
  dependencies:
    agent-base "^6.0.2"
    debug "^4.3.3"
    socks "^2.6.2"

socks@^2.6.2:
  version "2.7.1"
  resolved "http://npm.htsc/socks/download/socks-2.7.1.tgz#d8e651247178fde79c0663043e07240196857d55"
  integrity sha1-2OZRJHF4/eecBmMEPgckAZaFfVU=
  dependencies:
    ip "^2.0.0"
    smart-buffer "^4.2.0"

source-map-js@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/source-map-js/download/source-map-js-1.0.2.tgz#adbc361d9c62df380125e7f161f71c826f1e490c"
  integrity sha1-rbw2HZxi3zgBJefxYfccgm8eSQw=

source-map-support@^0.5.13, source-map-support@~0.5.20:
  version "0.5.21"
  resolved "http://npm.htsc/source-map-support/download/source-map-support-0.5.21.tgz#04fe7c7f9e1ed2d662233c28cb2b35b9f63f6e4f"
  integrity sha1-BP58f54e0tZiIzwoyys1ufY/bk8=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@^0.6.0, source-map@~0.6.0:
  version "0.6.1"
  resolved "http://npm.htsc/source-map/download/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

spawn-command@0.0.2:
  version "0.0.2"
  resolved "http://npm.htsc/spawn-command/download/spawn-command-0.0.2.tgz#9544e1a43ca045f8531aac1a48cb29bdae62338e"
  integrity sha1-lUThpDygRfhTGqwaSMspva5iM44=

spdx-correct@^3.0.0:
  version "3.2.0"
  resolved "http://npm.htsc/spdx-correct/download/spdx-correct-3.2.0.tgz#4f5ab0668f0059e34f9c00dce331784a12de4e9c"
  integrity sha1-T1qwZo8AWeNPnADc4zF4ShLeTpw=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "http://npm.htsc/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz#3f28ce1a77a00372683eade4a433183527a2163d"
  integrity sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "http://npm.htsc/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz#cf70f50482eefdc98e3ce0a6833e4a53ceeba679"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.13"
  resolved "http://npm.htsc/spdx-license-ids/download/spdx-license-ids-3.0.13.tgz#7189a474c46f8d47c7b0da4b987bb45e908bd2d5"
  integrity sha1-cYmkdMRvjUfHsNpLmHu0XpCL0tU=

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/spdy-transport/download/spdy-transport-3.0.0.tgz#00d4863a6400ad75df93361a1608605e5dcdcf31"
  integrity sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.2:
  version "4.0.2"
  resolved "http://npm.htsc/spdy/download/spdy-4.0.2.tgz#b74f466203a3eda452c02492b91fb9e84a27677b"
  integrity sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

sprintf-js@^1.1.2:
  version "1.1.2"
  resolved "http://npm.htsc/sprintf-js/download/sprintf-js-1.1.2.tgz#da1765262bf8c0f571749f2ad6c26300207ae673"
  integrity sha1-2hdlJiv4wPVxdJ8q1sJjACB65nM=

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://npm.htsc/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

ssri@^10.0.0:
  version "10.0.4"
  resolved "http://npm.htsc/ssri/download/ssri-10.0.4.tgz#5a20af378be586df139ddb2dfb3bf992cf0daba6"
  integrity sha1-WiCvN4vlht8Tndst+zv5ks8Nq6Y=
  dependencies:
    minipass "^5.0.0"

statuses@2.0.1:
  version "2.0.1"
  resolved "http://npm.htsc/statuses/download/statuses-2.0.1.tgz#55cb000ccf1d48728bd23c685a063998cf1a1b63"
  integrity sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=

"statuses@>= 1.4.0 < 2":
  version "1.5.0"
  resolved "http://npm.htsc/statuses/download/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

steno@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/steno/download/steno-3.0.0.tgz#212a11e8ef3646b610efc8953842f556fd0df28f"
  integrity sha1-ISoR6O82RrYQ78iVOEL1Vv0N8o8=

stream-buffers@~2.2.0:
  version "2.2.0"
  resolved "http://npm.htsc/stream-buffers/download/stream-buffers-2.2.0.tgz#91d5f5130d1cef96dcfa7f726945188741d09ee4"
  integrity sha1-kdX1Ew0c75bc+n9yaUUYh0HQnuQ=

string-argv@0.3.1:
  version "0.3.1"
  resolved "http://npm.htsc/string-argv/download/string-argv-0.3.1.tgz#95e2fbec0427ae19184935f816d74aaa4c5c19da"
  integrity sha1-leL77AQnrhkYSTX4FtdKqkxcGdo=

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  resolved "http://npm.htsc/string-width/download/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

"string-width@^1.0.2 || 2 || 3 || 4", string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "http://npm.htsc/string-width/download/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1, string-width@^5.1.2:
  version "5.1.2"
  resolved "http://npm.htsc/string-width/download/string-width-5.1.2.tgz#14f8daec6d81e7221d2a357e668cab73bdbca794"
  integrity sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string.prototype.matchall@^4.0.8:
  version "4.0.8"
  resolved "http://npm.htsc/string.prototype.matchall/download/string.prototype.matchall-4.0.8.tgz#3bf85722021816dcd1bf38bb714915887ca79fd3"
  integrity sha1-O/hXIgIYFtzRvzi7cUkViHynn9M=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    get-intrinsic "^1.1.3"
    has-symbols "^1.0.3"
    internal-slot "^1.0.3"
    regexp.prototype.flags "^1.4.3"
    side-channel "^1.0.4"

string.prototype.trim@^1.2.7:
  version "1.2.7"
  resolved "http://npm.htsc/string.prototype.trim/download/string.prototype.trim-1.2.7.tgz#a68352740859f6893f14ce3ef1bb3037f7a90533"
  integrity sha1-poNSdAhZ9ok/FM4+8bswN/epBTM=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

string.prototype.trimend@^1.0.6:
  version "1.0.6"
  resolved "http://npm.htsc/string.prototype.trimend/download/string.prototype.trimend-1.0.6.tgz#c4a27fa026d979d79c04f17397f250a462944533"
  integrity sha1-xKJ/oCbZedecBPFzl/JQpGKURTM=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

string.prototype.trimstart@^1.0.6:
  version "1.0.6"
  resolved "http://npm.htsc/string.prototype.trimstart/download/string.prototype.trimstart-1.0.6.tgz#e90ab66aa8e4007d92ef591bbf3cd422c56bdcf4"
  integrity sha1-6Qq2aqjkAH2S71kbvzzUIsVr3PQ=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "http://npm.htsc/string_decoder/download/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://npm.htsc/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

stringify-object@^3.3.0:
  version "3.3.0"
  resolved "http://npm.htsc/stringify-object/download/stringify-object-3.3.0.tgz#703065aefca19300d3ce88af4f5b3956d7556629"
  integrity sha1-cDBlrvyhkwDTzoivT1s5VtdVZik=
  dependencies:
    get-own-enumerable-property-symbols "^3.0.0"
    is-obj "^1.0.1"
    is-regexp "^1.0.0"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  resolved "http://npm.htsc/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://npm.htsc/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  resolved "http://npm.htsc/strip-ansi/download/strip-ansi-7.1.0.tgz#d5b6568ca689d8561370b0707685d22434faff45"
  integrity sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=
  dependencies:
    ansi-regex "^6.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/strip-bom/download/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/strip-eof/download/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/strip-final-newline/download/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-indent@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/strip-indent/download/strip-indent-2.0.0.tgz#5ef8db295d01e6ed6cbf7aab96998d7822527b68"
  integrity sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=

strip-indent@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/strip-indent/download/strip-indent-3.0.0.tgz#c32e1cee940b6b3432c771bc2c54bcce73cd3001"
  integrity sha1-wy4c7pQLazQyx3G8LFS8znPNMAE=
  dependencies:
    min-indent "^1.0.0"

strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "http://npm.htsc/strip-json-comments/download/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

strip-outer@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/strip-outer/download/strip-outer-1.0.1.tgz#b2fd2abf6604b9d1e6013057195df836b8a9d631"
  integrity sha1-sv0qv2YEudHmATBXGV34Nrip1jE=
  dependencies:
    escape-string-regexp "^1.0.2"

style-loader@^3.0.0:
  version "3.3.3"
  resolved "http://npm.htsc/style-loader/download/style-loader-3.3.3.tgz#bba8daac19930169c0c9c96706749a597ae3acff"
  integrity sha1-u6jarBmTAWnAyclnBnSaWXrjrP8=

style-search@^0.1.0:
  version "0.1.0"
  resolved "http://npm.htsc/style-search/download/style-search-0.1.0.tgz#7958c793e47e32e07d2b5cafe5c0bf8e12e77902"
  integrity sha1-eVjHk+R+MuB9K1yv5cC/jhLneQI=

stylelint-config-css-modules@^4.1.0:
  version "4.2.0"
  resolved "http://npm.htsc/stylelint-config-css-modules/download/stylelint-config-css-modules-4.2.0.tgz#0196347d5c143eff9e2a3e97b1ba980253b6b8bf"
  integrity sha1-AZY0fVwUPv+eKj6XsbqYAlO2uL8=
  optionalDependencies:
    stylelint-scss "^4.3.0"

stylelint-config-prettier@^9.0.3:
  version "9.0.5"
  resolved "http://npm.htsc/stylelint-config-prettier/download/stylelint-config-prettier-9.0.5.tgz#9f78bbf31c7307ca2df2dd60f42c7014ee9da56e"
  integrity sha1-n3i78xxzB8ot8t1g9CxwFO6dpW4=

stylelint-config-recommended@^8.0.0:
  version "8.0.0"
  resolved "http://npm.htsc/stylelint-config-recommended/download/stylelint-config-recommended-8.0.0.tgz#7736be9984246177f017c39ec7b1cd0f19ae9117"
  integrity sha1-dza+mYQkYXfwF8Oex7HNDxmukRc=

stylelint-config-standard@^26.0.0:
  version "26.0.0"
  resolved "http://npm.htsc/stylelint-config-standard/download/stylelint-config-standard-26.0.0.tgz#4701b8d582d34120eec7d260ba779e4c2d953635"
  integrity sha1-RwG41YLTQSDux9JguneeTC2VNjU=
  dependencies:
    stylelint-config-recommended "^8.0.0"

stylelint-declaration-block-no-ignored-properties@^2.5.0:
  version "2.7.0"
  resolved "http://npm.htsc/stylelint-declaration-block-no-ignored-properties/download/stylelint-declaration-block-no-ignored-properties-2.7.0.tgz#78a4a03a5ec74a06b4abb226a31ff8fc57782a0e"
  integrity sha1-eKSgOl7HSga0q7Imox/4/Fd4Kg4=

stylelint-prettier@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/stylelint-prettier/download/stylelint-prettier-2.0.0.tgz#ead781aea522379f2ffa2d136bafdfc451d699a5"
  integrity sha1-6teBrqUiN58v+i0Ta6/fxFHWmaU=
  dependencies:
    prettier-linter-helpers "^1.0.0"

stylelint-scss@^4.3.0:
  version "4.7.0"
  resolved "http://npm.htsc/stylelint-scss/download/stylelint-scss-4.7.0.tgz#f986bf8c5a4b93eae2b67d3a3562eef822657908"
  integrity sha1-+Ya/jFpLk+ritn06NWLu+CJleQg=
  dependencies:
    postcss-media-query-parser "^0.2.3"
    postcss-resolve-nested-selector "^0.1.1"
    postcss-selector-parser "^6.0.11"
    postcss-value-parser "^4.2.0"

stylelint@^14.9.1:
  version "14.16.1"
  resolved "http://npm.htsc/stylelint/download/stylelint-14.16.1.tgz#b911063530619a1bbe44c2b875fd8181ebdc742d"
  integrity sha1-uREGNTBhmhu+RMK4df2BgevcdC0=
  dependencies:
    "@csstools/selector-specificity" "^2.0.2"
    balanced-match "^2.0.0"
    colord "^2.9.3"
    cosmiconfig "^7.1.0"
    css-functions-list "^3.1.0"
    debug "^4.3.4"
    fast-glob "^3.2.12"
    fastest-levenshtein "^1.0.16"
    file-entry-cache "^6.0.1"
    global-modules "^2.0.0"
    globby "^11.1.0"
    globjoin "^0.1.4"
    html-tags "^3.2.0"
    ignore "^5.2.1"
    import-lazy "^4.0.0"
    imurmurhash "^0.1.4"
    is-plain-object "^5.0.0"
    known-css-properties "^0.26.0"
    mathml-tag-names "^2.1.3"
    meow "^9.0.0"
    micromatch "^4.0.5"
    normalize-path "^3.0.0"
    picocolors "^1.0.0"
    postcss "^8.4.19"
    postcss-media-query-parser "^0.2.3"
    postcss-resolve-nested-selector "^0.1.1"
    postcss-safe-parser "^6.0.0"
    postcss-selector-parser "^6.0.11"
    postcss-value-parser "^4.2.0"
    resolve-from "^5.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"
    style-search "^0.1.0"
    supports-hyperlinks "^2.3.0"
    svg-tags "^1.0.0"
    table "^6.8.1"
    v8-compile-cache "^2.3.0"
    write-file-atomic "^4.0.2"

sudo-prompt@^9.1.1:
  version "9.2.1"
  resolved "http://npm.htsc/sudo-prompt/download/sudo-prompt-9.2.1.tgz#77efb84309c9ca489527a4e749f287e6bdd52afd"
  integrity sha1-d++4QwnJykiVJ6TnSfKH5r3VKv0=

sumchecker@^3.0.1:
  version "3.0.1"
  resolved "http://npm.htsc/sumchecker/download/sumchecker-3.0.1.tgz#6377e996795abb0b6d348e9b3e1dfb24345a8e42"
  integrity sha1-Y3fplnlauwttNI6bPh37JDRajkI=
  dependencies:
    debug "^4.1.0"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "http://npm.htsc/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.2.0"
  resolved "http://npm.htsc/supports-color/download/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0, supports-color@^8.1.1:
  version "8.1.1"
  resolved "http://npm.htsc/supports-color/download/supports-color-8.1.1.tgz#cd6fc17e28500cff56c1b86c0a7fd4a54a73005c"
  integrity sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=
  dependencies:
    has-flag "^4.0.0"

supports-hyperlinks@^2.3.0:
  version "2.3.0"
  resolved "http://npm.htsc/supports-hyperlinks/download/supports-hyperlinks-2.3.0.tgz#3943544347c1ff90b15effb03fc14ae45ec10624"
  integrity sha1-OUNUQ0fB/5CxXv+wP8FK5F7BBiQ=
  dependencies:
    has-flag "^4.0.0"
    supports-color "^7.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

svg-tags@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/svg-tags/download/svg-tags-1.0.0.tgz#58f71cee3bd519b59d4b2a843b6c7de64ac04764"
  integrity sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=

table@^6.0.9, table@^6.8.1:
  version "6.8.1"
  resolved "http://npm.htsc/table/download/table-6.8.1.tgz#ea2b71359fe03b017a5fbc296204471158080bdf"
  integrity sha1-6itxNZ/gOwF6X7wpYgRHEVgIC98=
  dependencies:
    ajv "^8.0.1"
    lodash.truncate "^4.4.2"
    slice-ansi "^4.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

tapable@^0.1.8:
  version "0.1.10"
  resolved "http://npm.htsc/tapable/download/tapable-0.1.10.tgz#29c35707c2b70e50d07482b5d202e8ed446dafd4"
  integrity sha1-KcNXB8K3DlDQdIK10gLo7URtr9Q=

tapable@^2.0.0, tapable@^2.1.1, tapable@^2.2.0, tapable@^2.2.1:
  version "2.2.1"
  resolved "http://npm.htsc/tapable/download/tapable-2.2.1.tgz#1967a73ef4060a82f12ab96af86d52fdb76eeca0"
  integrity sha1-GWenPvQGCoLxKrlq+G1S/bdu7KA=

tar@^6.0.5, tar@^6.1.11, tar@^6.1.2:
  version "6.1.15"
  resolved "http://npm.htsc/tar/download/tar-6.1.15.tgz#c9738b0b98845a3b344d334b8fa3041aaba53a69"
  integrity sha1-yXOLC5iEWjs0TTNLj6MEGqulOmk=
  dependencies:
    chownr "^2.0.0"
    fs-minipass "^2.0.0"
    minipass "^5.0.0"
    minizlib "^2.1.1"
    mkdirp "^1.0.3"
    yallist "^4.0.0"

temp@^0.9.0:
  version "0.9.4"
  resolved "http://npm.htsc/temp/download/temp-0.9.4.tgz#cd20a8580cb63635d0e4e9d4bd989d44286e7620"
  integrity sha1-zSCoWAy2NjXQ5OnUvZidRChudiA=
  dependencies:
    mkdirp "^0.5.1"
    rimraf "~2.6.2"

terser-webpack-plugin@^5.3.7:
  version "5.3.9"
  resolved "http://npm.htsc/terser-webpack-plugin/download/terser-webpack-plugin-5.3.9.tgz#832536999c51b46d468067f9e37662a3b96adfe1"
  integrity sha1-gyU2mZxRtG1GgGf543Zio7lq3+E=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.17"
    jest-worker "^27.4.5"
    schema-utils "^3.1.1"
    serialize-javascript "^6.0.1"
    terser "^5.16.8"

terser@^5.10.0, terser@^5.16.8:
  version "5.18.2"
  resolved "http://npm.htsc/terser/download/terser-5.18.2.tgz#ff3072a0faf21ffd38f99acc9a0ddf7b5f07b948"
  integrity sha1-/zByoPryH/04+ZrMmg3fe18HuUg=
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    acorn "^8.8.2"
    commander "^2.20.0"
    source-map-support "~0.5.20"

text-table@^0.2.0:
  version "0.2.0"
  resolved "http://npm.htsc/text-table/download/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

through@^2.3.8:
  version "2.3.8"
  resolved "http://npm.htsc/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

thunky@^1.0.2:
  version "1.1.0"
  resolved "http://npm.htsc/thunky/download/thunky-1.1.0.tgz#5abaf714a9405db0504732bbccd2cedd9ef9537d"
  integrity sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=

tiny-each-async@2.0.3:
  version "2.0.3"
  resolved "http://npm.htsc/tiny-each-async/download/tiny-each-async-2.0.3.tgz#8ebbbfd6d6295f1370003fbb37162afe5a0a51d1"
  integrity sha1-jru/1tYpXxNwAD+7NxYq/loKUdE=

tmp-promise@^3.0.2:
  version "3.0.3"
  resolved "http://npm.htsc/tmp-promise/download/tmp-promise-3.0.3.tgz#60a1a1cc98c988674fcbfd23b6e3367bdeac4ce7"
  integrity sha1-YKGhzJjJiGdPy/0jtuM2e96sTOc=
  dependencies:
    tmp "^0.2.0"

tmp@^0.2.0:
  version "0.2.1"
  resolved "http://npm.htsc/tmp/download/tmp-0.2.1.tgz#8457fc3037dcf4719c251367a1af6500ee1ccf14"
  integrity sha1-hFf8MDfc9HGcJRNnoa9lAO4czxQ=
  dependencies:
    rimraf "^3.0.0"

tn1150@^0.1.0:
  version "0.1.0"
  resolved "http://npm.htsc/tn1150/download/tn1150-0.1.0.tgz#673503d24d56b87de8b8c77fee3fc0853d59a18d"
  integrity sha1-ZzUD0k1WuH3ouMd/7j/AhT1ZoY0=
  dependencies:
    unorm "^1.4.1"

to-data-view@^1.1.0:
  version "1.1.0"
  resolved "http://npm.htsc/to-data-view/download/to-data-view-1.1.0.tgz#08d6492b0b8deb9b29bdf1f61c23eadfa8994d00"
  integrity sha1-CNZJKwuN65spvfH2HCPq36iZTQA=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/to-fast-properties/download/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://npm.htsc/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/toidentifier/download/toidentifier-1.0.1.tgz#3be34321a88a820ed1bd80dfaa33e479fbb8dd35"
  integrity sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=

tr46@~0.0.3:
  version "0.0.3"
  resolved "http://npm.htsc/tr46/download/tr46-0.0.3.tgz#8184fd347dac9cdc185992f3a6622e14b9d9ab6a"
  integrity sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=

tree-kill@^1.2.2:
  version "1.2.2"
  resolved "http://npm.htsc/tree-kill/download/tree-kill-1.2.2.tgz#4ca09a9092c88b73a7cdc5e8a01b507b0790a0cc"
  integrity sha1-TKCakJLIi3OnzcXooBtQeweQoMw=

trim-newlines@^3.0.0:
  version "3.0.1"
  resolved "http://npm.htsc/trim-newlines/download/trim-newlines-3.0.1.tgz#260a5d962d8b752425b32f3a7db0dcacd176c144"
  integrity sha1-Jgpdli2LdSQlsy86fbDcrNF2wUQ=

trim-repeated@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/trim-repeated/download/trim-repeated-1.0.0.tgz#e3646a2ea4e891312bf7eace6cfb05380bc01c21"
  integrity sha1-42RqLqTokTEr9+rObPsFOAvAHCE=
  dependencies:
    escape-string-regexp "^1.0.2"

ts-loader@^9.2.2:
  version "9.4.3"
  resolved "http://npm.htsc/ts-loader/download/ts-loader-9.4.3.tgz#55cfa7c28dd82a2de968ae45c3adb75fb888b27e"
  integrity sha1-Vc+nwo3YKi3paK5Fw623X7iIsn4=
  dependencies:
    chalk "^4.1.0"
    enhanced-resolve "^5.0.0"
    micromatch "^4.0.0"
    semver "^7.3.4"

ts-node@^10.0.0:
  version "10.9.1"
  resolved "http://npm.htsc/ts-node/download/ts-node-10.9.1.tgz#e73de9102958af9e1f0b168a6ff320e25adcff4b"
  integrity sha1-5z3pEClYr54fCxaKb/Mg4lrc/0s=
  dependencies:
    "@cspotcode/source-map-support" "^0.8.0"
    "@tsconfig/node10" "^1.0.7"
    "@tsconfig/node12" "^1.0.7"
    "@tsconfig/node14" "^1.0.0"
    "@tsconfig/node16" "^1.0.2"
    acorn "^8.4.1"
    acorn-walk "^8.1.1"
    arg "^4.1.0"
    create-require "^1.1.0"
    diff "^4.0.1"
    make-error "^1.1.1"
    v8-compile-cache-lib "^3.0.1"
    yn "3.1.1"

tsconfig-paths@^3.14.1:
  version "3.14.2"
  resolved "http://npm.htsc/tsconfig-paths/download/tsconfig-paths-3.14.2.tgz#6e32f1f79412decd261f92d633a9dc1cfa99f088"
  integrity sha1-bjLx95QS3s0mH5LWM6ncHPqZ8Ig=
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@^1.8.1:
  version "1.14.1"
  resolved "http://npm.htsc/tslib/download/tslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^2.0.3, tslib@^2.1.0:
  version "2.6.0"
  resolved "http://npm.htsc/tslib/download/tslib-2.6.0.tgz#b295854684dbda164e181d259a22cd779dcd7bc3"
  integrity sha1-spWFRoTb2hZOGB0lmiLNd53Ne8M=

tsutils@^3.21.0:
  version "3.21.0"
  resolved "http://npm.htsc/tsutils/download/tsutils-3.21.0.tgz#b48717d394cea6c1e096983eed58e9d61715b623"
  integrity sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=
  dependencies:
    tslib "^1.8.1"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "http://npm.htsc/type-check/download/type-check-0.4.0.tgz#07b8203bfa7056c0657050e3ccd2c37730bab8f1"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.13.1:
  version "0.13.1"
  resolved "http://npm.htsc/type-fest/download/type-fest-0.13.1.tgz#0172cb5bce80b0bd542ea348db50c7e21834d934"
  integrity sha1-AXLLW86AsL1ULqNI21DH4hg02TQ=

type-fest@^0.18.0:
  version "0.18.1"
  resolved "http://npm.htsc/type-fest/download/type-fest-0.18.1.tgz#db4bc151a4a2cf4eebf9add5db75508db6cc841f"
  integrity sha1-20vBUaSiz07r+a3V23VQjbbMhB8=

type-fest@^0.20.2:
  version "0.20.2"
  resolved "http://npm.htsc/type-fest/download/type-fest-0.20.2.tgz#1bf207f4b28f91583666cb5fbd327887301cd5f4"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

type-fest@^0.21.3:
  version "0.21.3"
  resolved "http://npm.htsc/type-fest/download/type-fest-0.21.3.tgz#d260a24b0198436e133fa26a524a6d65fa3b2e37"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^0.6.0:
  version "0.6.0"
  resolved "http://npm.htsc/type-fest/download/type-fest-0.6.0.tgz#8d2a2370d3df886eb5c90ada1c5bf6188acf838b"
  integrity sha1-jSojcNPfiG61yQraHFv2GIrPg4s=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "http://npm.htsc/type-fest/download/type-fest-0.8.1.tgz#09e249ebde851d3b1e48d27c105444667f17b83d"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

type-is@~1.6.18:
  version "1.6.18"
  resolved "http://npm.htsc/type-is/download/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typed-array-length@^1.0.4:
  version "1.0.4"
  resolved "http://npm.htsc/typed-array-length/download/typed-array-length-1.0.4.tgz#89d83785e5c4098bec72e08b319651f0eac9c1bb"
  integrity sha1-idg3heXECYvscuCLMZZR8OrJwbs=
  dependencies:
    call-bind "^1.0.2"
    for-each "^0.3.3"
    is-typed-array "^1.1.9"

typed-emitter@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/typed-emitter/download/typed-emitter-2.1.0.tgz#ca78e3d8ef1476f228f548d62e04e3d4d3fd77fb"
  integrity sha1-ynjj2O8UdvIo9UjWLgTj1NP9d/s=
  optionalDependencies:
    rxjs "^7.5.2"

typescript@^4.0.0:
  version "4.9.5"
  resolved "http://npm.htsc/typescript/download/typescript-4.9.5.tgz#095979f9bcc0d09da324d58d03ce8f8374cbe65a"
  integrity sha1-CVl5+bzA0J2jJNWNA86Pg3TL5lo=

unbox-primitive@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/unbox-primitive/download/unbox-primitive-1.0.2.tgz#29032021057d5e6cdbd08c5129c226dff8ed6f9e"
  integrity sha1-KQMgIQV9Xmzb0IxRKcIm3/jtb54=
  dependencies:
    call-bind "^1.0.2"
    has-bigints "^1.0.2"
    has-symbols "^1.0.3"
    which-boxed-primitive "^1.0.2"

undici-types@~6.21.0:
  version "6.21.0"
  resolved "http://registry.npm.htsc/undici-types/-/undici-types-6.21.0.tgz#691d00af3909be93a7faa13be61b3a5b50ef12cb"
  integrity sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-2.0.0.tgz#301acdc525631670d39f6146e0e77ff6bbdebddc"
  integrity sha1-MBrNxSVjFnDTn2FG4Od/9rvevdw=

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz#54fd16e0ecb167cf04cf1f756bdcc92eba7976c3"
  integrity sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-2.1.0.tgz#cb5fffdcd16a05124f5a4b0bf7c3770208acbbe0"
  integrity sha1-y1//3NFqBRJPWksL98N3Agisu+A=

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.1.0"
  resolved "http://npm.htsc/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-2.1.0.tgz#43d41e3be698bd493ef911077c9b131f827e8ccd"
  integrity sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=

unique-filename@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/unique-filename/download/unique-filename-3.0.0.tgz#48ba7a5a16849f5080d26c760c86cf5cf05770ea"
  integrity sha1-SLp6WhaEn1CA0mx2DIbPXPBXcOo=
  dependencies:
    unique-slug "^4.0.0"

unique-slug@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/unique-slug/download/unique-slug-4.0.0.tgz#6bae6bb16be91351badd24cdce741f892a6532e3"
  integrity sha1-a65rsWvpE1G63STNznQfiSplMuM=
  dependencies:
    imurmurhash "^0.1.4"

unist-util-stringify-position@^2.0.0:
  version "2.0.3"
  resolved "http://npm.htsc/unist-util-stringify-position/download/unist-util-stringify-position-2.0.3.tgz#cce3bfa1cdf85ba7375d1d5b17bdc4cada9bd9da"
  integrity sha1-zOO/oc34W6c3XR1bF73Eytqb2do=
  dependencies:
    "@types/unist" "^2.0.2"

universalify@^0.1.0:
  version "0.1.2"
  resolved "http://npm.htsc/universalify/download/universalify-0.1.2.tgz#b646f69be3942dabcecc9d6639c80dc105efaa66"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

universalify@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/universalify/download/universalify-2.0.0.tgz#75a4984efedc4b08975c5aeb73f530d02df25717"
  integrity sha1-daSYTv7cSwiXXFrrc/Uw0C3yVxc=

unorm@^1.4.1:
  version "1.6.0"
  resolved "http://npm.htsc/unorm/download/unorm-1.6.0.tgz#029b289661fba714f1a9af439eb51d9b16c205af"
  integrity sha1-ApsolmH7pxTxqa9DnrUdmxbCBa8=

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/unpipe/download/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

update-browserslist-db@^1.0.11:
  version "1.0.11"
  resolved "http://npm.htsc/update-browserslist-db/download/update-browserslist-db-1.0.11.tgz#9a2a641ad2907ae7b3616506f4b977851db5b940"
  integrity sha1-mipkGtKQeuezYWUG9Ll3hR21uUA=
  dependencies:
    escalade "^3.1.1"
    picocolors "^1.0.0"

update-browserslist-db@^1.0.13:
  version "1.0.13"
  resolved "http://npm.htsc/update-browserslist-db/download/update-browserslist-db-1.0.13.tgz#3c5e4f5c083661bd38ef64b6328c26ed6c8248c4"
  integrity sha1-PF5PXAg2Yb0472S2Mowm7WyCSMQ=
  dependencies:
    escalade "^3.1.1"
    picocolors "^1.0.0"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "http://npm.htsc/uri-js/download/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

username@^5.1.0:
  version "5.1.0"
  resolved "http://npm.htsc/username/download/username-5.1.0.tgz#a7f9325adce2d0166448cdd55d4985b1360f2508"
  integrity sha1-p/kyWtzi0BZkSM3VXUmFsTYPJQg=
  dependencies:
    execa "^1.0.0"
    mem "^4.3.0"

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://npm.htsc/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

utila@~0.4:
  version "0.4.0"
  resolved "http://npm.htsc/utila/download/utila-0.4.0.tgz#8a16a05d445657a3aea5eecc5b12a4fa5379772c"
  integrity sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=

utils-merge@1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/utils-merge/download/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^8.3.2:
  version "8.3.2"
  resolved "http://npm.htsc/uuid/download/uuid-8.3.2.tgz#80d5b5ced271bb9af6c445f21a1a04c606cefbe2"
  integrity sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=

uuid@^9.0.0:
  version "9.0.1"
  resolved "http://registry.npm.htsc/uuid/-/uuid-9.0.1.tgz#e188d4c8853cc722220392c424cd637f32293f30"
  integrity sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==

v8-compile-cache-lib@^3.0.1:
  version "3.0.1"
  resolved "http://npm.htsc/v8-compile-cache-lib/download/v8-compile-cache-lib-3.0.1.tgz#6336e8d71965cb3d35a1bbb7868445a7c05264bf"
  integrity sha1-Yzbo1xllyz01obu3hoRFp8BSZL8=

v8-compile-cache@^2.0.3, v8-compile-cache@^2.3.0:
  version "2.3.0"
  resolved "http://npm.htsc/v8-compile-cache/download/v8-compile-cache-2.3.0.tgz#2de19618c66dc247dcfb6f99338035d8245a2cee"
  integrity sha1-LeGWGMZtwkfc+2+ZM4A12CRaLO4=

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "http://npm.htsc/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vary@~1.1.2:
  version "1.1.2"
  resolved "http://npm.htsc/vary/download/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

vue-eslint-parser@^8.0.1:
  version "8.3.0"
  resolved "http://npm.htsc/vue-eslint-parser/download/vue-eslint-parser-8.3.0.tgz#5d31129a1b3dd89c0069ca0a1c88f970c360bd0d"
  integrity sha1-XTESmhs92JwAacoKHIj5cMNgvQ0=
  dependencies:
    debug "^4.3.2"
    eslint-scope "^7.0.0"
    eslint-visitor-keys "^3.1.0"
    espree "^9.0.0"
    esquery "^1.4.0"
    lodash "^4.17.21"
    semver "^7.3.5"

wait-on@^7.0.1:
  version "7.0.1"
  resolved "http://npm.htsc/wait-on/download/wait-on-7.0.1.tgz#5cff9f8427e94f4deacbc2762e6b0a489b19eae9"
  integrity sha1-XP+fhCfpT03qy8J2LmsKSJsZ6uk=
  dependencies:
    axios "^0.27.2"
    joi "^17.7.0"
    lodash "^4.17.21"
    minimist "^1.2.7"
    rxjs "^7.8.0"

watchpack@^2.4.0:
  version "2.4.0"
  resolved "http://npm.htsc/watchpack/download/watchpack-2.4.0.tgz#fa33032374962c78113f93c7f2fb4c54c9862a5d"
  integrity sha1-+jMDI3SWLHgRP5PH8vtMVMmGKl0=
  dependencies:
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.1.2"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "http://npm.htsc/wbuf/download/wbuf-1.7.3.tgz#c1d8d149316d3ea852848895cb6a0bfe887b87df"
  integrity sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=
  dependencies:
    minimalistic-assert "^1.0.0"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/wcwidth/download/wcwidth-1.0.1.tgz#f0b0dcf915bc5ff1528afadb2c0e17b532da2fe8"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "http://npm.htsc/webidl-conversions/download/webidl-conversions-3.0.1.tgz#24534275e2a7bc6be7bc86611cc16ae0a5654871"
  integrity sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=

webpack-dev-middleware@^5.3.1:
  version "5.3.3"
  resolved "http://npm.htsc/webpack-dev-middleware/download/webpack-dev-middleware-5.3.3.tgz#efae67c2793908e7311f1d9b06f2a08dcc97e51f"
  integrity sha1-765nwnk5COcxHx2bBvKgjcyX5R8=
  dependencies:
    colorette "^2.0.10"
    memfs "^3.4.3"
    mime-types "^2.1.31"
    range-parser "^1.2.1"
    schema-utils "^4.0.0"

webpack-dev-server@^4.0.0:
  version "4.15.1"
  resolved "http://npm.htsc/webpack-dev-server/download/webpack-dev-server-4.15.1.tgz#8944b29c12760b3a45bdaa70799b17cb91b03df7"
  integrity sha1-iUSynBJ2CzpFvapweZsXy5GwPfc=
  dependencies:
    "@types/bonjour" "^3.5.9"
    "@types/connect-history-api-fallback" "^1.3.5"
    "@types/express" "^4.17.13"
    "@types/serve-index" "^1.9.1"
    "@types/serve-static" "^1.13.10"
    "@types/sockjs" "^0.3.33"
    "@types/ws" "^8.5.5"
    ansi-html-community "^0.0.8"
    bonjour-service "^1.0.11"
    chokidar "^3.5.3"
    colorette "^2.0.10"
    compression "^1.7.4"
    connect-history-api-fallback "^2.0.0"
    default-gateway "^6.0.3"
    express "^4.17.3"
    graceful-fs "^4.2.6"
    html-entities "^2.3.2"
    http-proxy-middleware "^2.0.3"
    ipaddr.js "^2.0.1"
    launch-editor "^2.6.0"
    open "^8.0.9"
    p-retry "^4.5.0"
    rimraf "^3.0.2"
    schema-utils "^4.0.0"
    selfsigned "^2.1.1"
    serve-index "^1.9.1"
    sockjs "^0.3.24"
    spdy "^4.0.2"
    webpack-dev-middleware "^5.3.1"
    ws "^8.13.0"

webpack-merge@^5.7.3:
  version "5.9.0"
  resolved "http://npm.htsc/webpack-merge/download/webpack-merge-5.9.0.tgz#dc160a1c4cf512ceca515cc231669e9ddb133826"
  integrity sha1-3BYKHEz1Es7KUVzCMWaendsTOCY=
  dependencies:
    clone-deep "^4.0.1"
    wildcard "^2.0.0"

webpack-sources@^3.2.3:
  version "3.2.3"
  resolved "http://npm.htsc/webpack-sources/download/webpack-sources-3.2.3.tgz#2d4daab8451fd4b240cc27055ff6a0c2ccea0cde"
  integrity sha1-LU2quEUf1LJAzCcFX/agwszqDN4=

webpack@^5.69.1:
  version "5.88.0"
  resolved "http://npm.htsc/webpack/download/webpack-5.88.0.tgz#a07aa2f8e7a64a8f1cec0c6c2e180e3cb34440c8"
  integrity sha1-oHqi+OemSo8c7AxsLhgOPLNEQMg=
  dependencies:
    "@types/eslint-scope" "^3.7.3"
    "@types/estree" "^1.0.0"
    "@webassemblyjs/ast" "^1.11.5"
    "@webassemblyjs/wasm-edit" "^1.11.5"
    "@webassemblyjs/wasm-parser" "^1.11.5"
    acorn "^8.7.1"
    acorn-import-assertions "^1.9.0"
    browserslist "^4.14.5"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^5.15.0"
    es-module-lexer "^1.2.1"
    eslint-scope "5.1.1"
    events "^3.2.0"
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.2.9"
    json-parse-even-better-errors "^2.3.1"
    loader-runner "^4.2.0"
    mime-types "^2.1.27"
    neo-async "^2.6.2"
    schema-utils "^3.2.0"
    tapable "^2.1.1"
    terser-webpack-plugin "^5.3.7"
    watchpack "^2.4.0"
    webpack-sources "^3.2.3"

websocket-driver@>=0.5.1, websocket-driver@^0.7.4:
  version "0.7.4"
  resolved "http://npm.htsc/websocket-driver/download/websocket-driver-0.7.4.tgz#89ad5295bbf64b480abcba31e4953aca706f5760"
  integrity sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "http://npm.htsc/websocket-extensions/download/websocket-extensions-0.1.4.tgz#7f8473bc839dfd87608adb95d7eb075211578a42"
  integrity sha1-f4RzvIOd/YdgituV1+sHUhFXikI=

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "http://npm.htsc/whatwg-url/download/whatwg-url-5.0.0.tgz#966454e8765462e37644d3626f6742ce8b70965d"
  integrity sha1-lmRU6HZUYuN2RNNib2dCzotwll0=
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/which-boxed-primitive/download/which-boxed-primitive-1.0.2.tgz#13757bc89b209b049fe5d86430e21cf40a89a8e6"
  integrity sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY=
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which-module@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/which-module/download/which-module-2.0.0.tgz#d9ef07dce77b9902b8a3a8fa4b31c3e3f7e6e87a"
  integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=

which-typed-array@^1.1.9:
  version "1.1.9"
  resolved "http://npm.htsc/which-typed-array/download/which-typed-array-1.1.9.tgz#307cf898025848cf995e795e8423c7f337efbde6"
  integrity sha1-MHz4mAJYSM+ZXnlehCPH8zfvveY=
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.0"
    is-typed-array "^1.1.10"

which@^1.2.14, which@^1.2.9, which@^1.3.1:
  version "1.3.1"
  resolved "http://npm.htsc/which/download/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

which@^2.0.1, which@^2.0.2:
  version "2.0.2"
  resolved "http://npm.htsc/which/download/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.5:
  version "1.1.5"
  resolved "http://npm.htsc/wide-align/download/wide-align-1.1.5.tgz#df1d4c206854369ecf3c9a4898f1b23fbd9d15d3"
  integrity sha1-3x1MIGhUNp7PPJpImPGyP72dFdM=
  dependencies:
    string-width "^1.0.2 || 2 || 3 || 4"

wildcard@^2.0.0:
  version "2.0.1"
  resolved "http://npm.htsc/wildcard/download/wildcard-2.0.1.tgz#5ab10d02487198954836b6349f74fff961e10f67"
  integrity sha1-WrENAkhxmJVINrY0n3T/+WHhD2c=

word-wrap@^1.2.3:
  version "1.2.3"
  resolved "http://npm.htsc/word-wrap/download/word-wrap-1.2.3.tgz#610636f6b1f703891bd34771ccb17fb93b47079c"
  integrity sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  resolved "http://npm.htsc/wrap-ansi/download/wrap-ansi-7.0.0.tgz#67e145cff510a6a6984bdf1152911d69d2eb9e43"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "http://npm.htsc/wrap-ansi/download/wrap-ansi-6.2.0.tgz#e9393ba07102e6c91a3b221478f0257cd2856e53"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "http://npm.htsc/wrap-ansi/download/wrap-ansi-7.0.0.tgz#67e145cff510a6a6984bdf1152911d69d2eb9e43"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "http://npm.htsc/wrap-ansi/download/wrap-ansi-8.1.0.tgz#56dc22368ee570face1b49819975d9b9a5ead214"
  integrity sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"
  resolved "http://npm.htsc/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^4.0.2:
  version "4.0.2"
  resolved "http://npm.htsc/write-file-atomic/download/write-file-atomic-4.0.2.tgz#a9df01ae5b77858a027fd2e80768ee433555fcfd"
  integrity sha1-qd8Brlt3hYoCf9LoB2juQzVV/P0=
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^3.0.7"

ws@^7.4.6:
  version "7.5.9"
  resolved "http://npm.htsc/ws/download/ws-7.5.9.tgz#54fa7db29f4c7cec68b1ddd3a89de099942bb591"
  integrity sha1-VPp9sp9MfOxosd3TqJ3gmZQrtZE=

ws@^8.13.0:
  version "8.13.0"
  resolved "http://npm.htsc/ws/download/ws-8.13.0.tgz#9a9fb92f93cf41512a0735c8f4dd09b8a1211cd0"
  integrity sha1-mp+5L5PPQVEqBzXI9N0JuKEhHNA=

xmlbuilder@^15.1.1:
  version "15.1.1"
  resolved "http://npm.htsc/xmlbuilder/download/xmlbuilder-15.1.1.tgz#9dcdce49eea66d8d10b42cae94a79c3c8d0c2ec5"
  integrity sha1-nc3OSe6mbY0QtCyulKecPI0MLsU=

xtend@^4.0.0:
  version "4.0.2"
  resolved "http://npm.htsc/xtend/download/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

xterm-addon-fit@^0.5.0:
  version "0.5.0"
  resolved "http://npm.htsc/xterm-addon-fit/download/xterm-addon-fit-0.5.0.tgz#2d51b983b786a97dcd6cde805e700c7f913bc596"
  integrity sha1-LVG5g7eGqX3NbN6AXnAMf5E7xZY=

xterm-addon-search@^0.8.0:
  version "0.8.2"
  resolved "http://npm.htsc/xterm-addon-search/download/xterm-addon-search-0.8.2.tgz#be7aa74d5ff12c901707c6ff674229f214318032"
  integrity sha1-vnqnTV/xLJAXB8b/Z0Ip8hQxgDI=

xterm@^4.9.0:
  version "4.19.0"
  resolved "http://npm.htsc/xterm/download/xterm-4.19.0.tgz#c0f9d09cd61de1d658f43ca75f992197add9ef6d"
  integrity sha1-wPnQnNYd4dZY9DynX5khl63Z720=

y18n@^4.0.0:
  version "4.0.3"
  resolved "http://npm.htsc/y18n/download/y18n-4.0.3.tgz#b5f259c82cd6e336921efd7bfd8bf560de9eeedf"
  integrity sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8=

y18n@^5.0.5:
  version "5.0.8"
  resolved "http://npm.htsc/y18n/download/y18n-5.0.8.tgz#7f4934d0f7ca8c56f95314939ddcd2dd91ce1d55"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^2.1.2:
  version "2.1.2"
  resolved "http://npm.htsc/yallist/download/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"
  integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=

yallist@^3.0.2:
  version "3.1.1"
  resolved "http://npm.htsc/yallist/download/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yallist@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/yallist/download/yallist-4.0.0.tgz#9bb92790d9c0effec63be73519e11a35019a3a72"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yaml@^1.10.0:
  version "1.10.2"
  resolved "http://npm.htsc/yaml/download/yaml-1.10.2.tgz#2301c5ffbf12b467de8da2333a459e29e7920e4b"
  integrity sha1-IwHF/78StGfejaIzOkWeKeeSDks=

yargs-parser@^18.1.2:
  version "18.1.3"
  resolved "http://npm.htsc/yargs-parser/download/yargs-parser-18.1.3.tgz#be68c4975c6b2abf469236b0c870362fab09a7b0"
  integrity sha1-vmjEl1xrKr9GkjawyHA2L6sJp7A=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^20.2.2, yargs-parser@^20.2.3:
  version "20.2.9"
  resolved "http://npm.htsc/yargs-parser/download/yargs-parser-20.2.9.tgz#2eb7dc3b0289718fc295f362753845c41a0c94ee"
  integrity sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=

yargs-parser@^21.1.1:
  version "21.1.1"
  resolved "http://npm.htsc/yargs-parser/download/yargs-parser-21.1.1.tgz#9096bceebf990d21bb31fa9516e0ede294a77d35"
  integrity sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=

yargs@^15.0.1:
  version "15.4.1"
  resolved "http://npm.htsc/yargs/download/yargs-15.4.1.tgz#0d87a16de01aee9d8bec2bfbf74f67851730f4f8"
  integrity sha1-DYehbeAa7p2L7Cv7909nhRcw9Pg=
  dependencies:
    cliui "^6.0.0"
    decamelize "^1.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.2"

yargs@^16.0.2:
  version "16.2.0"
  resolved "http://npm.htsc/yargs/download/yargs-16.2.0.tgz#1c82bf0f6b6a66eafce7ef30e376f49a12477f66"
  integrity sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yargs@^17.0.1, yargs@^17.7.2:
  version "17.7.2"
  resolved "http://npm.htsc/yargs/download/yargs-17.7.2.tgz#991df39aca675a192b816e1e0363f9d75d2aa269"
  integrity sha1-mR3zmspnWhkrgW4eA2P5110qomk=
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yarn-or-npm@^3.0.1:
  version "3.0.1"
  resolved "http://npm.htsc/yarn-or-npm/download/yarn-or-npm-3.0.1.tgz#6336eea4dff7e23e226acc98c1a8ada17a1b8666"
  integrity sha1-YzbupN/34j4iasyYwaitoXobhmY=
  dependencies:
    cross-spawn "^6.0.5"
    pkg-dir "^4.2.0"

yauzl@^2.10.0:
  version "2.10.0"
  resolved "http://npm.htsc/yauzl/download/yauzl-2.10.0.tgz#c7eb17c93e112cb1086fa6d8e51fb0667b79a5f9"
  integrity sha1-x+sXyT4RLLEIb6bY5R+wZnt5pfk=
  dependencies:
    buffer-crc32 "~0.2.3"
    fd-slicer "~1.1.0"

yn@3.1.1:
  version "3.1.1"
  resolved "http://npm.htsc/yn/download/yn-3.1.1.tgz#1e87401a09d767c1d5eab26a6e4c185182d2eb50"
  integrity sha1-HodAGgnXZ8HV6rJqbkwYUYLS61A=

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "http://npm.htsc/yocto-queue/download/yocto-queue-0.1.0.tgz#0294eb3dee05028d31ee1a5fa2c556a6aaf10a1b"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=

yorkie@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/yorkie/download/yorkie-2.0.0.tgz#92411912d435214e12c51c2ae1093e54b6bb83d9"
  integrity sha1-kkEZEtQ1IU4SxRwq4Qk+VLa7g9k=
  dependencies:
    execa "^0.8.0"
    is-ci "^1.0.10"
    normalize-path "^1.0.0"
    strip-indent "^2.0.0"
