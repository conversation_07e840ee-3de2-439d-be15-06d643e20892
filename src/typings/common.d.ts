type TSUCCESS_FLAG = 'SUCCESS';
type TFAILED_FLAG = 'FAILED';
type TCANCELED_FLAG = 'CANCELED';

type TCOMPLETE_FLAG = 'COMPLETE';
type TPAUSED_FLAG = 'PAUSED'; // 暂停
type TRUNNING_FLAG = 'RUNNING'; // 正在上传
type TWAITING_FLAG = 'WAITING'; // 初始状态，等待上传

type TADD_FLAG = 'ADD';
type TDELETE_FLAG = 'DELETE';

type TFunReturnFlag = TSUCCESS_FLAG | TFAILED_FLAG | TCANCELED_FLAG;
type TFunReturnSimpFlag = TSUCCESS_FLAG | TFAILED_FLAG;

interface ICommonFunReturn {
  code: TFunReturnFlag;
  errMsg?: string;
}

interface ICommonFunSimpReturn {
  code: TFunReturnSimpFlag;
  errMsg?: string;
}

type TTaskStatus =
  | TFunReturnSimpFlag
  | TPAUSED_FLAG
  | TRUNNING_FLAG
  | TWAITING_FLAG;

type TFilePathType = 'ent' | 'self'; // ent：企业空间； self：个人空间
