/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-depth */
import {
  CANCELED_CON,
  FAILED_CON,
  PAUSED_CON,
  SUCCESS_CON,
} from '@common/constant';
import { getErrorMsg } from '@utils/stringUtils';
import moment from 'moment';
import { TRunTaskReturn } from '@lightenModule/taskManager';
import { LINKFLOW_ON_LC_COMPLETE } from '@lightenSDK/constant/localCollection';
import { LINKFLOW_MESSAGE_WARN } from '@lightenSDK/constant/global';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { getUserLocalCollectionStore } from '@globalState/localStore/localCollectionStore';
import { getCollectionStatus } from './utils/localCollectionApi';
import { ILocalCollectionTask, IFileCollectionTarget } from '.';
import { EnumColleItemStatus } from './utils/enum';
import { uploadCollectionFile } from './uploadCollectionFile';

export const runLocalCollectionTask = (
  vTask: ILocalCollectionTask,
  localCollectionAbortControllers: Map<string, AbortController>
) => {
  return new Promise<TRunTaskReturn<string, ILocalCollectionTask>>(
    // eslint-disable-next-line max-statements
    async (resolve) => {
      try {
        // 新建中断器
        const curController = new AbortController();
        localCollectionAbortControllers.set(vTask.key, curController);
        const uploadList = formatUploadList(vTask);
        // 若上传队列为空，后台会自动将任务设置为已完成状态

        // 记录任务下每个文件的下载结果
        const resList: TFunReturnFlag[] = [];
        let taskErrMsg = '';
        for (const uploadItem of uploadList) {
          // 若当前文件已上传过，则进入下次循环
          if (uploadItem.status === 'SUCCESS') {
            continue;
          }
          const uploadRes = await uploadCollectionFile(
            uploadItem,
            curController
          );
          if (uploadRes.code === SUCCESS_CON) {
            updateUploadStatus(vTask, uploadItem, 'SUCCESS');
            resList.push(SUCCESS_CON);
          } else if (uploadRes.code === CANCELED_CON) {
            // 文件上传取消或暂停，则退出循环
            updateUploadStatus(vTask, uploadItem, 'PAUSED');
            resList.push(uploadRes.code);
            taskErrMsg = uploadRes.errMsg;
            break;
          } else {
            // 文件上传失败
            updateUploadStatus(vTask, uploadItem, 'FAILED');
            resList.push(uploadRes.code);
            taskErrMsg = uploadRes.errMsg;
          }
        }

        localCollectionAbortControllers.delete(vTask.key);
        // 任务运行完成，写入状态
        if (resList.includes(CANCELED_CON)) {
          // 暂停
          const resTask: ILocalCollectionTask = {
            ...vTask,
            errMsg: taskErrMsg,
            status: PAUSED_CON,
          };
          resolve({ code: PAUSED_CON, key: vTask.key, task: resTask });
        } else if (resList.includes(FAILED_CON) || resList.length === 0) {
          // 失败
          const resTask: ILocalCollectionTask = {
            ...vTask,
            errMsg: taskErrMsg,
            status: FAILED_CON,
          };
          notification(vTask.key);
          resolve({ code: FAILED_CON, key: vTask.key, task: resTask });
        } else {
          // 成功
          const resTask: ILocalCollectionTask = {
            ...vTask,
            errMsg: taskErrMsg,
            status: SUCCESS_CON,
          };
          vTask.completeTime = moment().format('YYYY_MM_DD_hh_mm_ss_SSS');
          notification(vTask.key);
          resolve({
            code: SUCCESS_CON,
            key: vTask.key,
            task: resTask,
          });
        }
      } catch (error) {
        const resTask: ILocalCollectionTask = {
          ...vTask,
          errMsg: getErrorMsg(error),
          status: FAILED_CON,
        };
        notification(vTask.key);
        resolve({ code: FAILED_CON, key: vTask.key, task: resTask });
      }
    }
  );
};

const formatUploadList = (vTask: ILocalCollectionTask) => {
  const uploadList: IFileCollectionTarget[] = [];
  const { collectionId, fileTargetList: data } = vTask;
  // 做下过滤，只处理后台返回上传路径targetCatalogPath
  for (const taskItem of data) {
    if (
      (taskItem.targetCatalogPath && taskItem.targetCatalogPath.length > 0) ||
      taskItem.pathId
    ) {
      // 把本地文件路径找到
      const relatedItem = vTask.fileInfoList.find((item) => {
        if (taskItem.parentPath) {
          return (
            taskItem.parentPath === item.parentPath &&
            taskItem.fileName === item.fileName
          );
        } else {
          return taskItem.fileName === item.fileName;
        }
      });
      if (relatedItem?.localPath && relatedItem.localPath.length > 0) {
        taskItem.collectionId = collectionId;
        taskItem.localPath = relatedItem.localPath;
        uploadList.push(taskItem);
      }
    }
  }
  return uploadList;
};

const updateUploadStatus = (vTask, uploadItem, STATUS) => {
  const { fileTargetList } = vTask;
  fileTargetList.forEach((item) => {
    if (item.id === uploadItem.id) {
      uploadItem.status = STATUS;
    }
  });
  const lcStore = getUserLocalCollectionStore();
  lcStore.read();
  lcStore.data.processingList.forEach((task) => {
    if (task.key === vTask.key) {
      task.fileTargetList = fileTargetList;
    }
  });
  lcStore.write();
};

/**
 * 本地归集任务完成，发送通知
 * @param key 任务key
 */
const notification = async (key: string) => {
  const collectionId = Number(key);
  // 使用setTimeout是因为后端的任务状态可能暂未更新
  let timer = setTimeout(async () => {
    const collectionStatus = await getCollectionStatus(collectionId);
    if (!collectionStatus) {
      return;
    }
    if (collectionStatus.status === EnumColleItemStatus.Collected) {
      mainIPC.send({
        id: LINKFLOW_ON_LC_COMPLETE,
        params: {
          message: '归集任务已完成',
          description: `本次归集成功文件数为${collectionStatus.success}，归集失败文件数为${collectionStatus.failed}`,
        },
      });
    } else if (collectionStatus.status === EnumColleItemStatus.Canceled) {
      mainIPC.send({
        id: LINKFLOW_MESSAGE_WARN,
        params: {
          text: '归集任务已取消',
        },
      });
    }
    clearTimeout(timer);
    timer = null;
  }, 1000);
};
