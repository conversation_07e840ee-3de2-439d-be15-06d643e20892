import { SUCCESS_CON } from '@common/constant';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { CTaskManager } from '@lightenModule/taskManager';
import { LINKFLOW_MESSAGE_WARN } from '@lightenSDK/constant/global';
import { getAllFilesFromDir } from '@utils/folderUtils';
import { BrowserWindow, dialog } from 'electron';
import path from 'path';
import { LINKFLOW_ON_CREATE_LOCAL_COLLECTION_CALLBACK } from '@lightenSDK/constant/localCollection';
import { ICollectionFileInfo, ILocalCollectionTask } from '.';
import { postLocalCollection } from './utils/localCollectionApi';
import { addPollingTaskAndRun } from './PollingTaskBeforeRun';

export const createLocalCollectionTask = (
  msg: any,
  localCollectionManager: CTaskManager<string, ILocalCollectionTask>
) => {
  const renderWindow = BrowserWindow.fromWebContents(
    mainIPC.getMainWindowWebContents()
  );
  const { type } = msg;
  const isFolder = type === '1';
  // 弹出文件选择框
  dialog
    .showOpenDialog(renderWindow, {
      title: `选择要归集的${isFolder ? '文件夹' : '文件'}`,
      properties: isFolder
        ? ['openDirectory', 'multiSelections']
        : ['openFile', 'multiSelections'],
    })
    .then(async ({ filePaths, canceled }) => {
      if (canceled) {
        return;
      }
      if (!filePaths) {
        mainIPC.send({
          id: LINKFLOW_MESSAGE_WARN,
          params: {
            text: '用户没有选择文件或者文件目录过长！',
          },
        });
        return;
      }
      handleFilePaths(msg, isFolder, filePaths, localCollectionManager);
    });
};

const handleFilePaths = async (
  msg: any,
  isFolder: boolean,
  filePaths: string[],
  localCollectionManager: CTaskManager<string, ILocalCollectionTask>
) => {
  let fileInfoList: ICollectionFileInfo[] = [];
  for (const aFilePath of filePaths) {
    if (isFolder) {
      const aRes = await getAllFilesFromDir(aFilePath);
      if (aRes.code === SUCCESS_CON) {
        fileInfoList = fileInfoList.concat(
          aRes.fileList.map((aFile) => ({
            localPath: aFile.path,
            fileName: aFile.name,
            parentPath: path.basename(path.dirname(aFile.path)),
          }))
        );
      } else {
        mainIPC.send({
          id: LINKFLOW_MESSAGE_WARN,
          params: {
            text: aRes.errorMsg,
          },
        });
        return;
      }
    } else {
      const aFileInfo: ICollectionFileInfo = {
        localPath: aFilePath,
        fileName: path.basename(aFilePath),
      };
      fileInfoList.push(aFileInfo);
    }
  }

  if (fileInfoList.length > 0) {
    const collectionId = await postLocalCollection({
      ...msg,
      fileInfoList,
    });
    const newTask = {
      key: collectionId.toString(),
      collectionId,
      ...msg,
      fileInfoList,
    };
    addPollingTaskAndRun(newTask, localCollectionManager);

    mainIPC.send({
      id: LINKFLOW_ON_CREATE_LOCAL_COLLECTION_CALLBACK,
    });
  } else {
    mainIPC.send({
      id: LINKFLOW_MESSAGE_WARN,
      params: {
        text: '所选文件夹下没有文件',
      },
    });
  }
};
