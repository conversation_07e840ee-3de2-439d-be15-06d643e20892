import { CANCELED_CON, PAUSED_CON } from '@common/constant';
import { LowSync } from 'lowdb/lib';
import { COVER_CON } from '@htElectronSDK/constant/ipc';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { CTaskManager } from '@lightenModule/taskManager';
import {
  getUserLocalCollectionStore,
  IUserLocalCollectionStore,
} from '@globalState/localStore/localCollectionStore';
import { LINKFLOW_MESSAGE_WARN } from '@lightenSDK/constant/global';
import LOG from '@htElectronSDK/main/log';
import {
  LINKFLOW_CREATE_LOCAL_COLLECTION,
  LINKFLOW_CANCEL_LOCAL_COLLECTION,
  LINKFLOW_GET_LOCAL_COLLECTION_DETAIL,
} from '@lightenSDK/constant/localCollection';
import { runLocalCollectionTask } from './runLocalCollectionTask';
import { createLocalCollectionTask } from './createLocalCollectionTask';
import { lCTaskChangeCallBack } from './utils/lCTaskChangeCallBack';
import { EnumColleItemStatus, EnumColleItemType } from './utils/enum';
import {
  cancelCollectionRecord,
  getCollectionDetail,
  getCollectionStatus,
} from './utils/localCollectionApi';
import {
  pausePollingAllTask,
  startPollingAllTask,
} from './PollingTaskBeforeRun';

export interface ICollectionFileInfo {
  localPath: string;
  fileName: string;
  parentPath?: string;
}
export interface IFileCollectionTarget {
  id: number;
  fileName: string;
  parentPath: string;
  catalogId: string;
  pathId: number;
  collectionId?: number; // 归集任务id，服务器不会返回，仅用于本地数据
  targetCatalogPath?: string; // 匹配的底稿目录网盘全路径，服务器返回
  localPath?: string; // 本地路径，本地传
  status?: TTaskStatus; // 任务状态
}

export interface ILocalCollectionTask {
  key: string; // collectionId的string格式
  collectionId: number; // 后端返回的归集id
  status: TTaskStatus; // 任务状态
  projectId: number;
  projectName: string;
  stageCode: string; // 所属阶段信息
  catalogId: string;
  spaceName: string; // 文件来源：‘本地文件’
  fileInfoList: ICollectionFileInfo[];
  fileTargetList: IFileCollectionTarget[];
  type: EnumColleItemType; // 归集类型（0-文件，1-文件夹）
  pollTimer?: number; // 轮询定时器
  createTime: string; // 创建时间
  creator?: string; // 创建人
  errMsg?: string; // 错误信息
  completeTime?: string; // 完成时间
}

const LOCAL_COLLECTION_CONCURRENT_TASK_NUM = 1;

const localCollectionAbortControllers = new Map<string, AbortController>();
export const localCollectionPollingTimers = new Map<string, NodeJS.Timer>();

const taskEqualFun = (
  vTask1: ILocalCollectionTask,
  vTask2: ILocalCollectionTask
) => {
  if (
    vTask1?.collectionId &&
    vTask1?.createTime &&
    vTask2?.collectionId &&
    vTask2?.createTime
  ) {
    return (
      vTask1.collectionId === vTask2.collectionId &&
      vTask1.createTime === vTask2.createTime
    );
  }
  return false;
};

const initLCTaskManager = (lcStore: LowSync<IUserLocalCollectionStore>) => {
  lcStore.read();
  lcStore.data.waitingList?.forEach((item) => {
    lcStore.data.pauseList?.push({
      ...item,
      status: PAUSED_CON,
    });
  });
  lcStore.data.processingList?.forEach((item) => {
    lcStore.data.pauseList?.push({
      ...item,
      status: PAUSED_CON,
    });
  });
  lcStore.data.waitingList = [];
  lcStore.data.processingList = [];
  lcStore.write();
  const initPauseList = new Map<string, ILocalCollectionTask>();
  lcStore.data.pauseList?.forEach((item: ILocalCollectionTask) => {
    initPauseList.set(item.key, item);
  });

  return new CTaskManager<string, ILocalCollectionTask>(
    LOCAL_COLLECTION_CONCURRENT_TASK_NUM,
    runLocalCollectionTask,
    initPauseList,
    lcStore.data.completeList,
    localCollectionAbortControllers,
    lCTaskChangeCallBack,
    taskEqualFun
  );
};

export const initLightenLocalCollecter = () => {
  const lcStore = getUserLocalCollectionStore();
  const localCollectionManager = initLCTaskManager(lcStore);
  // 启动轮询和上传任务
  startPollingAllTask(localCollectionManager);
  if (lcStore.data.pauseList?.length > 0) {
    for (const task of lcStore.data.pauseList) {
      localCollectionManager.addTaskToRun(task.key);
    }
  }

  mainIPC.addListener(
    LINKFLOW_CREATE_LOCAL_COLLECTION,
    (msg: any) => {
      createLocalCollectionTask(msg, localCollectionManager);
    },
    COVER_CON
  );
  mainIPC.addListener(
    LINKFLOW_CANCEL_LOCAL_COLLECTION,
    async (key: number) => {
      // 找到任务，abort中断,改变任务状态；向后端发送消息；向主进程发送关闭成功消息
      const controller = localCollectionAbortControllers.get(key.toString());
      const res = await reportTaskCanceled(key);
      if (controller) {
        controller.abort(CANCELED_CON);
        // controller.abort会把任务添加到暂停队列，所以移除做了延时处理
        setTimeout(() => {
          localCollectionManager.removeFromTasks(key.toString());
        }, 200);
      }
      if (res) {
        return true;
      } else {
        return false;
      }
    },
    COVER_CON
  );
  mainIPC.addListener(
    LINKFLOW_GET_LOCAL_COLLECTION_DETAIL,
    async (params: any) => {
      const res = await getCollectionDetail(params);
      // 当用户删除本地json文件，或该归集任务在其余PC端启动时，该任务一直显示归集中
      setTimeout(async () => {
        const collectionStatus = await getCollectionStatus(params.collectionId);
        if (
          collectionStatus.status === EnumColleItemStatus.Collecting &&
          !isInStore(params.collectionId)
        ) {
          mainIPC.send({
            id: LINKFLOW_MESSAGE_WARN,
            params: {
              text: '该归集任务在其余PC端启动，或本地json文件已删除',
            },
          });
        }
      }, 200);
      return res;
    }
  );
};

export const pauseAllLocalCollection = () => {
  pausePollingAllTask();
  for (const controller of localCollectionAbortControllers.values()) {
    controller.abort(PAUSED_CON);
  }
  localCollectionAbortControllers.clear();
};

export const isCollecting = (collectionId) => {
  const lcStore = getUserLocalCollectionStore();
  const flag = lcStore.data.processingList.find(
    (item) => item.key === collectionId.toString()
  );
  return flag;
};

// 向后端上报归集任务取消
const reportTaskCanceled = async (collectionId) => {
  const { flag: success, errMsg } = await cancelCollectionRecord(collectionId);
  if (success) {
    return true;
  } else {
    // 打日志，通知渲染进程取消失败
    LOG.error(errMsg);
    mainIPC.send({
      id: LINKFLOW_MESSAGE_WARN,
      params: {
        text: '取消任务失败',
      },
    });
    return false;
  }
};

// 查询本地json文件中是否含有该任务
const isInStore = (collectionId) => {
  const lcStore = getUserLocalCollectionStore();
  lcStore.read();
  if (
    lcStore.data.pollingList.find(
      (item) => item.collectionId === collectionId
    ) ||
    lcStore.data.waitingList.find(
      (item) => item.collectionId === collectionId
    ) ||
    lcStore.data.processingList.find(
      (item) => item.collectionId === collectionId
    ) ||
    lcStore.data.pauseList.find((item) => item.collectionId === collectionId) ||
    lcStore.data.completeList.find((item) => item.collectionId === collectionId)
  ) {
    return true;
  } else {
    return false;
  }
};
