import { FAILED_CON } from '@common/constant';
import { IFileCollectionTarget } from '.';
import { putCollectionDetailItem } from './utils/localCollectionApi';
import { EnumColleDetailStatus } from './utils/enum';
import { uploadFileAtOnce } from '../uploadFileAtOnce';
import {
  isMaxCollectionFileSizeExceeded,
  checkSpecialCharacters,
} from './utils/utils';

export const uploadCollectionFile = async (
  uploadItem: IFileCollectionTarget,
  controller
): Promise<{ code: TFunReturnFlag; errMsg?: string }> => {
  // 检查文件大小
  if (await isMaxCollectionFileSizeExceeded(uploadItem.localPath)) {
    const errMsg = '单个文件最大不可超过2G';
    handleUpload({
      success: false,
      err: errMsg,
      uploadItem,
    });
    return { code: FAILED_CON, errMsg };
  }
  // 检查文件名
  if (checkSpecialCharacters(uploadItem.fileName)) {
    const errMsg =
      '文件名称仅支持:中文英文数字 ～.@#$%&()（）【】[]《》：+ =。！!，,_—-';
    handleUpload({
      success: false,
      err: errMsg,
      uploadItem,
    });
    return { code: FAILED_CON, errMsg };
  }
  // 启动上传
  const result = await uploadFileAtOnce(
    {
      filePath: uploadItem.localPath,
      serverPath: uploadItem?.targetCatalogPath || uploadItem?.pathId,
      controller,
    },
    {
      onFail: (errMsg) => {
        // 例子：err={\"code\":30030,\"message\":\"底稿目录被锁定，无法对底稿文件进行增删改\",\"t\":null} "
        let failReason = '上传失败，原因未知';
        if (errMsg && errMsg.length > 0) {
          try {
            const errMsgObj = JSON.parse(errMsg);
            if (errMsgObj.message && errMsgObj.message.length > 0) {
              failReason = errMsgObj.message;
            } else {
              failReason = errMsg;
            }
          } catch (error) {
            failReason = errMsg; // 如果errMsg是string,JSON.parse会出错
          }
        }
        handleUpload({
          success: false,
          err: failReason,
          uploadItem,
        });
      },
      onProgress: () => {
        /* do nothing */
      },
      onSuccess: () => {
        handleUpload({
          success: true,
          err: '',
          uploadItem,
        });
      },
    }
  );
  return { code: result };
};

// 向服务端上报单个文件上传的结果（成功/失败）
const handleUpload = async (options: {
  success: boolean;
  err: string;
  uploadItem: IFileCollectionTarget;
}) => {
  const { success, err, uploadItem } = options;
  const res = await putCollectionDetailItem(
    uploadItem.id,
    err,
    success ? EnumColleDetailStatus.Success : EnumColleDetailStatus.Fail
  );
};
