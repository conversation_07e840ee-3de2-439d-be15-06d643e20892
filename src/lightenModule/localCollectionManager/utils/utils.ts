import { FAILED_CON, SUCCESS_CON } from '@common/constant';
import { isMaxFileSizeExceeded } from '@lightenModule/uploadManager/utils/checkFile';
import { getFilesForFilePath } from '@lightenModule/uploadManager/utils/chooseFilesOrFolders';
import { getFileOrDirInfo } from '@utils/folderUtils';

export const isMaxCollectionFileSizeExceeded = async (filePaths) => {
  let res = false;
  const fileList = filePaths instanceof Array ? filePaths : [filePaths];
  for (const aPath of fileList) {
    // 如果是文件夹选择框，那filePaths是文件夹path的列表，获取所有文件夹下的文件信息列表，为后面文件校验做准备
    const curFileOrDirInfRes = await getFileOrDirInfo(aPath);
    if (curFileOrDirInfRes.code === SUCCESS_CON) {
      const resFiles = await getFilesForFilePath(
        curFileOrDirInfRes.file.isDirectory,
        aPath
      );
      if (resFiles.code === FAILED_CON) {
        res = true;
        break;
      }

      for (const aFile of resFiles.fileList) {
        const fileSizeExceeded = isMaxFileSizeExceeded(aFile);
        if (fileSizeExceeded.code === FAILED_CON) {
          res = true;
          break;
        }
      }
    } else {
      res = false;
    }
  }
  return res;
};

export const checkSpecialCharacters = (files: string | string[]): boolean => {
  let isSpecial = false;
  const fileList = files instanceof Array ? files : [files];
  const reg =
    /[\u4e00-\u9fa5a-zA-Z\d~@#$%&＠＃＄％＆＋＝()（）【】\[\]：+=＝\.。《》！!,，、_—-\s]/g;
  for (const aFileName of fileList) {
    // 找到满足条件的字符，放在passChar里
    const passChar = aFileName.match(reg) ? aFileName.match(reg) : [];
    // 将文件名string转换成char的列表
    const originChar = [...aFileName];
    // 如果passChar和originChar相等，则说明每个字符都满足要求，不相等，说明有特殊字符被排除
    if (passChar?.length !== originChar.length) {
      isSpecial = true;
      break;
    }
  }

  return isSpecial;
};
