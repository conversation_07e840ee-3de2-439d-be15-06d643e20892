import Request, { flagRequest } from '@utils/request';
import { SUCCESS_CON } from '@common/constant';
import LOG from '@htElectronSDK/main/log';
import {
  CollectionDetailModel,
  CollectionDetailQueryOption,
  CollectionStatus,
  CollectionTask,
} from './interface';
import { EnumColleDetailStatus } from './enum';
import { IFileCollectionTarget, isCollecting } from '..';

/**
 * 获取归集状态
 * @param collectionId
 * @returns
 */
export const getCollectionStatus = async (
  collectionId: number
): Promise<CollectionStatus> => {
  return new Promise(async (resolve) => {
    const res = await Request<CollectionStatus>({
      url: `/pc/autoCollection/collection-manuscript-folder`,
      params: { collectionId },
    });
    const { code, data, errMsg } = res;
    //  底稿接口的data由三部分组成 {code,t,msg}=data
    // !important: 这里code=0足以判断是否成功，成功时t是允许为null，0，false等情况
    if (code === SUCCESS_CON && data.code === 0) {
      resolve(data.t);
    } else {
      LOG.error(errMsg);
      resolve(undefined);
    }
  });
};

/**
 * 本地归集-批量更新归集任务详情记录状态
 * @param items 实际上只需要item中的id、result、status
 * @returns 是否成功
 */
export const putCollectionDetailItem = async (
  id: number,
  result: string,
  status: EnumColleDetailStatus
) =>
  flagRequest({
    url: `/pc/autoCollection/collection-detail`,
    method: 'put',
    data: {
      data: [{ id, result, status }],
    },
  });

/**
 * 获取归集详情
 * @param option
 * @returns
 */
export const getCollectionDetail = async (
  option: CollectionDetailQueryOption
): Promise<CollectionDetailModel | undefined> => {
  const res = await Request<CollectionDetailModel | undefined>({
    url: `/pc/autoCollection/collection-detail`,
    params: { ...option },
  });

  const { code, data, errMsg } = res;
  if (code === SUCCESS_CON && data.t?.list && data.t.list.length > 0) {
    // 如果本地有正在上传的记录，则重置状态为归集中
    for (const item of data.t.list) {
      if (item.id && isCollecting(item.id)) {
        item.status = EnumColleDetailStatus.Collecting;
      }
    }
    return data.t;
  } else {
    LOG.error(errMsg);
    return undefined;
  }
};

/**
 * 初始化归集任务、详情，并且返回归集ID
 * @param task
 * @returns CollectionTaskItem列表
 */
export const postLocalCollection = (
  task: CollectionTask
): Promise<number | undefined> => {
  return new Promise(async (resolve) => {
    const res = await Request<number>({
      url: `/pc/autoCollection/local-collection`,
      method: 'post',
      data: task,
    });
    const { code, data, errMsg } = res;
    if (code === SUCCESS_CON && data.code === 0) {
      const { t } = data;
      resolve(t);
    } else {
      LOG.error(errMsg);
      resolve(undefined);
    }
  });
};

/**
 * 根据初始化归集任务返回的id，查询匹配结果
 * @param task
 * @returns
 */
export const getLocalCollection = (
  collectionId: number
): Promise<{
  collectionId: any;
  data: IFileCollectionTarget[];
}> => {
  return new Promise(async (resolve) => {
    const res = await Request<{
      collectionId: any;
      data: IFileCollectionTarget[];
    }>({
      url: `/pc/autoCollection/local-collection`,
      params: { collectionId },
    });
    const { code, data, errMsg } = res;
    if (code === SUCCESS_CON && data.code === 0) {
      const { t } = data;
      resolve(t);
    } else {
      LOG.error(errMsg);
      resolve(undefined);
    }
  });
};

/**
 * 取消归集任务
 * @param collectionId 归集id
 * @returns 是否成功
 */
export const cancelCollectionRecord = (collectionId: number) =>
  flagRequest({
    url: `/pc/autoCollection/collection-record`,
    method: 'put',
    data: { collectionId },
  });
