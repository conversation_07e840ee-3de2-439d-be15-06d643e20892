import {
  EnumColleFileOrigin,
  EnumColleItemType,
  EnumColleDetailStatus,
  EnumColleItemStatus,
  EnumColleOrder,
} from './enum';
import { ICollectionFileInfo } from '..';

export interface CollectionDetailModel {
  catalogId: string; // 底稿目录Id
  catalogName: string; // 底稿目录
  createTime: string; // 创建时间
  fileNumber: string; // 归集文件总数
  fileOrigin: EnumColleFileOrigin; // 文件来源路径(0-本地,1-源稿空间,2-协作空间)
  fileType: EnumColleItemType; // 文件类型 0-文件 ，1-文件夹
  keepOrigin: boolean; // 是否保留源文件：true-是，false-否
  projectId: string; // 项目Id
  projectName: string; // 项目名
  stageCode: string; // 阶段Id
  stageName: string; // 阶段名称
  phase: number; // 阶段代号
  limitWord: 0 | 1;
  list: CollectionDetailFileModel[]; // 归集任务详情信息
}
export interface CollectionDetailFileModel {
  id: number; // 归集id
  fileName: string; // 文件名
  status: EnumColleDetailStatus; // 0-失败 -1 成功
  result: string; // 失败则结果&成功则对应底稿目录
  parentPath: string; // 所属文件夹 /底稿空间/XXXX
  path: string; // 路径
}

export interface CollectionStatus {
  collectionId: number;
  failed: number;
  success: number;
  status: EnumColleItemStatus;
}

export type CollectionDetailQueryOption = {
  collectionId: number;
  fileName?: string;
  orderName?: EnumColleOrder;
  status?: EnumColleDetailStatus;
  projectId?: number;
};

/**
 * 本地归集任务
 * autoCollection/local-collection接口的入参
 */
export interface CollectionTask extends CollectionTaskParams {
  fileInfoList: ICollectionFileInfo[]; // 归集文件/文件夹
  projectName: string;
  spaceName: string;
}

/**
 * 创建本地归集的入参
 */
export interface CollectionTaskParams {
  projectId: number; // 项目id
  stageCode: string; // 底稿目录阶段编码
  type: EnumColleItemType; // 归集类型（0-文件，1-文件夹）
  catalogId: string; // 底稿目录范围编码
}

export interface CollectionTaskItem {
  collectionId: number; // 归集任务id，服务器不会返回，仅用于本地数据
  id: number; // 归集详情id，服务器返回
  fileName: string; // 归集文件名称, 服务器返回
  parentPath: string; // 文件上所属文件夹名称，文件夹归集需要用到，本地传
  targetCatalogPath: string; // 匹配的底稿目录网盘全路径，服务器返回
  localPath: string; // 本地路径，本地传
  pathId: number;
}
