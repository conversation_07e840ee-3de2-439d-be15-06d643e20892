import {
  ADD_CON,
  COMPLETE_CON,
  PAUSED_CON,
  RUNNING_CON,
  WAITING_CON,
} from '@common/constant';
import { LowSync } from 'lowdb/lib';
import {
  getUserLocalCollectionStore,
  IUserLocalCollectionStore,
} from '@globalState/localStore/localCollectionStore';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { LINKFLOW_LOCAL_COLLECTION_UPDATE_STATUS } from '@lightenSDK/constant/localCollection';
import { TTaskChangeCallBackFun } from '@lightenModule/taskManager';
import { ILocalCollectionTask } from '..';

export const lCTaskChangeCallBack: TTaskChangeCallBackFun<
  ILocalCollectionTask
> = (changeTaskInfos) => {
  const localCollectionStore = getUserLocalCollectionStore();
  localCollectionStore.read();
  for (const changeTaskInfo of changeTaskInfos) {
    // 更新存储数据
    updateLCStore(localCollectionStore, changeTaskInfo);
  }
  localCollectionStore.write();
  // 更新主任务状态
  mainIPC.send({ id: LINKFLOW_LOCAL_COLLECTION_UPDATE_STATUS });
};

const updateLCStore = (
  localCollectionStore: LowSync<IUserLocalCollectionStore>,
  changeTaskInfo: {
    changeType: TCOMPLETE_FLAG | TRUNNING_FLAG | TWAITING_FLAG | TPAUSED_FLAG;
    operationType: TADD_FLAG | TDELETE_FLAG;
    Task: ILocalCollectionTask;
  }
) => {
  switch (changeTaskInfo.changeType) {
    case RUNNING_CON:
      if (changeTaskInfo.operationType === ADD_CON) {
        localCollectionStore.data.processingList?.push({
          ...changeTaskInfo.Task,
          status: RUNNING_CON,
        });
      } else {
        localCollectionStore.data.processingList =
          localCollectionStore.data.processingList?.filter(
            (item) => item.key !== changeTaskInfo.Task.key
          );
      }
      break;
    case WAITING_CON:
      if (changeTaskInfo.operationType === ADD_CON) {
        localCollectionStore.data.waitingList?.push({
          ...changeTaskInfo.Task,
          status: WAITING_CON,
        });
      } else {
        localCollectionStore.data.waitingList =
          localCollectionStore.data.waitingList?.filter(
            (item) => item.key !== changeTaskInfo.Task.key
          );
      }
      break;
    case PAUSED_CON:
      if (changeTaskInfo.operationType === ADD_CON) {
        localCollectionStore.data.pauseList?.push({
          ...changeTaskInfo.Task,
          status: PAUSED_CON,
        });
      } else {
        localCollectionStore.data.pauseList =
          localCollectionStore.data.pauseList?.filter(
            (item) => item.key !== changeTaskInfo.Task.key
          );
      }
      break;
    case COMPLETE_CON:
      if (changeTaskInfo.operationType === ADD_CON) {
        localCollectionStore.data.completeList?.push(changeTaskInfo.Task);
      } else {
        localCollectionStore.data.completeList =
          localCollectionStore.data.completeList?.filter(
            (item) =>
              item.key !== changeTaskInfo.Task.key ||
              item.createTime !== changeTaskInfo.Task.createTime
          );
      }
      break;
    default:
      break;
  }
};
