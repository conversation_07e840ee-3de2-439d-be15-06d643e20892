import { LINKFLOW_MESSAGE_WARN } from '@lightenSDK/constant/global';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { WAITING_CON } from '@common/constant';
import moment from 'moment';
import { getUserLocalCollectionStore } from '@globalState/localStore/localCollectionStore';
import { getLocalCollection } from './utils/localCollectionApi';
import { localCollectionPollingTimers } from '.';

// 新建轮询任务
export const addPollingTaskAndRun = async (task, localCollectionManager) => {
  const startTime = new Date().getTime(); // 记录开始时间
  const pollTimer = setInterval(async () => {
    const { data: fileTargetList } = await getLocalCollection(
      task.collectionId
    );
    if (fileTargetList) {
      fileTargetList.forEach((fileTargetItem) => {
        fileTargetItem.status = WAITING_CON;
      });
      // 添加任务至等待队列
      const createTime = moment().format('YYYY-MM-DD hh:mm:ss');
      const vTask = {
        ...task,
        status: WAITING_CON,
        fileTargetList,
        createTime,
      };
      deletePollingTask(vTask.key);
      localCollectionManager.addTaskToRun(vTask.key, vTask);
      clearInterval(pollTimer);
    }
    // 轮询时间超过60秒
    const endTime = new Date().getTime();
    const intervalTime = endTime - startTime;
    if (intervalTime > 60000) {
      mainIPC.send({
        id: LINKFLOW_MESSAGE_WARN,
        params: {
          text: '归集任务匹配目录超时',
        },
      });
      clearInterval(pollTimer);
    }
  }, 2000);

  localCollectionPollingTimers.set(task.key, pollTimer);
  const lCStore = getUserLocalCollectionStore();
  lCStore.read();
  lCStore.data.pollingList.push(task);
  lCStore.write();
};

// 轮询完成，从列表中删除轮询任务
export const deletePollingTask = (key) => {
  const lcStore = getUserLocalCollectionStore();
  lcStore.read();
  lcStore.data.pollingList = lcStore.data.pollingList?.filter(
    (item) => item.key !== key
  );
  lcStore.write();
};

// 用户登录时，恢复所有轮询任务
export const startPollingAllTask = (localCollectionManager) => {
  const lcStore = getUserLocalCollectionStore();
  lcStore.read();
  const initPollingList = [...lcStore.data.pollingList];
  lcStore.data.pollingList = [];
  lcStore.write();
  initPollingList.forEach((task) => {
    addPollingTaskAndRun(task, localCollectionManager);
  });
};

// 用户退出时，中断所有轮询任务
export const pausePollingAllTask = async () => {
  for (const key in localCollectionPollingTimers) {
    clearInterval(localCollectionPollingTimers[key]);
    localCollectionPollingTimers[key] = null;
  }
  localCollectionPollingTimers.clear();
};
