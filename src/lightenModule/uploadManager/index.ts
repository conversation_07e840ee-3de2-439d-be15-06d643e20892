import { PAUSED_CON, SUCCESS_CON } from '@common/constant';
import {
  getUserUploadListStore,
  IUserUploadStore,
} from '@globalState/localStore/uploadListStore';
import { COVER_CON } from '@htElectronSDK/constant/ipc';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { CTaskManager } from '@lightenModule/taskManager';
import {
  LINKFLOW_MESSAGE_ERROR,
  LINKFLOW_ON_SHOW_MODAL,
  LINKFLOW_TRANSFER_LIST_INFO,
} from '@lightenSDK/constant/global';
import {
  LINKFLOW_UPLOAD_BY_DRAG,
  LINKFLOW_UPLOAD_CHOOSE_FILE,
  LINKFLOW_UPLOAD_CONFIRM_DUPLICATE_DEAL,
  LINKFLOW_UPLOAD_PAUSE,
  LINKFLOW_UPLOAD_REMOVE,
  LINKFLOW_UPLOAD_RESTART,
} from '@lightenSDK/constant/upload';
import { IFileOrDirInfo } from '@utils/folderUtils';
import { BrowserWindow } from 'electron';
import { EventEmitter } from 'events';
import { LowSync } from 'lowdb';
import { runUploadTask } from './runUploadTask';
import { TCheckIFileOrDirInfo } from './utils/checkFile';
import {
  chooseFilesOrFolders,
  dealFilePaths,
} from './utils/chooseFilesOrFolders';
import { confirmDuplicateDeal } from './utils/dealDuplicateName';
import { getUploadInfo } from './utils/getUploadInfo';
import { ILightenUploadChunkRes } from './utils/uploadFile';
import { getCreateUploadTasksAndRunFun } from './utils/uploadMsgHandler/getCreateUploadTasksAndRunFun';
import { uploadTaskChangCallBack } from './utils/uploadMsgHandler/uploadTaskChangCallBack';

export const CREATE_TASKS_AND_RUN = 'CREATE_TASKS_AND_RUN';

export interface IUploadItem {
  fileInfo: TCheckIFileOrDirInfo;
  key?: string; // 统一存储分片上传初始化接口返回的文件key
  uploadId?: string; // 统一存储分片上传初始化接口返回的文件uploadId,
  status: TTaskStatus;
  returnValues: ILightenUploadChunkRes[];
}

export interface IUploadTask {
  key: string; // 任务key,生成规则：neid+uploadFileOrDirInfos.path
  pathType: TFilePathType;
  taskFileOrDirInfo: IFileOrDirInfo;
  list: IUploadItem[]; // 待上传列表（要上传的本地文件是个文件夹时，待上传列表包含文件夹内所有文件，否则只有一项）
  neid: number; // 上传网盘目录的neid
  rootNetPath: string; // 上传网盘目录的完整path，个人文件夹不需要夹工号，后台自动拼接
  totalSize: number; // 总大小
  oldFileId?: number; // 如果有重复文件，选择新增版本，则需要oldFileId
  status: TTaskStatus; // 任务状态
  errMsg?: string;
  // 上传文件的目标网盘相对路径，如果需要重命名，则netPath为xxx(1),，如果不需要，则为上传文件夹的名称
  netPath?: string;
  completedSize?: number; // 已完成的上传数据大小
  rate?: number; // 下载速度, 单位：字节/s
  startTime: string;
  completeTime?: string; // 完成时间
  target: unknown; // 存routerLocation等信息，因为上传成功以后，点击放大镜图标，可以定位到底稿或者个人文件的目录，所以需要保存前端的路由信息等
}

export const UPLOAD_PIECE_SIZE = 5 * 1024 * 1024; // 上传切片大小，5Mb//对象存储限制必须为5M分块

const UPLOAD_CONCURRENT_TASK_NUM = 5;

const uploadAbortControllers = new Map<string, AbortController>();

export const uploaderEvent = new EventEmitter();

const taskEqualFun = (vTask1: IUploadTask, vTask2: IUploadTask) => {
  if (vTask1?.key && vTask1?.startTime && vTask2?.key && vTask2?.startTime) {
    return vTask1.key === vTask2.key && vTask1.startTime === vTask2.startTime;
  }
  return false;
};

const initUploadTaskManager = (uploadStore: LowSync<IUserUploadStore>) => {
  uploadStore.read();
  // 将正在进行和等待都转为暂停
  uploadStore.data.processingList?.forEach((item) => {
    uploadStore.data.pauseList?.push({
      ...item,
      status: PAUSED_CON,
    });
  });
  uploadStore.data.waitingList?.forEach((item) => {
    uploadStore.data.pauseList?.push({
      ...item,
      status: PAUSED_CON,
    });
  });
  uploadStore.data.processingList = [];
  uploadStore.data.waitingList = [];
  uploadStore.write();
  const initPauseList = new Map<string, IUploadTask>();
  uploadStore.data.pauseList?.forEach((item: IUploadTask) => {
    initPauseList.set(item.key, item);
  });

  mainIPC.send({
    id: LINKFLOW_TRANSFER_LIST_INFO,
    params: getUploadInfo(),
  });

  // 初始化任务管理器
  return new CTaskManager<string, IUploadTask>(
    UPLOAD_CONCURRENT_TASK_NUM,
    runUploadTask,
    initPauseList,
    uploadStore.data.completeList,
    uploadAbortControllers,
    uploadTaskChangCallBack,
    taskEqualFun
  );
};

export const initLightenUploader = () => {
  const renderWindow = BrowserWindow.fromWebContents(
    mainIPC.getMainWindowWebContents()
  );
  // 拿到磁盘缓存的数据
  const uploadStore = getUserUploadListStore();
  // 初始化任务管理器
  const uploadManager = initUploadTaskManager(uploadStore);
  // 清除可能存在的上一个用户的信息，监听重命名处理以后的消息
  uploaderEvent.removeAllListeners(CREATE_TASKS_AND_RUN);
  uploaderEvent.addListener(
    CREATE_TASKS_AND_RUN,
    getCreateUploadTasksAndRunFun(uploadManager)
  );

  // 新增上传消息监听
  mainIPC.addListener(
    LINKFLOW_UPLOAD_CHOOSE_FILE,
    async (msg: any) => {
      // 兼容之前的传参
      const { target, isFolder } = msg;
      const { limitWord } = target.routerLocation;
      const neid =
        target.routerLocation.oldProjectCatalogId || target.routerLocation.neid;
      const { pathType } = target;
      const rootNetPath = target.routerLocation.path;

      const uploadTaskInfo = await chooseFilesOrFolders(
        renderWindow,
        neid,
        isFolder,
        limitWord,
        pathType,
        rootNetPath,
        target
      );
      if (uploadTaskInfo.code !== SUCCESS_CON) {
        if (uploadTaskInfo.wrongFlag) {
          mainIPC.send({
            id: LINKFLOW_ON_SHOW_MODAL,
            params: {
              type: 'warning',
              title: uploadTaskInfo.errMsg,
              content: uploadTaskInfo.checkString,
              okText: '确定',
              isHtmlStr: uploadTaskInfo.wrongFlag === 'SPECIAL_CHARACTER',
            },
          });
        } else if (uploadTaskInfo.errMsg) {
          mainIPC.send({
            id: LINKFLOW_MESSAGE_ERROR,
            params: { text: uploadTaskInfo.errMsg },
          });
        }
      }
    },
    COVER_CON
  );
  // 新增拖拽上传消息监听
  mainIPC.addListener(
    LINKFLOW_UPLOAD_BY_DRAG,
    async (msg: any) => {
      // 兼容之前的传参
      const { target, files } = msg;
      const { limitWord } = target.routerLocation;
      const neid =
        target.routerLocation.oldProjectCatalogId || target.routerLocation.neid;
      const { pathType } = target;
      const rootNetPath = target.routerLocation.path;

      const uploadTaskInfo = await dealFilePaths(
        files,
        neid,
        limitWord,
        pathType,
        rootNetPath,
        target
      );
      if (uploadTaskInfo.code !== SUCCESS_CON) {
        if (uploadTaskInfo.wrongFlag) {
          mainIPC.send({
            id: LINKFLOW_ON_SHOW_MODAL,
            params: {
              type: 'warning',
              title: uploadTaskInfo.errMsg,
              content: uploadTaskInfo.checkString,
              okText: '确定',
              isHtmlStr: uploadTaskInfo.wrongFlag === 'SPECIAL_CHARACTER',
            },
          });
        } else if (uploadTaskInfo.errMsg) {
          mainIPC.send({
            id: LINKFLOW_MESSAGE_ERROR,
            params: { text: uploadTaskInfo.errMsg },
          });
        }
      }
    },
    COVER_CON
  );

  // 有重复文件或者文件夹的时候，用户选择后，二次确认监听
  mainIPC.addListener(
    LINKFLOW_UPLOAD_CONFIRM_DUPLICATE_DEAL,
    async (msg: any) => {
      // 兼容之前的传参
      const { target, filePath, isRename, isApplyAll } = msg;
      const neid =
        target.routerLocation.oldProjectCatalogId || target.routerLocation.neid;
      confirmDuplicateDeal(neid, filePath, isRename, isApplyAll);
    },
    COVER_CON
  );
  // 暂停上传消息监听
  mainIPC.addListener(
    LINKFLOW_UPLOAD_PAUSE,
    (msg: any) => {
      const { keys } = msg;
      for (const curKey of keys) {
        uploadAbortControllers.get(curKey)?.abort();
        uploadAbortControllers.delete(curKey);
        // 等待队列中的暂停动作
        uploadManager.addWaitingTaskToPauseTasks(curKey);
      }
    },
    COVER_CON
  );

  // 重传消息监听
  mainIPC.addListener(
    LINKFLOW_UPLOAD_RESTART,
    (msg: any) => {
      // 两种情况，1.暂停重传，失败重传，失败的情况，key不能作为唯一值，所以要加上startTime
      const { reStartInfos } = msg;
      for (const cResInfo of reStartInfos) {
        uploadStore.read();
        const { key, startTime } = cResInfo;
        // 失败重传
        if (startTime) {
          const findTask =
            uploadStore.data.completeList?.find(
              (item) => item.key === key && item.startTime === startTime
            ) || undefined;
          uploadManager.removeFromCompleteTasks(findTask);
          uploadManager.addTaskToRun(key, findTask);
        } else {
          uploadManager.addTaskToRun(key);
        }
      }
    },
    COVER_CON
  );

  // 移除上传消息监听
  mainIPC.addListener(
    LINKFLOW_UPLOAD_REMOVE,
    (msg: any) => {
      const { deleteInfos } = msg;
      for (const cDelInfo of deleteInfos) {
        const { key, startTime } = cDelInfo;
        // 失败重传
        if (startTime) {
          uploadStore.read();
          const findTask =
            uploadStore.data.completeList?.find(
              (item) => item.key === key && item.startTime === startTime
            ) || undefined;
          uploadManager.removeFromCompleteTasks(findTask);
        } else {
          uploadAbortControllers.get(key)?.abort();
          uploadAbortControllers.delete(key);
          uploadManager.removeFromTasks(key);
        }
      }
    },
    COVER_CON
  );
};

export const pauseAllUpload = () => {
  for (const controller of uploadAbortControllers.values()) {
    controller.abort();
  }
  uploadAbortControllers.clear();
};
