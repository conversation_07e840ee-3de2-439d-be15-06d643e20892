/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
import {
  CANCELED_CON,
  FAILED_CON,
  PAUSED_CON,
  RUNNING_CON,
  SUCCESS_CON,
} from '@common/constant';
import { TRunTaskReturn } from '@lightenModule/taskManager';
import { getErrorMsg } from '@utils/stringUtils';
import { mainIPC } from '@htElectronSDK/main/ipc';
import moment from 'moment';
import { LINKFLOW_ON_UPLOAD_END } from '@lightenSDK/constant/upload';
import { IUploadItem, IUploadTask, UPLOAD_PIECE_SIZE } from '.';
import {
  getUploadProcessFun,
  uploadProcess4SmallFile,
} from './utils/getUploadProcessFun';
import { uploadBigFile } from './utils/uploadBigFile';
import { uploadSmallFile } from './utils/uploadSmallFile';
import { uploadCallback } from './utils/uploadCallback';

export const runUploadTask = (
  vTask: IUploadTask,
  uploadAbortControllers: Map<string, AbortController>
) => {
  return new Promise<TRunTaskReturn<string, IUploadTask>>(async (resolve) => {
    try {
      vTask.status = RUNNING_CON;
      const unCompList = vTask.list.filter(
        (item) => item.status !== SUCCESS_CON
      );

      // 暂停token
      const curController = new AbortController();
      uploadAbortControllers.set(vTask.key, curController);

      // 记录任务下每个文件的上传结果
      const resList: TFunReturnFlag[] = [];
      let taskErrMsg = '';

      for (const uploadItem of unCompList) {
        taskErrMsg = await handleUploadItem(
          vTask,
          curController,
          uploadItem,
          resList
        );
        if (taskErrMsg) {
          break;
        }
      }

      uploadAbortControllers.delete(vTask.key);
      // 任务运行完成，写入状态
      if (resList.includes(FAILED_CON)) {
        // 失败
        vTask.status = FAILED_CON;
        vTask.errMsg = taskErrMsg;
        resolve({ code: FAILED_CON, key: vTask.key, task: vTask });
      } else if (resList.includes(CANCELED_CON)) {
        // 暂停
        vTask.status = PAUSED_CON;
        vTask.errMsg = taskErrMsg;
        resolve({ code: PAUSED_CON, key: vTask.key, task: vTask });
      } else {
        // 成功
        vTask.status = SUCCESS_CON;
        vTask.completeTime = moment().format('YYYY_MM_DD_hh_mm_ss_SSS');
        vTask.errMsg = taskErrMsg;
        handleUploadEnd(vTask);
        resolve({ code: SUCCESS_CON, key: vTask.key, task: vTask });
      }
    } catch (error) {
      vTask.status = FAILED_CON;
      vTask.errMsg = getErrorMsg(error);
      resolve({ code: FAILED_CON, key: vTask.key, task: vTask });
    }
  });
};

const handleUploadItem = async (
  vTask: IUploadTask,
  curController: AbortController,
  uploadItem: IUploadItem,
  resList: TFunReturnFlag[]
) => {
  // 状态设置为正在执行
  uploadItem.status = RUNNING_CON;
  // 如果是大文件
  if (uploadItem.fileInfo.size > UPLOAD_PIECE_SIZE) {
    const uploadBigFileRes = await uploadBigFile(
      vTask,
      uploadItem,
      curController,
      getUploadProcessFun(vTask, moment())
    );
    if (uploadBigFileRes.code === SUCCESS_CON) {
      uploadItem.status = SUCCESS_CON;
      uploadCallback.onUploadSuccessCb({
        type: 'uploadItem',
        result: uploadBigFileRes,
        uploadItem,
        uploadTask: vTask,
      });
      resList.push(SUCCESS_CON);
    } else if (uploadBigFileRes.code === CANCELED_CON) {
      // task中一个文件上传失败或者取消，取消的情况，这个文件的状态置为失败，task的状态置为暂停
      uploadItem.status = PAUSED_CON;
      resList.push(uploadBigFileRes.code);
      return uploadBigFileRes.errMsg;
    } else {
      uploadItem.status = uploadBigFileRes.code;
      resList.push(uploadBigFileRes.code);
      return uploadBigFileRes.errMsg;
    }
  } else {
    // 小文件直接上传
    const startTime = moment();
    const resP = await uploadSmallFile(uploadItem, vTask, curController);
    const timeGapSecs = moment().diff(startTime, 'milliseconds');
    if (resP.code === SUCCESS_CON) {
      // 文件上传成功
      vTask.completedSize = vTask.completedSize
        ? vTask.completedSize + uploadItem.fileInfo.size
        : uploadItem.fileInfo.size;
      vTask.rate = uploadItem.fileInfo.size / timeGapSecs;
      uploadProcess4SmallFile(vTask);

      uploadItem.status = SUCCESS_CON;
      resList.push(SUCCESS_CON);
      uploadCallback.onUploadSuccessCb({
        type: 'uploadItem',
        result: resP,
        uploadItem,
        uploadTask: vTask,
      });
    } else if (resP.code === CANCELED_CON) {
      // task中一个文件上传失败或者取消，取消的情况，这个文件的状态置为失败，task的状态置为暂停
      uploadItem.status = PAUSED_CON;
      resList.push(resP.code);
      return resP.errMsg;
    } else {
      // task中一个文件上传失败或者取消，取消的情况，这个文件的状态置为失败，task的状态置为暂停
      uploadItem.status = FAILED_CON;
      resList.push(resP.code);
      return resP.errMsg;
    }
  }
  return '';
};

const handleUploadEnd = (task: IUploadTask) => {
  mainIPC.send({
    id: LINKFLOW_ON_UPLOAD_END,
    params: task,
  });
};
