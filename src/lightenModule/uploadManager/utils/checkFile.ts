import { FAILED_CON, SUCCESS_CON } from '@common/constant';
import { IFileOrDirInfo } from '@utils/folderUtils';
import { getErrorMsg } from '@utils/stringUtils';
import LOG from '@htElectronSDK/main/log';

import path from 'path';

export type TCheckIFileOrDirInfo = IFileOrDirInfo & { prefix?: string };

export const MAX_FILE_SIZE = 2 * 1024 * 1024 * 1024; // 最大支持2G的文件上传
const MAX_PATH_LENGTH = 200; // 最长文件名

export const SPECIAL_CHARACTER_CON = 'SPECIAL_CHARACTER';
export const FILE_NAME_LENGTH_EXCEEDED_CON = 'FILE_NAME_LENGTH_EXCEEDED';
export const FILE_SIZE_EXCEEDED_CON = 'FILE_SIZE_EXCEEDED';
export const FILE_SUFFIX_WRONG_CON = 'FILE_SUFFIX_WRONG';
export const FILE_SUFFIX_SPECIAL_CON = 'FILE_SUFFIX_SPECIAL';

export const CHECK_MANUSCRIPT_REG =
  /\.(pdf|doc|docx|xls|xlsx|jpg|jpeg|png|mp3|mp4|bmp|wma|msg|wav|m4a)$/i;
export const CHECK_MANUSCRIPT_IBD_REG =
  /\.(docx|msg|pdf|zip|xlsx|xls|png|doc|jpg|mp4|eml|rar|pptx|txt|7z|jpeg|m4a|db|tif|ppt|html|tmp|wav|svg|pic|rtf|pages|log|htm|heic|mp3|bmp|wma)$/i;

type TSPECIAL_CHARACTER = 'SPECIAL_CHARACTER';
type TFILE_NAME_LENGTH_EXCEEDED = 'FILE_NAME_LENGTH_EXCEEDED';
type TFILE_SIZE_EXCEEDED = 'FILE_SIZE_EXCEEDED';
type TFILE_SUFFIX_WRONG = 'FILE_SUFFIX_WRONG';
type TFILE_SUFFIX_SPECIAL = 'FILE_SUFFIX_SPECIAL';

type TWrongFlag =
  | TSPECIAL_CHARACTER
  | TFILE_NAME_LENGTH_EXCEEDED
  | TFILE_SIZE_EXCEEDED
  | TFILE_SUFFIX_WRONG
  | TFILE_SUFFIX_SPECIAL;
export type ICheckFileReturn = ICommonFunSimpReturn & {
  checkString?: string;
  wrongFlag?: TWrongFlag;
};

/**
 * 这段逻辑解释举例：输入['a', 'b', 'c', 'd', 'e'], ['a', 'd']，则b, c,e都是要高亮的
 * 输出：<span>a<span style="color: #FF592C;">bc</span>d<span style="color: #FF592C;">e</span></span>
 * 以原始串为基准，每次向后移一位，和passNameChs相应的字符做比较，找到一串需要高亮或者不需要高亮的子串，然后以不同样式拼凑这些子串，最后得到一个特殊字符高亮显示的html字符串
 * @param originNameChs
 * @param passNameChs
 * @returns
 */
const contactHighLightStr = (originNameChs, passNameChs) => {
  let pIndex = 0;
  let reStr = '<span>';
  let curStr = '';
  let passSubStr = true;

  for (const originNameChsItem of originNameChs) {
    if (originNameChsItem === passNameChs[pIndex]) {
      if (passSubStr) {
        curStr += originNameChsItem;
      } else {
        reStr += `<span style="color: #FF592C;">${curStr}</span>`;
        curStr = originNameChsItem;
        passSubStr = true;
      }
      pIndex++;
    } else if (passSubStr) {
      reStr += curStr;
      curStr = originNameChsItem;
      passSubStr = false;
    } else {
      curStr += originNameChsItem;
    }
  }
  if (passSubStr) {
    reStr += curStr;
  } else {
    reStr += `<span style="color: #FF592C;">${curStr}</span>`;
  }
  return `${reStr}</span>`;
};

export const checkSpecialCharacters: (
  vFile: TCheckIFileOrDirInfo
) => ICheckFileReturn = (vFile) => {
  try {
    let isSpecial = false;
    let concatString = '';
    const reg =
      /[\u4e00-\u9fa5a-zA-Z\d~@#$%&＠＃＄％＆＋＝()（）【】\[\]：+=＝\.。《》！!,，、_—\-．\s]/g;
    // 文件夹内的文件要校验path，如果是单独的文件，则只校验文件名
    const checkPath = vFile.prefix
      ? vFile.path.replace(vFile.prefix, '')
      : vFile.name;
    // 举例：'foo/bar/baz'.split(path.sep);
    // Returns: ['foo', 'bar', 'baz']
    const checkPathItems = checkPath.split(path.sep);
    for (const pathItem of checkPathItems) {
      const passChar = pathItem.match(reg) ? pathItem.match(reg) : [];
      // 将文件名string转换成char的列表
      const originChar = [...pathItem];
      // 如果passChar和originChar相等，则说明每个字符都满足要求，不相等，说明有特殊字符被排除
      if (passChar?.length !== originChar.length) {
        // 拼凑高亮字符串
        concatString = contactHighLightStr(originChar, passChar);
        isSpecial = true;
        break;
      }
    }

    if (isSpecial) {
      return {
        code: FAILED_CON,
        wrongFlag: SPECIAL_CHARACTER_CON,
        errMsg: '以下文件名称包含特殊字符（高亮显示处）',
        checkString: concatString,
      };
    } else {
      return { code: SUCCESS_CON };
    }
  } catch (error) {
    return {
      code: FAILED_CON,
      wrongFlag: SPECIAL_CHARACTER_CON,
      errMsg: getErrorMsg(error),
    };
  }
};

// 单个文件名称长度校验
export const isFileNameLengthExceeded: (
  vFile: TCheckIFileOrDirInfo
) => ICheckFileReturn = (vFile) => {
  let isMaxName = false;
  let aFileName = '';
  // 文件夹内的文件要校验path，如果是单独的文件，则只校验文件名
  const checkPath = vFile.prefix
    ? vFile.path.replace(vFile.prefix, '')
    : vFile.name;

  const checkPathItems = checkPath.split(path.sep);
  for (const pathItem of checkPathItems) {
    if (pathItem?.length > MAX_PATH_LENGTH) {
      aFileName = pathItem;
      isMaxName = true;
      break;
    }
  }
  if (isMaxName) {
    return {
      code: FAILED_CON,
      wrongFlag: FILE_NAME_LENGTH_EXCEEDED_CON,
      errMsg: '文件/文件夹名称不能超过200个字符，请缩减名称!',
      checkString: aFileName,
    };
  } else {
    return { code: SUCCESS_CON };
  }
};

// 单个文件大小校验
export const isMaxFileSizeExceeded: (
  vFiles: TCheckIFileOrDirInfo
) => ICheckFileReturn = (vFile) => {
  if (vFile.size > MAX_FILE_SIZE) {
    return {
      code: FAILED_CON,
      wrongFlag: FILE_SIZE_EXCEEDED_CON,
      errMsg: '单个文件最大不可超过2G!',
      checkString: vFile.name,
    };
  }
  return { code: SUCCESS_CON };
};

export const checkManuscriptOrIbdFilesSuffix: (
  vFiles: TCheckIFileOrDirInfo[],
  type: string
  // eslint-disable-next-line max-statements
) => ICheckFileReturn = (vFiles, type) => {
  let flag = true;
  const noCheckPassFileSuffixList = [];
  const noCheckPassFileNameList = [];
  let checkString = '';
  const reg =
    type === 'manuscript' ? CHECK_MANUSCRIPT_REG : CHECK_MANUSCRIPT_IBD_REG;

  for (const aFile of vFiles) {
    if (!reg.test(aFile.name)) {
      if (flag) {
        flag = false;
      }
      const lastIndex = aFile.name?.lastIndexOf('.');
      noCheckPassFileNameList.push(aFile.name || '');
      const subStringName = aFile.name?.substring(
        lastIndex + 1,
        aFile.name?.length
      );
      if (!noCheckPassFileSuffixList.includes(subStringName) && subStringName) {
        noCheckPassFileSuffixList.push(subStringName);
      }
    }
  }

  if (!flag) {
    LOG.info(
      `${
        type === 'manuscript' ? '底稿空间' : '金控底稿空间'
      }文件上传，文件类型不支持的文件：`,
      noCheckPassFileNameList
    );
    for (const item of noCheckPassFileSuffixList) {
      checkString += `${item}、`;
    }
    checkString = checkString.substring(0, checkString.length - 1);
    return {
      code: FAILED_CON,
      wrongFlag: FILE_SUFFIX_WRONG_CON,
      errMsg: `${
        type === 'manuscript' ? '底稿空间' : '金控底稿空间'
      }不支持以下文件类型!`,
      checkString: `${checkString}，请确保全部文件类型为简富支持的文件类型，否则将影响您的操作！`,
    };
  }

  return { code: SUCCESS_CON };
};

export const checkManuscriptFileSpecialSuffix: (
  vFiles: TCheckIFileOrDirInfo
) => ICheckFileReturn = (vFile) => {
  const reg = /\.(doc|docx|DOC|DOCX)$/i;
  if (reg.test(vFile.name)) {
    return {
      code: FAILED_CON,
      wrongFlag: FILE_SUFFIX_SPECIAL_CON,
      errMsg: '该目录不支持上传word文件!',
    };
  }
  return { code: SUCCESS_CON };
};

export const checkUploadItems: (
  vFiles: TCheckIFileOrDirInfo[],
  limitWord: 0 | 1,
  pathType: TFilePathType,
  rootNetPath: string
) => ICheckFileReturn = (vFiles, limitWord, pathType, rootNetPath) => {
  for (const aFile of vFiles) {
    const result = checkFile(aFile, limitWord, pathType, rootNetPath);
    if (result) {
      return result;
    }
  }
  return { code: SUCCESS_CON };
};

const checkFile = (
  aFile: TCheckIFileOrDirInfo,
  limitWord: 0 | 1,
  pathType: TFilePathType,
  rootNetPath: string
) => {
  const nameExceeded = isFileNameLengthExceeded(aFile);
  if (nameExceeded.code === FAILED_CON) {
    return nameExceeded;
  }
  const fileSizeExceeded = isMaxFileSizeExceeded(aFile);
  if (fileSizeExceeded.code === FAILED_CON) {
    return fileSizeExceeded;
  }

  // 底稿空间需要单独做文件上传类型限制
  const isManuscript =
    pathType !== 'self' &&
    !['源稿空间', '协作空间', '金控底稿空间'].includes(
      rootNetPath?.split('/')?.filter((i) => {
        return !!i;
      })?.[1]
    );

  if (limitWord && isManuscript) {
    const FileSuffixSpecial = checkManuscriptFileSpecialSuffix(aFile);
    if (FileSuffixSpecial.code === FAILED_CON) {
      return FileSuffixSpecial;
    }
  }
  const NameSpecialCharacters = checkSpecialCharacters(aFile);
  if (NameSpecialCharacters.code === FAILED_CON) {
    return NameSpecialCharacters;
  }
};
