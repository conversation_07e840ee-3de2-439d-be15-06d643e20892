import { FAILED_CON, SUCCESS_CON } from '@common/constant';
import {
  getAllFilesFromDir,
  getFileOrDirInfo,
  IFileOrDirInfo,
} from '@utils/folderUtils';
import { getErrorMsg } from '@utils/stringUtils';
import { dialog } from 'electron';
import path from 'path';
import {
  checkManuscriptOrIbdFilesSuffix,
  checkUploadItems,
  ICheckFileReturn,
  TCheckIFileOrDirInfo,
} from './checkFile';
import {
  checkDuplicateName,
  initFilePath,
  preFinished,
} from './dealDuplicateName';

export const chooseFilesOrFolders = (
  renderWindow: Electron.BrowserWindow,
  neid: number,
  isFolder: boolean,
  limitWord: 0 | 1,
  pathType: TFilePathType,
  rootNetPath: string,
  target: unknown
) => {
  return new Promise<ICheckFileReturn>((resolve) => {
    dialog
      .showOpenDialog(renderWindow, {
        title: `请选择要上传的${isFolder ? '文件夹' : '文件'}`,
        properties: isFolder
          ? ['openDirectory', 'multiSelections']
          : ['openFile', 'multiSelections'],
      })
      .then(async ({ filePaths, canceled }) => {
        if (canceled) {
          return;
        }
        const deRes = await dealFilePaths(
          filePaths,
          neid,
          limitWord,
          pathType,
          rootNetPath,
          target
        );
        if (deRes.code === SUCCESS_CON) {
          resolve({
            code: SUCCESS_CON,
          });
        } else {
          resolve({
            code: FAILED_CON,
            ...deRes,
          });
        }
      })
      .catch((error) => {
        resolve({ code: FAILED_CON, errMsg: getErrorMsg(error) });
      });
  });
};

export const getFilesForFilePath = (isFolder, filePath) => {
  return new Promise<
    ICommonFunSimpReturn & { fileList?: TCheckIFileOrDirInfo[] }
  >(async (resolve) => {
    if (isFolder) {
      const aRes = await getAllFilesFromDir(filePath);
      if (aRes.code === SUCCESS_CON) {
        resolve({
          code: SUCCESS_CON,
          fileList: aRes.fileList.map((item) => ({
            ...item,
            prefix: filePath.substring(
              0,
              filePath.length - path.basename(filePath).length
            ),
          })),
        });
      } else {
        resolve({
          code: FAILED_CON,
          errMsg: aRes.errorMsg.join(';'),
        });
      }
    } else {
      // 如果是文件选择框，那filePaths是文件path的列表，获取所有文件的文件信息，为后面文件校验做准备
      const aRes = await getFileOrDirInfo(filePath);
      if (aRes.code === SUCCESS_CON) {
        resolve({
          code: SUCCESS_CON,
          fileList: [aRes.file],
        });
      } else {
        resolve({ code: FAILED_CON, errMsg: aRes.errorMsg });
      }
    }
  });
};

export const dealFilePaths = (
  filePaths: string[],
  neid: number,
  limitWord: 0 | 1,
  pathType: TFilePathType,
  rootNetPath: string,
  target: unknown
) => {
  // eslint-disable-next-line max-statements
  return new Promise<ICheckFileReturn>(async (resolve) => {
    if (preFinished()) {
      if (!filePaths || filePaths.length === 0) {
        // 上传文件的系统目录过长files也为空，并且没有报错提示，所以这边提醒一下用户
        resolve({
          code: FAILED_CON,
          errMsg: '用户没有选择任何文件或者上传文件的系统目录过长，请检查！',
        });
        return;
      }

      const allUpLoadFileList = [];

      // 校验文件的名称，去掉前缀，比如选择d://download文件夹上传，则d://download/yyy/xxx.docx文件只需要校验yyy/xxx.docx即可
      // 所以需要prefix这个属性
      // 当前task的上传任务的文件的信息，一个task对应一个或者多个文件
      const allFileInfos: Map<string, TCheckIFileOrDirInfo[]> = new Map();
      // 当前task对应的文件夹或者文件的信息，如果是文件，就为当前文件的信息，如果是文件夹，就是文件夹的信息
      const taskFileInfos: Map<string, IFileOrDirInfo> = new Map();
      // 一个path对应一个任务，一个任务对应一个或多个需要上传文件
      for (const aPath of filePaths) {
        // 如果是文件夹选择框，那filePaths是文件夹path的列表，获取所有文件夹下的文件信息列表，为后面文件校验做准备
        const curFileOrDirInfRes = await getFileOrDirInfo(aPath);
        if (curFileOrDirInfRes.code === SUCCESS_CON) {
          taskFileInfos.set(aPath, curFileOrDirInfRes.file);
          const resFiles = await getFilesForFilePath(
            curFileOrDirInfRes.file.isDirectory,
            aPath
          );
          if (resFiles.code === SUCCESS_CON) {
            allUpLoadFileList.push(...resFiles.fileList);
            allFileInfos.set(aPath, resFiles.fileList);
          } else {
            resolve(resFiles);
            return;
          }
          const checkRes = checkUploadItems(
            resFiles.fileList,
            limitWord,
            pathType,
            rootNetPath
          );
          if (checkRes.code !== SUCCESS_CON) {
            // 名称校验没通过
            resolve(checkRes);
            return;
          }
        } else {
          resolve({
            code: FAILED_CON,
            errMsg: curFileOrDirInfRes.errorMsg,
          });
          return;
        }
      }

      // 金控底稿空间需要单独做文件上传类型校验，判断限制的文件类型并给出提示
      const isManuscriptIbd =
        pathType !== 'self' &&
        ['金控底稿空间'].includes(
          rootNetPath?.split('/')?.filter((i) => {
            return !!i;
          })?.[1]
        );

      // 底稿空间需要单独做文件上传类型限制
      const isManuscript =
        pathType !== 'self' &&
        !['源稿空间', '协作空间', '金控底稿空间'].includes(
          rootNetPath?.split('/')?.filter((i) => {
            return !!i;
          })?.[1]
        );

      if (isManuscriptIbd || isManuscript) {
        const FileSuffixWrong = checkManuscriptOrIbdFilesSuffix(
          allUpLoadFileList,
          isManuscript ? 'manuscript' : 'manuscriptIbd'
        );
        if (FileSuffixWrong.code === FAILED_CON) {
          resolve(FileSuffixWrong);
          return;
        }
      }

      // 名称校验全通过
      initFilePath(
        filePaths.map((item) => ({
          taskFileOrDirInfo: taskFileInfos.get(item),
          pathType,
          isFolder: taskFileInfos.get(item).isDirectory,
          rootNetPath,
          osPath: item,
          relativePath: path.basename(item),
          netPath: path.basename(item),
          uploadItems: allFileInfos.get(item),
          target,
        }))
      );
      // 检查重复命名，则chooseFilesOrFolders这个函数的任务完成，后面的异常在checkDuplicateName中处理
      checkDuplicateName(neid, false, false);
      resolve({
        code: SUCCESS_CON,
      });
    } else {
      resolve({ code: FAILED_CON });
    }
  });
};
