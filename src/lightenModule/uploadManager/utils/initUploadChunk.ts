import { FAILED_CON, SUCCESS_CON } from '@common/constant';
import Request from '@utils/request';
import { getErrorMsg } from '@utils/stringUtils';

// 统一存储分片上传初始化接口
export const initUploadChunk = (
  oldFileId: number,
  controller?: AbortController
) => {
  return new Promise<ICommonFunReturn & { key?: string; uploadId?: string }>(
    async (resolve) => {
      try {
        const url = `/pcfile/transfer/initUploadChunk`;
        const res = await Request<{ key: string; uploadId: string }>({
          url,
          method: 'GET',
          params: { oldFileId },
          signal: controller ? controller.signal : undefined,
        });
        if (
          res.code === SUCCESS_CON &&
          res.data?.t?.key &&
          res.data?.t?.uploadId
        ) {
          resolve({
            code: SUCCESS_CON,
            key: res.data.t.key,
            uploadId: res.data.t.uploadId,
          });
        } else {
          // 取消或者失败的情况
          resolve({
            code: res.code,
            errMsg: res.errMsg,
          });
        }
      } catch (error) {
        resolve({
          code: FAILED_CON,
          errMsg: getErrorMsg(error),
        });
      }
    }
  );
};
