import { FAILED_CON, SUCCESS_CON } from '@common/constant';
import { mainIPC } from '@htElectronSDK/main/ipc';
import {
  LINKFLOW_ON_SHOW_MODAL,
  LINKFLOW_ON_SHOW_RENAME_MODAL,
} from '@lightenSDK/constant/global';
import { IFileOrDirInfo } from '@utils/folderUtils';
import Request from '@utils/request';
import { getErrorMsg } from '@utils/stringUtils';
import { CREATE_TASKS_AND_RUN, uploaderEvent } from '..';
import { TCheckIFileOrDirInfo } from './checkFile';
import { uploadEmptyDir } from './uploadEmptyDir';

export type TUploadTaskPath = {
  isFolder: boolean;
  pathType: TFilePathType;
  // 当前文件或者文件夹的信息
  taskFileOrDirInfo: IFileOrDirInfo;
  // 上传文件的系统路径
  osPath: string;
  // 相对路径 选择D://xx//yy，其实是上传yy文件夹到远端路径，所以相对路径为yy
  relativePath: string;
  // 上传文件的目标网盘相对路径，如果需要重命名，则relativePath为xxx，则netPath为xxx(1)，如果不重命名，则relativePath和netPath一致
  netPath: string;
  // 重名文件的neid，文件新增版本需要用到
  oldFileId?: number;
  // uploadItems, path中需要上传的文件
  rootNetPath: string; // 上传网盘目录的完整path，个人文件夹不需要夹工号，后台自动拼接
  uploadItems: TCheckIFileOrDirInfo[];
  target: unknown;
};

type TCheckFileExistsReturn = ICommonFunSimpReturn & { duplicateNeid?: number };

/**
 * allFilePaths 全部需要处理的paths(用户在文件选择框中选择的文件夹或者文件列表)
 * taskFilePaths 最终要生成task的paths，如果没有和线上重复，则path直接从allFilePaths取出，加到taskFilePaths
 * 如果和线上重复，文件夹选择重命名，文件选择新增版本的情况下，加入taskFilePaths，如果选择取消，则直接从allFilePaths中删除
 * map的key为osPath，因为一个osPath对应生成一个task
 */
const allFilePaths: Map<string, TUploadTaskPath> = new Map();
const taskFilePaths: Map<string, TUploadTaskPath> = new Map();

const renameFolder = (name: string) => {
  const date = new Date();
  return `${name}(${date.getFullYear()}${
    date.getMonth() + 1
  }${date.getDate()}${date.getHours()}${date.getMinutes()}${date.getSeconds()}${date.getMilliseconds()})`;
};

export const initFilePath = (vFilePaths: TUploadTaskPath[]) => {
  allFilePaths.clear();
  for (const filePath of vFilePaths) {
    allFilePaths.set(filePath.osPath, filePath);
  }
  taskFilePaths.clear();
};

export const preFinished = () => {
  if (allFilePaths.size !== 0) {
    mainIPC.send({
      id: LINKFLOW_ON_SHOW_MODAL,
      params: {
        type: 'warning',
        title: '请稍等',
        content: `上一个上传任务正在处理或取消，还剩${allFilePaths.size}项任务未完成，请稍后再试。(如发现处理进度长时间没有变化，请重启简富后，再次启动上传任务)`,
        okText: '确定',
      },
    });
  }
  return allFilePaths.size === 0;
};

/**
 * 查看网盘当前路径是否存在需要上传的路径或者文件
 * @param neid 网盘路径id
 * @param name 文件名
 * @returns 是否重复
 */
const checkWhetherFileExists = (neid: number, name: string) => {
  return new Promise<TCheckFileExistsReturn>(async (resolve) => {
    try {
      const result = await Request<number>({
        url: `/pcfile/transfer/whetherFileExists?directoryPathId=${neid}&fileName=${encodeURIComponent(
          name
        )}`,
        method: 'GET',
      });
      if (result.code === SUCCESS_CON) {
        // 业务异常处理
        if (result.data.code !== 0) {
          resolve({ code: FAILED_CON, errMsg: result.data?.message });
        } else {
          resolve({ code: SUCCESS_CON, duplicateNeid: result.data?.t });
        }
      } else {
        resolve({ code: FAILED_CON, errMsg: result.errMsg });
      }
    } catch (error) {
      resolve({ code: FAILED_CON, errMsg: getErrorMsg(error) });
    }
  });
};

/**
 * 如果能通过applyAll和reNameOrAddVersion处理，则处理完返回true，如果需要用户确认，返回false
 * @param filePath
 * @param duplicateNeid
 * @param applyAll
 * @param reNameOrAddVersion
 * @returns
 */
const dealDuplicateCase = (
  osPath: string,
  duplicateNeid: number,
  applyAll: boolean,
  reNameOrAddVersion: boolean
) => {
  // 有重复，分情况处理,
  // 文件夹重复需要重命名，所以不需要记录网盘重名的neid
  // 文件重复，是新增版本的处理，需要记录网盘重名的neid
  const curPath = allFilePaths.get(osPath);
  // 文件需要保存oldFileId，不管用户新增不新增都先保存，confirmDuplicateDeal中就不需要设置文件的oldFileId逻辑，用户取消则直接删除任务
  if (!curPath.isFolder) {
    curPath.oldFileId = duplicateNeid;
  }
  if (applyAll) {
    if (reNameOrAddVersion) {
      // 重命名或者新增版本
      if (curPath.isFolder) {
        curPath.netPath = renameFolder(curPath.netPath);
      }
      taskFilePaths.set(osPath, curPath);
      allFilePaths.delete(osPath);
    } else {
      // 取消上传，则直接删除这个path
      allFilePaths.delete(osPath);
    }
    return true;
  } else {
    // 没有处理状态可继承，则交给用户去处理
    return false;
  }
};

/**
 * 校验是否重名
 * @param neid 网盘路径id
 * @param reNameOrAddVersion 重命名或者新增版本为true，取消上传为false
 * @param applyAll 为后续文件或者文件夹统一选择reNameOrAddVersion
 */
// eslint-disable-next-line max-statements
export const checkDuplicateName = async (
  neid: number,
  reNameOrAddVersion: boolean,
  applyAll: boolean
) => {
  // 如果allFilePaths为空，则说明这批任务已经结束
  const taskPaths = allFilePaths.values();
  // 重名处理结果，需要用户确认，结果为false，如果处理异常，结果也为false
  let dealResult = true;
  for (const filePath of taskPaths) {
    const checkRes = await checkWhetherFileExists(neid, filePath.relativePath);

    if (checkRes.code !== SUCCESS_CON) {
      // 服务报错，通知渲染进程，并结束这批任务
      dealResult = false;
      allFilePaths.clear();
      taskFilePaths.clear();
      mainIPC.send({
        id: LINKFLOW_ON_SHOW_MODAL,
        params: {
          type: 'error',
          title: 'checkWhetherFileExists 创建上传任务失败',
          content: checkRes.errMsg,
          okText: '确定',
        },
      });
      break;
    }

    if (checkRes.duplicateNeid) {
      const dealRes = dealDuplicateCase(
        filePath.osPath,
        checkRes.duplicateNeid,
        applyAll,
        reNameOrAddVersion
      );
      if (!dealRes) {
        // 结果为false， 需要用户确认，
        // 让前端显示message
        // 兼容之前的写法，tarket应该是拼写错误
        mainIPC.send({
          id: LINKFLOW_ON_SHOW_RENAME_MODAL,
          params: {
            fileName: filePath.taskFileOrDirInfo.name,
            isFolder: filePath.isFolder,
            duplicateNeid: checkRes.duplicateNeid,
            tarket: filePath.target,
            fileJudge: filePath.osPath, // 上传的单个文件
            isApplyAll: true,
          },
        });
        dealResult = false;
        break;
      }
    } else {
      // 加到taskFilePaths，并从allFilePaths删除
      taskFilePaths.set(filePath.osPath, allFilePaths.get(filePath.osPath));
      allFilePaths.delete(filePath.osPath);
    }
  }

  // 重名校验逻辑结束，开始创建上传任务
  if (dealResult && taskFilePaths.size > 0) {
    // 检查是否存在空文件夹并创建
    for (const item of taskFilePaths.values()) {
      if (item.isFolder) {
        const { osPath, rootNetPath, taskFileOrDirInfo, pathType } = item;
        const upRes = await uploadEmptyDir({
          dirPath: osPath,
          netPath: rootNetPath,
          name: taskFileOrDirInfo.name,
          pathType,
        });
        if (upRes.code !== SUCCESS_CON) {
          // 如果创建文件夹失败，清除这批任务
          allFilePaths.clear();
          taskFilePaths.clear();
          mainIPC.send({
            id: LINKFLOW_ON_SHOW_MODAL,
            params: {
              type: 'error',
              title: 'uploadEmptyDir 创建上传任务失败',
              content: upRes.errMsg,
              okText: '确定',
            },
          });
          return;
        }
      }
    }
    uploaderEvent.emit(CREATE_TASKS_AND_RUN, taskFilePaths, neid);
  }
};

/**
 * 用户选择以后的确认操作
 * @param neid 网盘路径id
 * @param path 文件相对路径
 * @param reNameOrAddVersion
 * @param applyAll
 */
export const confirmDuplicateDeal = async (
  neid: number,
  curOsPath: string,
  reNameOrAddVersion: boolean,
  applyAll: boolean
) => {
  if (reNameOrAddVersion) {
    const curPath = allFilePaths.get(curOsPath);
    // 重命名
    if (curPath.isFolder) {
      curPath.netPath = renameFolder(curPath.netPath);
    }
    taskFilePaths.set(curOsPath, curPath);
    allFilePaths.delete(curOsPath);
  } else {
    // 取消上传，则直接删除这个path
    allFilePaths.delete(curOsPath);
  }
  checkDuplicateName(neid, reNameOrAddVersion, applyAll);
};
