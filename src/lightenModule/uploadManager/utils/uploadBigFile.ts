import { CANCELED_CON, FAILED_CON, SUCCESS_CON } from '@common/constant';
import { TUploadProcessFun } from '@utils/fileUtils/uploadFile/uploadFile';
import { getErrorMsg } from '@utils/stringUtils';
import { IUploadItem, IUploadTask } from '..';
import { completeUploadChunk } from './completeUploadChunk';
import { getUploadNeid } from './createNetFolder';
import { initUploadChunk } from './initUploadChunk';
import { uploadFile } from './uploadFile';
import { IUploadFileRet } from './uploadSmallFile';

export const uploadBigFile = async (
  vTask: IUploadTask,
  uploadItem: IUploadItem,
  curController: AbortController,
  getUploadProcessFun: TUploadProcessFun<IUploadItem>
) => {
  return new Promise<IUploadFileRet>(
    // eslint-disable-next-line max-statements
    async function (resolve) {
      // 断点续传
      if (!uploadItem.key || !uploadItem.uploadId) {
        const initRes = await initUploadChunk(vTask.oldFileId, curController);
        if (initRes.code === SUCCESS_CON) {
          uploadItem.key = initRes.key;
          uploadItem.uploadId = initRes.uploadId;
        } else if (initRes.code === CANCELED_CON) {
          resolve({
            code: CANCELED_CON,
            errMsg: 'initUploadChunk取消！',
          });
          return;
        } else {
          // task中一个文件上传失败
          resolve({
            code: FAILED_CON,
            errMsg: getErrorMsg(initRes.errMsg),
          });
          return;
        }
        // 确保返回列表为空
        uploadItem.returnValues = [];
      }
      const resP = await uploadFile(
        uploadItem,
        getUploadProcessFun,
        curController
      );
      if (resP.code === SUCCESS_CON) {
        const getNeidRes = await getUploadNeid(
          uploadItem,
          vTask,
          curController
        );
        if (getNeidRes.code !== SUCCESS_CON) {
          resolve({
            code: getNeidRes.code,
            errMsg: getNeidRes.errMsg,
          });
          return;
        }
        const uploadNeid = getNeidRes.neid;
        const compRes = await completeUploadChunk(
          uploadItem,
          uploadNeid,
          vTask.oldFileId,
          [...uploadItem.returnValues],
          curController
        );
        if (compRes.code === SUCCESS_CON) {
          resolve({ code: SUCCESS_CON, neid: compRes.neid });
        } else {
          resolve({
            code: FAILED_CON,
            errMsg: compRes.errMsg,
          });
        }
      } else if (resP.code === CANCELED_CON) {
        resolve({
          code: CANCELED_CON,
          errMsg: '取消上传！',
        });
      } else {
        // task中一个文件上传失败
        resolve({
          code: FAILED_CON,
          errMsg: resP.errMsg,
        });
      }
    }
  );
};
