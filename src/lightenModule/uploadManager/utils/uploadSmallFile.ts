import { FAILED_CON, SUCCESS_CON } from '@common/constant';
import LOG from '@htElectronSDK/main/log';
import Request from '@utils/request';
import { getErrorMsg } from '@utils/stringUtils';
import FormData from 'form-data';
import { createReadStream } from 'node:fs';
import { IUploadItem, IUploadTask } from '..';
import { getUploadNeid } from './createNetFolder';

export interface IUploadFileRet {
  code: TFunReturnFlag;
  errMsg?: string;
  neid?: number;
}

/**
 * 小文件一次性上传接口
 * @param uploadInfo 需要上传的本地文件信息
 * @param target 需要上传的目标信息
 * @returns
 */
export const uploadSmallFile = (
  uploadInfo: IUploadItem,
  vTask: IUploadTask,
  controller?: AbortController
) => {
  return new Promise<IUploadFileRet>(async (resolve) => {
    try {
      const getNeidRes = await getUploadNeid(uploadInfo, vTask, controller);
      if (getNeidRes.code !== SUCCESS_CON) {
        resolve({
          code: getNeidRes.code,
          errMsg: getNeidRes.errMsg,
        });
        return;
      }
      const uploadNeid = getNeidRes.neid;
      const form = new FormData();
      // Create a stream from some character device.
      const stream = createReadStream(uploadInfo.fileInfo.path);
      form.append('file', stream);
      form.append('fileName', uploadInfo.fileInfo.name);
      form.append('filePathId', uploadNeid);
      vTask.oldFileId && form.append('oldFileId', vTask.oldFileId);

      const res = await Request<{
        neid: number;
      }>({
        url: `/pcfile/transfer/uploadFileS3`,
        method: 'POST',
        signal: controller ? controller.signal : undefined,
        data: form,
        headers: {
          ...form.getHeaders(),
        },
      });
      if (res.code === SUCCESS_CON) {
        if (res.data?.t?.neid) {
          resolve({ code: res.code, neid: res.data.t.neid });
        } else {
          LOG.error(
            '/pcfile/transfer/uploadFileS3报错，入参=',
            'fileName',
            uploadInfo.fileInfo.name,
            'filePathId',
            uploadNeid,
            'oldFileId',
            vTask.oldFileId,
            '，响应值=',
            res
          );
          resolve({
            code: FAILED_CON,
            errMsg: res.errMsg || res.data?.message || '上传失败，项目空间内存不足!',
          });
        }
      } else {
        // 请求失败或取消
        resolve({ code: res.code, errMsg: res.errMsg });
      }
    } catch (error) {
      resolve({ code: FAILED_CON, errMsg: getErrorMsg(error) });
    }
  });
};
