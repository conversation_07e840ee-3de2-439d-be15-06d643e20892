import { mainIPC } from '@htElectronSDK/main/ipc';
import { LINKFLOW_MESSAGE_ERROR } from '@lightenSDK/constant/global';
import { LINKFLOW_ON_UPLOAD_ACHIEVEMENT_CALLBACK } from '@lightenSDK/constant/upload';

const onUploadSuccessCb = (msg) => {
  const { type, result, uploadItem, uploadTask } = msg;
  if (type === 'uploadItem') {
    const isUploadAchievement =
      uploadTask?.rootNetPath?.includes?.('/协作空间/尽调任务成果');
    if (isUploadAchievement) {
      const params = {
        localPath: uploadItem.fileInfo.path,
        targetPath: uploadTask.rootNetPath,
        neid: result.neid,
      };
      mainIPC.send({
        id: LINKFLOW_ON_UPLOAD_ACHIEVEMENT_CALLBACK,
        params,
      });
    }
  }
};

const onUploadFailedCb = (msg) => {
  const { type, result, uploadTask } = msg;
  if (type === 'uploadItem') {
    const isUploadAchievement =
      uploadTask?.rootNetPath?.includes?.('/协作空间/尽调任务成果');
    if (isUploadAchievement) {
      mainIPC.send({
        id: LINKFLOW_MESSAGE_ERROR,
        params: { text: result?.errMsg || '' },
      });
    }
  }
};

export const uploadCallback = {
  onUploadSuccessCb,
  onUploadFailedCb,
};
