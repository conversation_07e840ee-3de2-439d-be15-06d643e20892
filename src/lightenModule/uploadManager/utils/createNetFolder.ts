import { FAILED_CON, SUCCESS_CON } from '@common/constant';
import Request from '@utils/request';
import { getErrorMsg } from '@utils/stringUtils';
import path from 'path';
import { IUploadItem, IUploadTask } from '..';

// 在网盘指定路径创建文件夹
export const createFolder: (
  netPath: string,
  pathType: TFilePathType,
  rootNetPath: string,
  controller?: AbortController
) => Promise<ICommonFunReturn & { neid?: number }> = (
  netPath,
  pathType,
  rootNetPath,
  controller
) => {
  return new Promise(async (resolve) => {
    try {
      if (!netPath || netPath === path.sep) {
        resolve({ code: FAILED_CON, errMsg: '传入路径不能为空' });
        return;
      }
      // filter用来去除最后一个空字符串
      const stepPaths = netPath.split(path.sep).filter((aPath) => !!aPath);
      const cpath = `${rootNetPath}/${stepPaths
        .slice(0, stepPaths.length - 1)
        .join('/')}`;
      const cname = stepPaths[stepPaths.length - 1];
      const res = await Request<{ neid: number }>({
        url: '/pcfile/createFileOrFolder',
        method: 'post',
        data: {
          filetype: 'folder',
          name: cname,
          path: cpath,
          path_type: pathType,
        },
        signal: controller ? controller.signal : undefined,
      });
      if (res.code === SUCCESS_CON) {
        resolve({
          code: SUCCESS_CON,
          neid: res.data?.t?.neid,
        });
      } else {
        resolve({
          code: res.code,
          errMsg: res.errMsg,
        });
      }
    } catch (error) {
      resolve({ code: FAILED_CON, errMsg: getErrorMsg(error) });
    }
  });
};

export const getUploadNeid = (
  uploadInfo: IUploadItem,
  vTask: IUploadTask,
  controller: AbortController
) => {
  // 分情况讨论，如果是文件的上传任务，不需要创建文件夹，上传的neid就是task的neid（文件所在的目录id）
  // 如果是文件夹的上传任务，则需要先创建父目录，拿到neid，再上传文件，父目录可能需要重命名
  // 如果task的文件name和uploadItem的文件name一样，说明这个task就是文件上传的task
  return new Promise<ICommonFunReturn & { neid?: number }>(async (resolve) => {
    if (vTask.taskFileOrDirInfo.path === uploadInfo.fileInfo.path) {
      // 当前任务为文件上传任务，task list中只有一个上传文件，上传目标文件的neid就是task的neid
      resolve({ code: SUCCESS_CON, neid: vTask.neid });
    } else {
      // 上传文件夹为D://aa//bb, 其中上传的一个文件路径为D://aa//bb//xx//yy//cc.pdf
      // prefix是D://aa//，relPath为bb//xx//yy//cc.pdf, dir为[bb, xx, yy]
      // 如果文件需要重命名，则vTask.netPath为bb(1)，如果不需要重命名，vTask.netPath为bb
      // 重命名的情况，需要把netPath的bb//xx//yy//转为bb(1)//xx//yy//
      const relPath = path.relative(
        uploadInfo.fileInfo.prefix,
        uploadInfo.fileInfo.path
      );
      const dir = relPath
        .split(path.sep)
        .filter((item) => item !== uploadInfo.fileInfo.name);
      let uploadNetPath = dir.join(path.sep);
      if (dir[0] !== vTask.netPath) {
        // 存在重命名的情况
        uploadNetPath = [vTask.netPath].concat(dir.slice(1)).join(path.sep);
      }

      const res = await createFolder(
        uploadNetPath,
        vTask.pathType,
        vTask.rootNetPath,
        controller
      );
      resolve(res);
    }
  });
};
