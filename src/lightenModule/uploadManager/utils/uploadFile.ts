import { FAILED_CON, SUCCESS_CON } from '@common/constant';
import {
  IUploadChunkFunResp,
  TUploadChunkDIYFun,
} from '@utils/fileUtils/uploadFile/uploadChunk';
import {
  IUploadInfo,
  TGetUploadParamsFun,
  TUploadProcessFun,
  uploadByMultipart,
} from '@utils/fileUtils/uploadFile/uploadFile';
import Request from '@utils/request';
import { getErrorMsg } from '@utils/stringUtils';
import FormData from 'form-data';
import { createReadStream } from 'node:fs';
import { IUploadItem, UPLOAD_PIECE_SIZE } from '..';
import { IUploadFileRet } from './uploadSmallFile';

const UPLOAD_HIGH_WARTER = 2 * 5 * 1024 * 1024; // 分割文件水位，大于网盘分片

export interface ILightenUploadFileRet<T> extends IUploadFileRet {
  resList?: T[];
}

export interface ILightenUploadChunkRes {
  eTag: string;
  partNum: number;
}

const uploadChunkFun: TUploadChunkDIYFun = (params, index) => {
  return new Promise<IUploadChunkFunResp<ILightenUploadChunkRes>>(
    async (resolve) => {
      try {
        const streamRe = await Request<{
          eTag: string;
          partNum: number;
        }>(params);
        if (streamRe.code === SUCCESS_CON) {
          resolve({
            status: SUCCESS_CON,
            t: { eTag: streamRe.data.t.eTag, partNum: streamRe.data.t.partNum },
          });
        } else {
          resolve({
            status: streamRe.code,
            errorMsg: streamRe.errMsg,
          });
        }
      } catch (error: any) {
        resolve({ status: 'FAILED', errorMsg: getErrorMsg(error) });
      }
    }
  );
};

const genGetUploadParamsFun = (
  uploadInfo: IUploadInfo<IUploadItem>,
  uploadFileInfo: IUploadItem,
  controller?: AbortController
) => {
  const getUploadParams: TGetUploadParamsFun<IUploadItem> = async (
    vUploadFileInfo,
    chunkIndex
  ) => {
    return new Promise(async (resolve) => {
      try {
        const form = new FormData();
        const stream = createReadStream(uploadFileInfo.fileInfo.path, {
          start: chunkIndex * UPLOAD_PIECE_SIZE,
          end: Math.min(
            vUploadFileInfo.file.fileInfo.size - 1,
            (chunkIndex + 1) * UPLOAD_PIECE_SIZE - 1
          ),
          highWaterMark: UPLOAD_HIGH_WARTER,
        });
        form.append('key', vUploadFileInfo.file.key);
        form.append('uploadId', vUploadFileInfo.file.uploadId);
        form.append('file', stream);
        form.append(
          'lastPart',
          chunkIndex === uploadInfo.chunkTotalNumber - 1 ? 1 : 0
        );
        // partNum从1开始
        form.append('partNum', (chunkIndex + 1).toString());
        const serParams = {
          url: `/pcfile/transfer/uploadChunkS3`,
          method: 'POST',
          signal: controller ? controller.signal : undefined,
          data: form,
          headers: {
            ...form.getHeaders(),
          },
        };
        resolve(serParams);
      } catch (error) {
        resolve({ errInfo: error });
      }
    });
  };
  return getUploadParams;
};

/**
 * 大文件上传接口
 * @param uploadFileInfo 需要上传的本地文件信息
 * @param target 需要上传的目标信息
 * @returns
 */
export const uploadFile = (
  uploadFileInfo: IUploadItem,
  uploadProcessFun: TUploadProcessFun<IUploadItem>,
  controller?: AbortController
) => {
  return new Promise<
    ILightenUploadFileRet<IUploadChunkFunResp<ILightenUploadChunkRes>>
  >(async (resolve) => {
    try {
      // 文件块上传成功以后，会在uploadFileInfo.returnValues中记录，所以上传的start为uploadFileInfo.returnValues.length
      const uploadInfo: IUploadInfo<IUploadItem> = {
        start: uploadFileInfo.returnValues.length,
        chunkTotalNumber: Math.ceil(
          uploadFileInfo.fileInfo.size / UPLOAD_PIECE_SIZE
        ),
        file: uploadFileInfo,
      };
      const uploadRe = await uploadByMultipart(
        uploadChunkFun,
        uploadInfo,
        genGetUploadParamsFun(uploadInfo, uploadFileInfo, controller),
        uploadProcessFun
      );
      resolve({
        code: uploadRe.code,
        resList: uploadRe.t as IUploadChunkFunResp<ILightenUploadChunkRes>[],
        errMsg: uploadRe.message,
      });
    } catch (error) {
      resolve({
        code: FAILED_CON,
        errMsg: getErrorMsg(error),
      });
    }
  });
};
