import {
  ADD_CON,
  COMPLETE_CON,
  PAUSED_CON,
  RUNNING_CON,
  WAITING_CON,
} from '@common/constant';
import { LowSync } from 'lowdb/lib';
import {
  IUserUploadStore,
  getUserUploadListStore,
} from '@globalState/localStore/uploadListStore';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { TTaskChangeCallBackFun } from '@lightenModule/taskManager';
import { IUploadTask } from '@lightenModule/uploadManager';
import { LINKFLOW_TRANSFER_LIST_INFO } from '@lightenSDK/constant/global';
import { getUploadInfo } from '../getUploadInfo';

export const uploadTaskChangCallBack: TTaskChangeCallBackFun<IUploadTask> = (
  changeTaskInfos
) => {
  const uploadStore = getUserUploadListStore();
  uploadStore.read();
  for (const changeTaskInfo of changeTaskInfos) {
    updateBatchStore(uploadStore, changeTaskInfo);
  }
  uploadStore.write();
  mainIPC.send({
    id: LINKFLOW_TRANSFER_LIST_INFO,
    params: getUploadInfo(),
  });
};

const updateBatchStore = (
  uploadStore: LowSync<IUserUploadStore>,
  changeTaskInfo: {
    changeType: TCOMPLETE_FLAG | TPAUSED_FLAG | TRUNNING_FLAG | TWAITING_FLAG;
    operationType: TADD_FLAG | TDELETE_FLAG;
    Task: IUploadTask;
  }
) => {
  switch (changeTaskInfo.changeType) {
    case RUNNING_CON:
      if (changeTaskInfo.operationType === ADD_CON) {
        uploadStore.data.processingList?.push(changeTaskInfo.Task);
      } else {
        uploadStore.data.processingList =
          uploadStore.data.processingList?.filter(
            (item) => item.key !== changeTaskInfo.Task.key
          );
      }
      break;
    case WAITING_CON:
      if (changeTaskInfo.operationType === ADD_CON) {
        uploadStore.data.waitingList?.push(changeTaskInfo.Task);
      } else {
        uploadStore.data.waitingList = uploadStore.data.waitingList?.filter(
          (item) => item.key !== changeTaskInfo.Task.key
        );
      }
      break;
    case PAUSED_CON:
      if (changeTaskInfo.operationType === ADD_CON) {
        uploadStore.data.pauseList?.push(changeTaskInfo.Task);
      } else {
        uploadStore.data.pauseList = uploadStore.data.pauseList?.filter(
          (item) => item.key !== changeTaskInfo.Task.key
        );
      }
      break;
    case COMPLETE_CON:
      if (changeTaskInfo.operationType === ADD_CON) {
        uploadStore.data.completeList?.push(changeTaskInfo.Task);
      } else {
        uploadStore.data.completeList = uploadStore.data.completeList?.filter(
          (item) =>
            item.key !== changeTaskInfo.Task.key ||
            item.startTime !== changeTaskInfo.Task.startTime
        );
      }
      break;
    default:
      break;
  }
};
