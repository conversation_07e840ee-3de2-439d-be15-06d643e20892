import { WAITING_CON } from '@common/constant';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { CTaskManager } from '@lightenModule/taskManager';
import { IUploadItem, IUploadTask } from '@lightenModule/uploadManager';
import { LINKFLOW_MESSAGE_INFO } from '@lightenSDK/constant/global';
import moment from 'moment';
import { TUploadTaskPath } from '../dealDuplicateName';

export const getCreateUploadTasksAndRunFun = (
  uploadManager: CTaskManager<string, IUploadTask>
) => {
  /**
   * 上传的校验逻辑结束，创建任务并添加到队列
   * @param vOriginInfos 重名校验以后的数据
   * @param neid 上传的路径id
   */
  const createTasksAndRun = (
    vOriginInfos: Map<string, TUploadTaskPath>,
    neid: number
  ) => {
    for (const originInfo of vOriginInfos.values()) {
      const taskUploadItems: IUploadItem[] = [];
      let totalSize = 0;
      for (const fileInfo of originInfo.uploadItems) {
        totalSize += fileInfo.size;
        const curUploadItem: IUploadItem = {
          fileInfo,
          status: WAITING_CON,
          returnValues: [],
        };
        taskUploadItems.push(curUploadItem);
      }
      const curUploadTask: IUploadTask = {
        key: `${neid}-${originInfo.osPath}`,
        taskFileOrDirInfo: originInfo.taskFileOrDirInfo,
        list: taskUploadItems,
        neid,
        oldFileId: originInfo.oldFileId,
        rootNetPath: originInfo.rootNetPath,
        totalSize,
        status: WAITING_CON,
        pathType: originInfo.pathType,
        netPath: originInfo.netPath,
        startTime: moment().format('YYYY_MM_DD_hh_mm_ss_SSS'),
        target: originInfo.target,
      };
      uploadManager.addTaskToRun(curUploadTask.key, curUploadTask);
    }
    mainIPC.send({
      id: LINKFLOW_MESSAGE_INFO,
      params: {
        text: '文件已添加至传输列表',
      },
    });
  };
  return createTasksAndRun;
};
