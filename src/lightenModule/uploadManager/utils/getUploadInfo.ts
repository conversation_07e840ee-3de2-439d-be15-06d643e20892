import { getUserUploadListStore } from '@globalState/localStore/uploadListStore';

export const getUploadInfo = () => {
  const uploadStore = getUserUploadListStore();
  uploadStore.read();
  return {
    uploadList: {
      processingList: uploadStore.data.processingList,
      waitingList: uploadStore.data.waitingList,
      pauseList: uploadStore.data.pauseList,
      completeList: uploadStore.data.completeList,
    },
  };
};
