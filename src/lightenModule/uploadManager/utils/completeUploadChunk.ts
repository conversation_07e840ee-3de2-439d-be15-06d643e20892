import { FAILED_CON, SUCCESS_CON } from '@common/constant';
import LOG from '@htElectronSDK/main/log';
import Request from '@utils/request';
import { getErrorMsg } from '@utils/stringUtils';
import { IUploadItem } from '..';
import { ILightenUploadChunkRes } from './uploadFile';
import { IUploadFileRet } from './uploadSmallFile';

export const completeUploadChunk = (
  uploadFileInfo: IUploadItem,
  neid: number,
  oldFileId: number,
  vPartEtagDTOList: ILightenUploadChunkRes[],
  controller?: AbortController
) => {
  return new Promise<IUploadFileRet>(async (resolve) => {
    try {
      const reqData = {
        partEtagDTOList: vPartEtagDTOList,
        key: uploadFileInfo.key,
        uploadId: uploadFileInfo.uploadId,
        fileName: uploadFileInfo.fileInfo.name,
        filePathId: neid,
        oldFileId,
      };
      const res = await Request<{
        neid: number;
      }>({
        url: '/pcfile/transfer/completeUploadChunk',
        method: 'POST',
        data: reqData,
        signal: controller ? controller.signal : undefined,
      });
      if (res.code === SUCCESS_CON) {
        if (res.data?.t?.neid) {
          resolve({
            code: res.code,
            neid: res.data.t.neid,
          });
        } else {
          LOG.error(
            '/pcfile/transfer/completeUploadChunk报错，入参=',
            reqData,
            '，响应值=',
            res
          );
          resolve({
            code: FAILED_CON,
            errMsg: res.errMsg || res.data?.message || '上传失败，项目空间内存不足',
          });
        }
      } else {
        resolve({
          code: res.code,
          errMsg: res.errMsg,
        });
      }
    } catch (error) {
      resolve({
        code: FAILED_CON,
        errMsg: getErrorMsg(error),
      });
    }
  });
};
