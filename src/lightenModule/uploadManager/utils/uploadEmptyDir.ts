import { FAILED_CON, SUCCESS_CON } from '@common/constant';
import { getErrorMsg } from '@utils/stringUtils';
import { readdir, stat } from 'fs/promises';
import path from 'path';
import { createFolder } from './createNetFolder';

/**
 * 上传文件夹时，递归检查是否存在空文件夹并创建
 * @param options -
 * - dirPath: 当前文件夹路径
 * - netPath：网盘父路径
 * - name：文件夹名称
 */
export const uploadEmptyDir = async (options: {
  dirPath: string;
  netPath: string;
  name: string;
  pathType: 'self' | 'ent';
}): Promise<ICommonFunReturn> => {
  try {
    const { dirPath, netPath, name, pathType } = options;
    const fileStat = await stat(dirPath);
    if (fileStat.isDirectory()) {
      const files = await readdir(dirPath);
      if (files.length === 0) {
        const result = await createFolder(name, pathType, netPath);
        if (result.code === FAILED_CON) {
          return result;
        }
      } else {
        for (const file of files) {
          const result = await uploadEmptyDir({
            dirPath: path.join(dirPath, file),
            netPath: `${netPath}/${name}`,
            name: file,
            pathType,
          });
          if (result.code === FAILED_CON) {
            return result;
          }
        }
      }
    }
  } catch (error) {
    return { code: FAILED_CON, errMsg: getErrorMsg(error) };
  }
  return { code: SUCCESS_CON };
};
