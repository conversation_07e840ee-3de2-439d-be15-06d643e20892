import { getUserUploadListStore } from '@globalState/localStore/uploadListStore';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { LINKFLOW_TRANSFER_LIST_INFO } from '@lightenSDK/constant/global';
import { TUploadProcessFun } from '@utils/fileUtils/uploadFile/uploadFile';
import moment, { Moment } from 'moment';
import { IUploadItem, IUploadTask, UPLOAD_PIECE_SIZE } from '..';
import { getUploadInfo } from './getUploadInfo';
import { ILightenUploadChunkRes } from './uploadFile';

export const getUploadProcessFun = (vTask: IUploadTask, startTime: Moment) => {
  let tStartTime = startTime;
  const uploadProcessFun: TUploadProcessFun<IUploadItem> = (
    curUploadItemInfo,
    chunkIndex,
    resInfo
  ) => {
    const uploadStore = getUserUploadListStore();
    uploadStore.read();
    const curUploadItem = vTask.list.find(
      (item) => item.fileInfo.path === curUploadItemInfo.file.fileInfo.path
    );
    if (curUploadItem) {
      curUploadItem.returnValues.push(
        (resInfo as ICommonFunReturn & { t: ILightenUploadChunkRes }).t
      );
      uploadStore.data.processingList
        ?.find((item) => item.key === vTask.key)
        ?.list?.find(
          (item) => item.fileInfo.path === curUploadItem.fileInfo.path
        )
        ?.returnValues?.push(
          (resInfo as ICommonFunReturn & { t: ILightenUploadChunkRes }).t
        );
    }
    const timeGapSecs = moment().diff(tStartTime, 'milliseconds');
    tStartTime = moment();
    if (chunkIndex === curUploadItemInfo.chunkTotalNumber - 1) {
      // 最后一片
      const curCompSize =
        curUploadItemInfo.file.fileInfo.size - chunkIndex * UPLOAD_PIECE_SIZE;
      vTask.completedSize = vTask.completedSize
        ? vTask.completedSize + curCompSize
        : curCompSize;
      vTask.rate = curCompSize / timeGapSecs;
    } else {
      vTask.completedSize = vTask.completedSize
        ? vTask.completedSize + UPLOAD_PIECE_SIZE
        : UPLOAD_PIECE_SIZE;
      vTask.rate = UPLOAD_PIECE_SIZE / timeGapSecs;
    }
    const fTask = uploadStore.data.processingList?.find(
      (item) => item.key === vTask.key
    );
    if (fTask) {
      fTask.completedSize = vTask.completedSize;
      fTask.rate = vTask.rate;
    }

    uploadStore.write();
    mainIPC.send({
      id: LINKFLOW_TRANSFER_LIST_INFO,
      params: getUploadInfo(),
    });
  };
  return uploadProcessFun;
};

export const uploadProcess4SmallFile = (vTask: IUploadTask) => {
  const uploadStore = getUserUploadListStore();
  uploadStore.read();
  const fTask = uploadStore.data.processingList?.find(
    (item) => item.key === vTask.key
  );
  if (fTask) {
    fTask.completedSize = vTask.completedSize;
    fTask.rate = vTask.rate;
  }
  uploadStore.write();
  mainIPC.send({
    id: LINKFLOW_TRANSFER_LIST_INFO,
    params: getUploadInfo(),
  });
};
