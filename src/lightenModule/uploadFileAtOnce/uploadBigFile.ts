import {
  CANCELED_CON,
  FAILED_CON,
  RUNNING_CON,
  SUCCESS_CON,
} from '@common/constant';
import { IUploadItem } from '@lightenModule/uploadManager';
import { completeUploadChunk } from '@lightenModule/uploadManager/utils/completeUploadChunk';
import { initUploadChunk } from '@lightenModule/uploadManager/utils/initUploadChunk';
import { uploadFile } from '@lightenModule/uploadManager/utils/uploadFile';
import { TUploadProcessFun } from '@utils/fileUtils/uploadFile/uploadFile';
import { IFileOrDirInfo } from '@utils/folderUtils';

export const uploadBigFile = async (
  curFile: IFileOrDirInfo,
  netPathNeid: number,
  getUploadProcessFun: TUploadProcessFun<IUploadItem>,
  oldFileId?: number,
  controller?: AbortController
) => {
  return new Promise<ICommonFunReturn & { neid?: number }>(
    // eslint-disable-next-line max-statements
    async function (resolve) {
      const initRes = await initUploadChunk(oldFileId, controller);
      if (initRes.code === SUCCESS_CON) {
        // 复用一下上传列表的上传函数
        const uploadItem: IUploadItem = {
          fileInfo: curFile,
          key: initRes.key,
          uploadId: initRes.uploadId,
          status: RUNNING_CON,
          returnValues: [],
        };
        const resP = await uploadFile(
          uploadItem,
          getUploadProcessFun,
          controller
        );
        if (resP.code === SUCCESS_CON) {
          const compRes = await completeUploadChunk(
            uploadItem,
            netPathNeid,
            oldFileId,
            resP.resList.map((item) => ({
              ...item.t,
            }))
          );
          if (compRes.code === SUCCESS_CON) {
            resolve({ code: SUCCESS_CON, neid: compRes.neid });
          } else {
            resolve({
              code: FAILED_CON,
              errMsg: compRes.errMsg,
            });
          }
        } else if (resP.code === CANCELED_CON) {
          resolve({
            code: CANCELED_CON,
            errMsg: '取消上传！',
          });
        } else {
          // task中一个文件上传失败
          resolve({
            code: FAILED_CON,
            errMsg: resP.errMsg,
          });
        }
      } else if (initRes.code === CANCELED_CON) {
        resolve({
          code: CANCELED_CON,
          errMsg: 'initUploadChunk取消！',
        });
      } else {
        // task中一个文件上传失败
        resolve({
          code: FAILED_CON,
          errMsg: initRes.errMsg,
        });
      }
    }
  );
};
