/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
import { CANCELED_CON, FAILED_CON, SUCCESS_CON } from '@common/constant';
import { COVER_CON } from '@htElectronSDK/constant/ipc';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { IUploadItem, UPLOAD_PIECE_SIZE } from '@lightenModule/uploadManager';
import {
  LINKFLOW_ON_UPLOAD_CALLBACK,
  LINKFLOW_UPLOAD_AT_ONCE,
} from '@lightenSDK/constant/upload';
import { TUploadProcessFun } from '@utils/fileUtils/uploadFile/uploadFile';
import { getFileOrDirInfo } from '@utils/folderUtils';
import { uploadBigFile } from './uploadBigFile';
import { uploadSmallFile } from './uploadSmallFile';

/**
 * 独立的上传文件接口（不支持文件夹）
 * 上传过程不会显示在传输列表里，也不会受上传任务数限制，即调即上传。
 *
 * @param filePath ：要上传的本地文件路径
 * @param serverPath ：服务端路径
 * @param serverPathType ：服务端路径类型，
 * @param callback ：过程回调
 */

const getUploadProcessFun = (callback: {
  onProgress: (percent: number) => void;
  onSuccess: (neid: number) => void;
  onFail: (errMsg: string) => void;
}) => {
  const uploadProcessFun: TUploadProcessFun<IUploadItem> = (
    curUploadItem,
    chunkIndex
  ) => {
    callback.onProgress(
      Math.min(
        ((chunkIndex + 1) * UPLOAD_PIECE_SIZE) /
          curUploadItem.file.fileInfo.size,
        1
      )
    );
  };
  return uploadProcessFun;
};

export const uploadFileAtOnce = async (
  msg: any,
  callback: {
    onProgress: (percent: number) => void;
    onSuccess: (neid: number) => void;
    onFail: (errMsg: string) => void;
  }
) => {
  const { filePath, serverPath, controller, fileId, oldProjectCatalogId } = msg;
  if (!filePath || !serverPath) {
    callback.onFail(`mainIPC_UPLOAD_FILE 传参错误！${filePath}- ${serverPath}`);
    return;
  }
  // 获取文件信息
  const aRes = await getFileOrDirInfo(filePath);
  if (aRes.code !== SUCCESS_CON) {
    callback.onFail(aRes.errorMsg);
    return;
  }

  if (aRes.file.isDirectory) {
    callback.onFail('该接口不支持文件夹上传！');
  } else if (aRes.file.size < UPLOAD_PIECE_SIZE) {
    // 小文件直接上传
    const resP = await uploadSmallFile(
      filePath,
      aRes.file.name,
      oldProjectCatalogId || serverPath,
      fileId,
      controller
    );
    if (resP.code === SUCCESS_CON) {
      // 文件上传成功
      callback.onSuccess(resP.neid);
      return SUCCESS_CON;
    } else if (resP.code === CANCELED_CON) {
      // 文件上传取消
      return CANCELED_CON;
    } else {
      // 文件上传失败
      callback.onFail(resP.errMsg);
      return resP.code;
    }
  } else {
    // 大文件上传
    const uploadBigFileRes = await uploadBigFile(
      aRes.file,
      oldProjectCatalogId || serverPath,
      getUploadProcessFun(callback),
      fileId,
      controller
    );
    if (uploadBigFileRes.code === SUCCESS_CON) {
      // 文件上传成功
      callback.onSuccess(uploadBigFileRes.neid);
      return SUCCESS_CON;
    } else if (uploadBigFileRes.code === CANCELED_CON) {
      // 文件上传取消
      return CANCELED_CON;
    } else {
      // 文件上传失败
      callback.onFail(uploadBigFileRes.errMsg);
      return uploadBigFileRes.code;
    }
  }
};

export const initUploadFileAtOnce = () => {
  // 上传文件（独立接口）
  mainIPC.addListener(
    LINKFLOW_UPLOAD_AT_ONCE,
    (msg: any) => {
      const { filePath, serverPath } = msg;
      uploadFileAtOnce(msg, {
        onFail(errMsg) {
          mainIPC.send({
            id: LINKFLOW_ON_UPLOAD_CALLBACK,
            params: {
              filePath,
              serverPath,
              errMsg,
            },
          });
        },
        onProgress(percent) {
          mainIPC.send({
            id: LINKFLOW_ON_UPLOAD_CALLBACK,
            params: {
              filePath,
              serverPath,
              percent,
            },
          });
        },
        onSuccess(neid) {
          mainIPC.send({
            id: LINKFLOW_ON_UPLOAD_CALLBACK,
            params: {
              filePath,
              serverPath,
              neid,
            },
          });
        },
      });
    },
    COVER_CON
  );
};
