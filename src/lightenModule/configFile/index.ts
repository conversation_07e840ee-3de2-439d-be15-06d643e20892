import { ensureDir } from '@utils/folderUtils';
import { app } from 'electron';
import path from 'path';

/**
 * 获取{userData/appName}文件夹路径
 */
export const getAppLocalPath = () => {
  return path.join(path.dirname(app.getPath('userData')), app.getName());
};

/**
 * 初始化{userData/appName}文件夹下的logs和config文件夹，日志和db模块用到
 */
export const initConfigPath = () => {
  ensureDir(path.join(getAppLocalPath(), 'logs'));
  ensureDir(path.join(getAppLocalPath(), 'config/upload'));
  ensureDir(path.join(getAppLocalPath(), 'config/download'));
};
