/**
 * 使用will-download，直接下载文件，这种方式无法分片，无法加鉴权，需要使用一次性token
 */
import { COVER_CON } from '@htElectronSDK/constant/ipc';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { addNetworkErrListener } from '@htElectronSDK/main/network';
import {
  LINKFLOW_DOWNLOAD_BY_URL_PROCESS,
  LINKFLOW_DOWNLOAD_FILE_BY_URL,
} from '@lightenSDK/constant/download';
import { app, BrowserWindow, dialog } from 'electron';
import * as path from 'path';

/**
 * 通过url下载的返回值，标识完成、失败、下载中等状态
 */
interface IDownloadByUrlDataType {
  state: 'paused' | 'progressing' | 'cancelled' | 'completed' | 'failed'; // 下载中断，下载暂停，下载中，取消下载，下载成功，下载失败
  savePath: string;
  fileName: string;
  received?: number;
  total?: number;
  downloadURL: string;
  errInfo?: string;
}

/**
 * 注册downloadByUrl的功能
 * @param mainWindow 渲染进程句柄
 */
export const listenDownloadByURL = () => {
  const renderWindow = BrowserWindow.fromWebContents(
    mainIPC.getMainWindowWebContents()
  );
  // 缓存下载路径，默认是系统下载文件夹，一个应用只启动一次
  let currentPath = '';
  mainIPC.addListener(
    LINKFLOW_DOWNLOAD_FILE_BY_URL,
    (msg) => {
      const { fileName, downloadPath } = msg;
      const dPath = app.getPath('downloads');
      const filePath = path.join(`${dPath}`, fileName);
      dialog
        ?.showSaveDialog(renderWindow, { defaultPath: filePath })
        .then(({ filePath: vSavePath, canceled }) => {
          if (canceled) {
            return;
          }
          // fix: 默认系统隐藏文件后缀，重命名会导致后缀丢失
          // fix: 手动补齐后缀，不考虑用户期望修改后缀的场景
          let savePath = vSavePath?.trim();
          if (savePath && fileName) {
            const lastIndex = fileName.lastIndexOf('.');
            if (lastIndex !== -1) {
              const subfix = fileName.substring(lastIndex);
              if (subfix && !savePath.endsWith(subfix)) {
                savePath += subfix;
              }
            }
            currentPath = savePath;
            renderWindow.webContents.downloadURL(downloadPath);
            // 捕获401或者超时等网络错误异常
            addNetworkErrListener(
              downloadPath,
              (error: Electron.OnResponseStartedListenerDetails) => {
                const data: IDownloadByUrlDataType = {
                  savePath: vSavePath,
                  fileName,
                  downloadURL: filePath,
                  state: 'failed',
                  errInfo: `网络错误，stateCode: ${error?.statusCode}`,
                };
                mainIPC.send({
                  id: LINKFLOW_DOWNLOAD_BY_URL_PROCESS,
                  params: data,
                });
              }
            );
          }
        });
    },
    COVER_CON
  );

  // 监听下载进度
  renderWindow.webContents.session.on('will-download', (_event, item) => {
    item.setSavePath(currentPath);
    // 下载时获取下载进度
    handleDownloadUpdate(item);

    // 下载完成监听
    handleDownloadDone(item);
  });

  const handleDownloadUpdate = (item: Electron.DownloadItem) => {
    item.on('updated', (__event, state) => {
      const data: IDownloadByUrlDataType = {
        savePath: item.getSavePath(),
        fileName: item.getFilename(),
        received: item.getReceivedBytes(),
        total: item.getTotalBytes(),
        downloadURL: item.getURL(),
        state: state === 'interrupted' ? 'failed' : state,
      };
      // 如果进程已经被销毁，则无需处理
      if (!renderWindow || renderWindow.isDestroyed()) {
        return;
      }
      if (state === 'interrupted') {
        // 网络错误由addNetworkErrListener处理
        if (!item.getURL) {
          data.errInfo = '下载地址错误，请检查！';
          mainIPC.send({ id: LINKFLOW_DOWNLOAD_BY_URL_PROCESS, params: data });
        }
        return;
      }
      if (item.isPaused() && state === 'progressing') {
        data.state = 'paused';
      }
      mainIPC.send({ id: LINKFLOW_DOWNLOAD_BY_URL_PROCESS, params: data });
    });
  };

  const handleDownloadDone = (item: Electron.DownloadItem) => {
    item.once('done', (__event, state) => {
      // 如果进程已经被销毁，则无需处理
      if (!renderWindow || renderWindow.isDestroyed()) {
        return;
      }
      const data: IDownloadByUrlDataType = {
        savePath: item.getSavePath(),
        fileName: item.getFilename(),
        received: item.getReceivedBytes(),
        total: item.getTotalBytes(),
        downloadURL: item.getURL(),
        state: state === 'interrupted' ? 'failed' : state,
      };
      if (state === 'interrupted') {
        // 网络错误由addNetworkErrListener处理
        if (!item.getURL()) {
          data.errInfo = '下载地址错误，请检查！';
          mainIPC.send({ id: LINKFLOW_DOWNLOAD_BY_URL_PROCESS, params: data });
        } else {
          data.errInfo = `下载异常，下载地址为：${item.getURL()}`;
          mainIPC.send({ id: LINKFLOW_DOWNLOAD_BY_URL_PROCESS, params: data });
        }
        return;
      }
      mainIPC.send({ id: LINKFLOW_DOWNLOAD_BY_URL_PROCESS, params: data });
    });
  };
};
