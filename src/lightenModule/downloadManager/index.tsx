import { PAUSED_CON } from '@common/constant';
import {
  getUserDownloadListStore,
  IUserDownloadStore,
} from '@globalState/localStore/downloadListStore';
import { COVER_CON } from '@htElectronSDK/constant/ipc';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { CTaskManager } from '@lightenModule/taskManager';
import {
  LINKFLOW_DOWNLOAD_ADD,
  LINKFLOW_DOWNLOAD_AT_ONCE,
  LINKFLOW_DOWNLOAD_AT_ONCE_CANCEL,
  LINKFLOW_DOWNLOAD_PAUSE,
  LINKFLOW_DOWNLOAD_REMOVE,
  LINKFLOW_DOWNLOAD_RESTART,
  LINKFLOW_DOWNLOAD_START,
  LINKFLOW_SHOW_DOWNLOAD_SAVE_PATH_DIALOG,
} from '@lightenSDK/constant/download';
import { LINKFLOW_TRANSFER_LIST_INFO } from '@lightenSDK/constant/global';
import { IFileDownloadModel } from '@utils/lightenApi/download';
import { BrowserWindow } from 'electron';
import { LowSync } from 'lowdb/lib';
import { runDownloadTask } from './runDownloadTask';
import { cancelDownloadAtOnce } from './utils/downloadFileAtOnce';
import {
  addDownload,
  reStartDownload,
  showSavePathDialog,
  singleDownloadFile,
  startDownload,
} from './utils/downloadMsgHandler';
import { TAddDownloadMsg } from './utils/downloadMsgHandler/addDownload';
import { downloadTaskChangCallBack } from './utils/downloadMsgHandler/downloadTaskChangCallBack';
import { TReStartDownloadMsg } from './utils/downloadMsgHandler/reStartDownload';
import { TDownloadFileMsg } from './utils/downloadMsgHandler/singleDownloadFile';
import { TStartDownloadMsg } from './utils/downloadMsgHandler/startDownload';
import { getDownloadInfo } from './utils/getDownloadInfo';

export interface IDownloadItem {
  file: IFileDownloadModel; // 下载项文件
  downloadSize: number; // 已下载字节数，方便下次继续下载时知道从哪个地方开始请求
  savePath: string; // 本地保存路径
  status: TTaskStatus;
}

export interface IDownloadTask {
  key: string;
  file: IFileDownloadModel; // 云盘上下载目标文件（夹）
  list: IDownloadItem[]; // 下载目标文件需要的下载项
  totalSize: number; // 任务总大小（字节数）
  savePath: string; // 本地保存路径
  startTime: string; // 创建下载任务时间
  completedSize?: number; // 已完成的下载的数据大小
  rate?: number; // 当前下载速率，单位:字节/s
  errMsg?: string; // yan
  status: TTaskStatus; // 任务状态
  completeTime?: string; // 完成时间
}

const DOWNLOAD_CONCURRENT_TASK_NUM = 5;
export const DOWNLOAD_PIECE_SIZE = 300 * 1024 * 1024;

const downloadAbortControllers = new Map<string, AbortController>();
let gSavePath: string; // 下载保存路径

const taskEqualFun = (vTask1: IDownloadTask, vTask2: IDownloadTask) => {
  if (vTask1?.key && vTask1?.startTime && vTask2?.key && vTask2?.startTime) {
    return vTask1.key === vTask2.key && vTask1.startTime === vTask2.startTime;
  }
  return false;
};

const initDownloadTaskManager = (
  downloadStore: LowSync<IUserDownloadStore>
) => {
  // 将正在进行和等待都转为暂停
  downloadStore.read();
  downloadStore.data.processingList?.forEach((item) => {
    downloadStore.data.pauseList?.push({
      ...item,
      status: PAUSED_CON,
    });
  });
  downloadStore.data.waitingList?.forEach((item) => {
    downloadStore.data.pauseList?.push({
      ...item,
      status: PAUSED_CON,
    });
  });
  downloadStore.data.processingList = [];
  downloadStore.data.waitingList = [];
  downloadStore.write();
  const initPauseList = new Map<string, IDownloadTask>();
  downloadStore.data.pauseList?.forEach((item: IDownloadTask) => {
    initPauseList.set(item.key, item);
  });

  mainIPC.send({
    id: LINKFLOW_TRANSFER_LIST_INFO,
    params: getDownloadInfo(),
  });

  // 初始化任务管理器
  return new CTaskManager<string, IDownloadTask>(
    DOWNLOAD_CONCURRENT_TASK_NUM,
    runDownloadTask,
    initPauseList,
    downloadStore.data.completeList,
    downloadAbortControllers,
    downloadTaskChangCallBack,
    taskEqualFun
  );
};

export const initLightenDownloader = () => {
  const renderWindow = BrowserWindow.fromWebContents(
    mainIPC.getMainWindowWebContents()
  );

  const downloadStore = getUserDownloadListStore();
  const downloadManager = initDownloadTaskManager(downloadStore);

  // 下载路径选择消息监听
  mainIPC.addListener(
    LINKFLOW_SHOW_DOWNLOAD_SAVE_PATH_DIALOG,
    () => {
      showSavePathDialog(renderWindow, (savePath: string) => {
        gSavePath = savePath;
      });
    },
    COVER_CON
  );

  // 开始下载消息监听
  mainIPC.addListener(
    LINKFLOW_DOWNLOAD_START,
    (msg: TStartDownloadMsg) => {
      startDownload(msg, gSavePath);
    },
    COVER_CON
  );

  // 重新下载消息监听
  mainIPC.addListener(
    LINKFLOW_DOWNLOAD_RESTART,
    (msg: TReStartDownloadMsg) => {
      reStartDownload(msg, downloadManager, downloadStore);
    },
    COVER_CON
  );

  // 新增下载消息监听
  mainIPC.addListener(
    LINKFLOW_DOWNLOAD_ADD,
    async (msg: TAddDownloadMsg) => {
      addDownload(msg, downloadManager);
    },
    COVER_CON
  );

  // 暂停下载消息监听
  mainIPC.addListener(
    LINKFLOW_DOWNLOAD_PAUSE,
    (msg) => {
      const { keys } = msg;
      for (const curKey of keys) {
        downloadAbortControllers.get(curKey)?.abort();
        downloadAbortControllers.delete(curKey);
        downloadManager.addWaitingTaskToPauseTasks(curKey);
      }
    },
    COVER_CON
  );

  // 移除下载消息监听
  mainIPC.addListener(
    LINKFLOW_DOWNLOAD_REMOVE,
    (msg: any) => {
      const { deleteInfos } = msg;
      downloadStore.read();
      for (const cDelInfo of deleteInfos) {
        const { key, startTime } = cDelInfo;
        // 失败重传
        if (startTime) {
          const findTask =
            downloadStore.data.completeList?.find(
              (item) => item.key === key && item.startTime
            ) || undefined;
          downloadManager.removeFromCompleteTasks(findTask);
        } else {
          downloadAbortControllers.get(key)?.abort();
          downloadAbortControllers.delete(key);
          downloadManager.removeFromTasks(key);
        }
      }
    },
    COVER_CON
  );

  // 独立下载文件接口
  mainIPC.addListener(
    LINKFLOW_DOWNLOAD_AT_ONCE,
    (msg: TDownloadFileMsg) => {
      singleDownloadFile(msg);
    },
    COVER_CON
  );
  mainIPC.addListener(
    LINKFLOW_DOWNLOAD_AT_ONCE_CANCEL,
    (msg) => {
      const { key } = msg;
      cancelDownloadAtOnce(key);
    },
    COVER_CON
  );
};

export const pauseAllDownload = () => {
  for (const controller of downloadAbortControllers.values()) {
    controller.abort();
  }
  downloadAbortControllers.clear();
};
