import {
  IDownloadProcessResp,
  TDownloadChunkDIYFun,
} from '@utils/fileUtils/downloadFile/downloadChunk';
import Request from '@utils/request';

import { FAILED_CON, SUCCESS_CON } from '@common/constant';
import {
  downloadByMultipart,
  IDownloadInfo,
} from '@utils/fileUtils/downloadFile/downloadFile';
import { getFileOrDirInfo, isFileExist } from '@utils/folderUtils';
import { getErrorMsg } from '@utils/stringUtils';
import { AxiosRequestConfig } from 'axios';
import { IFileDownloadModel } from '@utils/lightenApi/download';
import { DOWNLOAD_PIECE_SIZE, IDownloadItem } from '..';

export const downloadChunkFun: TDownloadChunkDIYFun = (params: any) => {
  return new Promise(async (resolve) => {
    const streamRe = await Request(params);
    if (streamRe.code === SUCCESS_CON) {
      resolve({
        status: SUCCESS_CON,
        // data是fs.ReadStream，不符合常规定义
        data: streamRe.data as any, // yan
      });
    } else {
      resolve({
        status: streamRe.code,
        errorMsg: streamRe.errMsg,
      });
    }
  });
};

export const getDownloadParams =
  (controller: AbortController) =>
  async (
    downloadInfo: IDownloadInfo<IFileDownloadModel>,
    start: number,
    end: number
  ): Promise<AxiosRequestConfig> => {
    const { file } = downloadInfo;
    const params = {
      start,
      end,
      fileId: file.neid,
      versionId: file.rev,
    };

    return {
      url: '/pcfile/transfer/downloadByRangeS3',
      responseType: 'stream',
      params,
      method: 'GET',
      signal: controller ? controller.signal : undefined,
    };
  };

export const downloadFile = (
  downloadItem: IDownloadItem,
  curController: AbortController,
  getDownloadProcessFun?: (size: number) => void
): Promise<IDownloadProcessResp> => {
  return new Promise(async (resolve) => {
    try {
      const { file, savePath } = downloadItem;
      // 判断文件是否存在
      if (isFileExist(savePath) === SUCCESS_CON) {
        const currDownFileStat = await getFileOrDirInfo(savePath);
        // 更新已下载尺寸
        if (currDownFileStat.code === SUCCESS_CON) {
          if (currDownFileStat.file.size !== downloadItem.downloadSize) {
            downloadItem.downloadSize = currDownFileStat.file.size;
          }
        } else {
          resolve({
            code: FAILED_CON,
            message: currDownFileStat.errorMsg,
          });
          return;
        }
      }

      const downloadInfo: IDownloadInfo<IFileDownloadModel> = {
        downloadFilePath: savePath,
        start: downloadItem.downloadSize,
        DOWNLOAD_PIECE_SIZE,
        fileSize: file.realSize,
        file: downloadItem.file,
      };

      const res = await downloadByMultipart<IFileDownloadModel>(
        downloadChunkFun,
        downloadInfo,
        getDownloadParams(curController),
        getDownloadProcessFun
      );
      resolve(res);
    } catch (error) {
      resolve({
        code: FAILED_CON,
        message: getErrorMsg(error),
      });
    }
  });
};
