import path from 'path';
import { FAILED_CON, SUCCESS_CON, WAITING_CON } from '@common/constant';
import { getFilePathWithoutRepeat } from '@utils/folderUtils';
import {
  getFileModel,
  getFilesFromNeidCancelable,
  TFileNeid,
} from '@utils/lightenApi/download';
import { getErrorMsg } from '@utils/stringUtils';
import moment from 'moment';
import { IDownloadItem, IDownloadTask } from '..';

// 生成task
export const generateTask = async (
  neid: TFileNeid,
  savePath: string,
  version: string
): Promise<ICommonFunReturn & { task?: IDownloadTask }> => {
  try {
    // 获取文件信息
    const modelRep = await getFileModel(neid);
    if (modelRep.code === SUCCESS_CON) {
      let realSavePath = savePath;
      const list: IDownloadItem[] = [];
      const curFileModel = modelRep.fileModel;
      if (curFileModel.dir) {
        // 如果下载的目标文件夹下存在同名文件夹，则重命名文件夹
        // 但是download文件夹中已经存在aaa,就将下载路径aaa重命名为aaa(1)
        const listInfoRep = await getFilesFromNeidCancelable({
          pathType: curFileModel.pathType,
          neid: curFileModel.neid,
        });
        if (listInfoRep.code === SUCCESS_CON) {
          realSavePath = getFilePathWithoutRepeat(savePath, curFileModel.name);
          const listInfo = listInfoRep.fileModels;
          listInfo.forEach((item) => {
            list.push({
              file: item,
              downloadSize: 0,
              savePath: path.resolve(
                realSavePath + item.path.substring(curFileModel.path.length)
              ),
              status: WAITING_CON,
            });
          });
        } else {
          return listInfoRep;
        }
      } else {
        // 当前下载任务为文件
        list.push({
          file: curFileModel,
          downloadSize: 0,
          // 如果下载的目标文件夹下存在同名文件，则重命名
          savePath: getFilePathWithoutRepeat(
            savePath,
            curFileModel.name,
            curFileModel.ext
          ),
          status: WAITING_CON,
        });
      }
      if (version) {
        curFileModel.rev = version;
      }
      const task: IDownloadTask = {
        file: curFileModel,
        list,
        totalSize: list.reduce((pre, cur) => {
          return pre + cur.file.realSize;
        }, 0),
        key: `${savePath}-${neid}`,
        savePath,
        startTime: moment().format('YYYY_MM_DD_hh_mm_ss'),
        status: WAITING_CON,
      };

      return { code: SUCCESS_CON, task };
    } else {
      return modelRep;
    }
  } catch (error) {
    return { code: FAILED_CON, errMsg: getErrorMsg(error) };
  }
};
