import { SUCCESS_CON } from '@common/constant';
import {
  downloadByMultipart,
  IDownloadInfo,
} from '@utils/fileUtils/downloadFile/downloadFile';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { LINKFLOW_ON_SHOW_MODAL } from '@lightenSDK/constant/global';
import { getErrorMsg } from '@utils/stringUtils';
import LOG from '@htElectronSDK/main/log';
import { getFilePathWithoutRepeat } from '@utils/folderUtils';
import { getFileModel, IFileDownloadModel } from '@utils/lightenApi/download';
import { DOWNLOAD_PIECE_SIZE } from '..';
import { downloadChunkFun, getDownloadParams } from './downloadFile';

const downloadAbortControllers = new Map<string, AbortController>();

/**
 * 独立下载文件（不能下载文件夹！）,下载信息不会显示在传输列表
 * @param neid：文件的neid
 * @param savePath ：本地保存路径
 * @param callback ：下载过程回调
 * @returns
 */
// eslint-disable-next-line max-statements
export const downloadFileAtOnce = async (
  neid: number,
  savePath: string,
  startTime: string,
  callback: {
    onProgress: (percent: number) => void;
    onSuccess: (filePath: string) => void;
    onFail: (errMsg: string) => void;
  }
): Promise<void> => {
  try {
    const fileRep = await getFileModel(neid);
    if (fileRep.code !== SUCCESS_CON) {
      callback.onFail(fileRep.errMsg);
      LOG.error('downloadFileAtOnce error', fileRep.errMsg);
      mainIPC.send({
        id: LINKFLOW_ON_SHOW_MODAL,
        params: {
          type: 'error',
          title: '下载报错',
          content: fileRep.errMsg,
          okText: '确定',
        },
      });
      return;
    }
    const file = fileRep.fileModel;
    if (file.dir) {
      callback.onFail('该接口不支持下载文件夹! ');
      mainIPC.send({
        id: LINKFLOW_ON_SHOW_MODAL,
        params: {
          type: 'error',
          title: '下载报错',
          content: '该接口不支持下载文件夹!',
          okText: '确定',
        },
      });
      return;
    }
    const saveFilePath = getFilePathWithoutRepeat(
      savePath,
      file.name,
      file.ext
    );

    const abCont = new AbortController();
    downloadAbortControllers.set(`${neid}-${savePath}-${startTime}`, abCont);

    const downloadInfo: IDownloadInfo<IFileDownloadModel> = {
      downloadFilePath: saveFilePath,
      start: 0,
      DOWNLOAD_PIECE_SIZE,
      fileSize: file.realSize,
      file,
    };

    const downloadFileRes = await downloadByMultipart<IFileDownloadModel>(
      downloadChunkFun,
      downloadInfo,
      getDownloadParams(abCont)
    );

    downloadAbortControllers.delete(`${neid}-${saveFilePath}`);

    if (downloadFileRes.code === SUCCESS_CON) {
      callback.onSuccess(saveFilePath);
    } else {
      callback.onFail(downloadFileRes.message);
      LOG.error('downloadFileAtOnce error', downloadFileRes.message);
      mainIPC.send({
        id: LINKFLOW_ON_SHOW_MODAL,
        params: {
          type: 'error',
          title: '下载报错',
          content: downloadFileRes.message,
          okText: '确定',
        },
      });
    }
  } catch (e) {
    callback.onFail(JSON.stringify(e));
    LOG.error('downloadFileAtOnce error', getErrorMsg(e));
    mainIPC.send({
      id: LINKFLOW_ON_SHOW_MODAL,
      params: {
        type: 'error',
        title: '下载报错',
        content: getErrorMsg(e),
        okText: '确定',
      },
    });
  }
};

export const cancelDownloadAtOnce = (key) => {
  downloadAbortControllers.get(key)?.abort();
};
