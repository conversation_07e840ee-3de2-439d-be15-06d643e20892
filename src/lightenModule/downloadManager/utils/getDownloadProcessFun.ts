import { getUserDownloadListStore } from '@globalState/localStore/downloadListStore';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { LINKFLOW_TRANSFER_LIST_INFO } from '@lightenSDK/constant/global';
import {
  IGetChildFilesFromDirRes,
  getAllFilesFromDir,
} from '@utils/folderUtils';
import { SUCCESS_CON } from '@common/constant';
import LOG from '@htElectronSDK/main/log';
import moment from 'moment';
import { IDownloadTask } from '..';
import { getDownloadInfo } from './getDownloadInfo';

/**
 * 当前task下本地文件的大小，每次启动timer的时候计算一下，或者初始值，然后每次运行timer时或者最新的进行进度替换
 * @param vTask
 * @returns 当前task下本地文件的大小
 */
const getFilesFromTask = async (vTask: IDownloadTask) => {
  const curFilesPromises = [];
  vTask.list.forEach((item) => {
    curFilesPromises.push(getAllFilesFromDir(item.savePath));
  });
  const fileInfos = await Promise.all<IGetChildFilesFromDirRes[]>(
    curFilesPromises
  );

  const curFiles = [];
  fileInfos.forEach((item) => {
    if (item.code === SUCCESS_CON) {
      curFiles.push(...item.fileList);
    } else {
      LOG.error('downloadProcessFun', item.errorMsg);
    }
  });
  const curSize =
    curFiles?.reduce((preData, curData) => {
      return preData + curData.size;
    }, 0) || 0;
  return curSize;
};

export const getDownloadProcessFun = async (vTask: IDownloadTask) => {
  let tStartTime = moment();

  let preSize = await getFilesFromTask(vTask);

  const downloadProcessFun = async () => {
    const curSize = await getFilesFromTask(vTask);

    const downloadStore = getUserDownloadListStore();
    downloadStore.read();
    const timeGapSecs = moment().diff(tStartTime, 'milliseconds');
    tStartTime = moment();
    vTask.completedSize = curSize;
    // 更新下载项的进度
    vTask.rate = (curSize - preSize) / timeGapSecs;
    preSize = curSize;
    const fTask = downloadStore.data.processingList?.find(
      (item) => item.key === vTask.key
    );
    if (fTask) {
      fTask.completedSize = vTask.completedSize;
      fTask.rate = vTask.rate;
    }
    downloadStore.write();
    mainIPC.send({
      id: LINKFLOW_TRANSFER_LIST_INFO,
      params: getDownloadInfo(),
    });
  };

  return downloadProcessFun;
};
