import { getUserDownloadListStore } from '@globalState/localStore/downloadListStore';

export const getDownloadInfo = () => {
  const downloadStore = getUserDownloadListStore();
  downloadStore.read();
  return {
    downloadList: {
      processingList: downloadStore.data.processingList,
      waitingList: downloadStore.data.waitingList,
      pauseList: downloadStore.data.pauseList,
      completeList: downloadStore.data.completeList,
    },
  };
};
