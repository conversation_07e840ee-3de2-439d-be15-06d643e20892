import { SUCCESS_CON } from '@common/constant';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { IDownloadTask } from '@lightenModule/downloadManager';
import { CTaskManager } from '@lightenModule/taskManager';
import { LINKFLOW_MESSAGE_INFO } from '@lightenSDK/constant/global';
import { TFileNeid } from '@utils/lightenApi/download';
import { generateTask } from '../downloadTask';

export type TAddDownloadMsg = {
  neids: TFileNeid[];
  savePath: string;
  version: string;
};

// 新增下载消息处理
const addDownload = async (
  msg: TAddDownloadMsg,
  taskManager: CTaskManager<string, IDownloadTask>
) => {
  const { neids, savePath, version } = msg;
  if (!neids || !savePath || !(neids instanceof Array)) {
    return;
  }

  for (const neid of neids) {
    // 这里加await是为了保证先添加的就一定先开始下载，按顺序出现在下载视图里。
    // （可能会导致的问题是假设批量添加多个任务里有个层级特别多的文件夹，会导致后面的几个延迟出现在下载视图）
    const { code, task, errMsg } = await generateTask(neid, savePath, version);
    if (code !== SUCCESS_CON) {
      mainIPC.send({
        id: LINKFLOW_MESSAGE_INFO,
        params: {
          text: errMsg,
        },
      });
      return;
    }
    taskManager.addTaskToRun(task.key, task);
  }
  // 让前端显示message
  mainIPC.send({
    id: LINKFLOW_MESSAGE_INFO,
    params: {
      text: '文件已添加至传输列表',
    },
  });
};
export default addDownload;
