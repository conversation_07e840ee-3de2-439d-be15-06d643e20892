import { IUserDownloadStore } from '@globalState/localStore/downloadListStore';
import { IDownloadTask } from '@lightenModule/downloadManager';
import { CTaskManager } from '@lightenModule/taskManager';
import { LowSync } from 'lowdb/lib';

export type TReStartDownloadMsg = {
  tasks: IDownloadTask[];
};

// 重新下载消息处理
const reStartDownload = async (
  msg,
  taskManager: CTaskManager<string, IDownloadTask>,
  downloadStore: LowSync<IUserDownloadStore>
) => {
  // 两种情况，1.暂停重传，失败重传，失败的情况，key不能作为唯一值，所以要加上startTime
  downloadStore.read();
  const { reStartInfos } = msg;
  for (const cResInfo of reStartInfos) {
    const { key, startTime } = cResInfo;
    // 失败重传
    if (startTime) {
      const findTask =
        downloadStore.data.completeList?.find(
          (item) => item.key === key && item.startTime === startTime
        ) || undefined;
      taskManager.removeFromCompleteTasks(findTask);
      taskManager.addTaskToRun(key, findTask);
    } else {
      taskManager.addTaskToRun(key);
    }
  }
};

export default reStartDownload;
