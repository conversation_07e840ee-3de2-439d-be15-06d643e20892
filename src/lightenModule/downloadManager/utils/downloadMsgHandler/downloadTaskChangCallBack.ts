import {
  ADD_CON,
  COMPLETE_CON,
  PAUSED_CON,
  RUNNING_CON,
  WAITING_CON,
} from '@common/constant';
import { LowSync } from 'lowdb/lib';
import {
  IUserDownloadStore,
  getUserDownloadListStore,
} from '@globalState/localStore/downloadListStore';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { IDownloadTask } from '@lightenModule/downloadManager';
import { TTaskChangeCallBackFun } from '@lightenModule/taskManager';
import { LINKFLOW_TRANSFER_LIST_INFO } from '@lightenSDK/constant/global';
import { getDownloadInfo } from '../getDownloadInfo';

export const downloadTaskChangCallBack: TTaskChangeCallBackFun<
  IDownloadTask
> = (changeTaskInfos) => {
  const downloadStore = getUserDownloadListStore();
  downloadStore.read();
  for (const changeTaskInfo of changeTaskInfos) {
    updateBatchStore(downloadStore, changeTaskInfo);
  }
  downloadStore.write();
  mainIPC.send({
    id: LINKFLOW_TRANSFER_LIST_INFO,
    params: getDownloadInfo(),
  });
};

const updateBatchStore = (
  downloadStore: LowSync<IUserDownloadStore>,
  changeTaskInfo: {
    changeType: TCOMPLETE_FLAG | TPAUSED_FLAG | TRUNNING_FLAG | TWAITING_FLAG;
    operationType: TADD_FLAG | TDELETE_FLAG;
    Task: IDownloadTask;
  }
) => {
  switch (changeTaskInfo.changeType) {
    case RUNNING_CON:
      if (changeTaskInfo.operationType === ADD_CON) {
        downloadStore.data.processingList?.push(changeTaskInfo.Task);
      } else {
        downloadStore.data.processingList =
          downloadStore.data.processingList?.filter(
            (item) => item.key !== changeTaskInfo.Task.key
          );
      }
      break;
    case WAITING_CON:
      if (changeTaskInfo.operationType === ADD_CON) {
        downloadStore.data.waitingList?.push(changeTaskInfo.Task);
      } else {
        downloadStore.data.waitingList = downloadStore.data.waitingList?.filter(
          (item) => item.key !== changeTaskInfo.Task.key
        );
      }
      break;
    case PAUSED_CON:
      if (changeTaskInfo.operationType === ADD_CON) {
        downloadStore.data.pauseList?.push(changeTaskInfo.Task);
      } else {
        downloadStore.data.pauseList = downloadStore.data.pauseList?.filter(
          (item) => item.key !== changeTaskInfo.Task.key
        );
      }
      break;
    case COMPLETE_CON:
      if (changeTaskInfo.operationType === ADD_CON) {
        downloadStore.data.completeList?.push(changeTaskInfo.Task);
      } else {
        downloadStore.data.completeList =
          downloadStore.data.completeList?.filter(
            (item) =>
              item.key !== changeTaskInfo.Task.key ||
              item.startTime !== changeTaskInfo.Task.startTime
          );
      }
      break;
    default:
      break;
  }
};
