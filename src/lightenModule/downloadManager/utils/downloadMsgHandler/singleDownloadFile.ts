import { mainIPC } from '@htElectronSDK/main/ipc';
import LOG from '@htElectronSDK/main/log';
import { LINKFLOW_ON_DOWNLOAD_CALLBACK } from '@lightenSDK/constant/download';
import { downloadFileAtOnce } from '../downloadFileAtOnce';

export type TDownloadFileMsg = {
  neid: number;
  savePath: string;
  startTime: string;
};

// 单独下载文件
const singleDownloadFile = (msg: TDownloadFileMsg) => {
  const { neid, savePath, startTime } = msg;
  if (!neid || !savePath || !startTime) {
    LOG.error('singleDownloadFile参数错误', neid, savePath, startTime);
    return;
  }
  downloadFileAtOnce(neid, savePath, startTime, {
    onFail(errMsg) {
      mainIPC.send({
        id: LINKFLOW_ON_DOWNLOAD_CALLBACK,
        params: {
          neid,
          errMsg,
        },
      });
    },
    onProgress(percent) {
      mainIPC.send({
        id: LINKFLOW_ON_DOWNLOAD_CALLBACK,
        params: {
          neid,
          percent,
        },
      });
    },
    onSuccess(filePath) {
      mainIPC.send({
        id: LINKFLOW_ON_DOWNLOAD_CALLBACK,
        params: {
          neid,
          filePath,
        },
      });
    },
  });
};

export default singleDownloadFile;
