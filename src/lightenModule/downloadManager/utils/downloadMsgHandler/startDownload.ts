import { mainIPC } from '@htElectronSDK/main/ipc';
import { LINKFLOW_ON_SHOW_DOWNLOAD_MODAL } from '@lightenSDK/constant/download';
import { TFileNeid } from '@utils/lightenApi/download';

export type TStartDownloadMsg = { neids: TFileNeid[]; version: string };

// 开始下载消息处理
const startDownload = async (msg: TStartDownloadMsg, savePath: string) => {
  const { neids, version } = msg;
  if (!(neids && neids instanceof Array)) {
    return;
  }

  // 告诉前端 弹出路径选择框
  mainIPC.send({
    id: LINKFLOW_ON_SHOW_DOWNLOAD_MODAL,
    params: {
      neids,
      savePath,
      rev: version,
    },
  });
};

export default startDownload;
