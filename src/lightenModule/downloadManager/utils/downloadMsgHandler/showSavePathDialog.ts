import { mainIPC } from '@htElectronSDK/main/ipc';
import { LINKFLOW_ON_DOWNLOAD_SAVE_PATH_DIALOG_CLOSE } from '@lightenSDK/constant/download';
import { dialog } from 'electron';

// 下载路径选择消息处理
const showSavePathDialog = (
  renderWindow: Electron.BrowserWindow,
  callback: (savePath: string) => void
) => {
  dialog
    .showOpenDialog(renderWindow, {
      title: '选择路径...',
      properties: ['openDirectory'],
    })
    .then(({ filePaths: filename }) => {
      if (filename && filename.length > 0) {
        callback(filename[0]);
      } // 更新一下
      mainIPC.send({
        id: LINKFLOW_ON_DOWNLOAD_SAVE_PATH_DIALOG_CLOSE,
        params: {
          filePath: filename && filename.length > 0 ? filename[0] : '',
        },
      });
    });
};

export default showSavePathDialog;
