import {
  CANCELED_CON,
  FAILED_CON,
  PAUSED_CON,
  RUNNING_CON,
  SUCCESS_CON,
  WAITING_CON,
} from '@common/constant';
import { TRunTaskReturn } from '@lightenModule/taskManager';
import { ensureDir } from '@utils/folderUtils';
import { getErrorMsg } from '@utils/stringUtils';
import LOG from '@htElectronSDK/main/log';
import moment from 'moment';
import { IDownloadTask } from '.';
import { downloadFile } from './utils/downloadFile';
import { getDownloadProcessFun } from './utils/getDownloadProcessFun';

export const runDownloadTask = (
  vTask: IDownloadTask,
  downloadAbortControllers: Map<string, AbortController>
) => {
  // eslint-disable-next-line max-statements
  return new Promise<TRunTaskReturn<string, IDownloadTask>>(async (resolve) => {
    try {
      vTask.status = WAITING_CON;
      // 空文件夹
      if (!vTask.list.length) {
        const { code, errMsg } = ensureDir(vTask.savePath);
        if (code === SUCCESS_CON) {
          vTask.status = SUCCESS_CON;
          vTask.completeTime = moment().format('YYYY_MM_DD_hh_mm_ss_SSS');
          resolve({ code: SUCCESS_CON, key: vTask.key, task: vTask });
        } else {
          LOG.error('runDownloadTask ensureDir', errMsg);
          vTask.errMsg = errMsg;
          vTask.status = FAILED_CON;
          resolve({ code: FAILED_CON, key: vTask.key, task: vTask });
        }
        return;
      }
      const unCompList = vTask.list.filter(
        (item) => item.status !== SUCCESS_CON
      );

      // 暂停token
      const curController = new AbortController();
      downloadAbortControllers.set(vTask.key, curController);

      // 记录任务下每个文件的下载结果
      const resList: TFunReturnFlag[] = [];
      let taskErrMsg = '';
      const processFun = await getDownloadProcessFun(vTask);

      const processTimer = setInterval(() => {
        processFun().catch((e) => {});
      }, 1000);

      for (const downloadItem of unCompList) {
        // 状态设置为正在执行
        downloadItem.status = RUNNING_CON;
        const downloadFileRes = await downloadFile(downloadItem, curController);
        if (downloadFileRes.code === SUCCESS_CON) {
          downloadItem.status = SUCCESS_CON;
          resList.push(SUCCESS_CON);
        } else if (downloadFileRes.code === CANCELED_CON) {
          // task中一个文件下载失败或者取消，取消的情况，task的状态置为暂停
          downloadItem.status = PAUSED_CON;
          resList.push(downloadFileRes.code);
          taskErrMsg = downloadFileRes.message;
          break;
        } else {
          downloadItem.status = FAILED_CON;
          resList.push(downloadFileRes.code);
          taskErrMsg = downloadFileRes.message;
          break;
        }
      }
      // 下载结束，再手工调用一次，确保进度百分比是最终的结果
      processFun();

      clearInterval(processTimer);
      downloadAbortControllers.delete(vTask.key);
      // 任务运行完成，写入状态
      if (resList.includes(FAILED_CON)) {
        // 失败
        vTask.errMsg = taskErrMsg;
        vTask.status = FAILED_CON;
        resolve({ code: FAILED_CON, key: vTask.key, task: vTask });
      } else if (resList.includes(CANCELED_CON)) {
        // 暂停
        vTask.status = PAUSED_CON;
        vTask.errMsg = taskErrMsg;
        resolve({ code: PAUSED_CON, key: vTask.key, task: vTask });
      } else {
        // 成功
        vTask.errMsg = taskErrMsg;
        vTask.completeTime = moment().format('YYYY_MM_DD_hh_mm_ss_SSS');
        vTask.status = SUCCESS_CON;
        resolve({ code: SUCCESS_CON, key: vTask.key, task: vTask });
      }
    } catch (error) {
      vTask.status = FAILED_CON;
      vTask.errMsg = getErrorMsg(error);
      resolve({ code: FAILED_CON, key: vTask.key, task: vTask });
    }
  });
};
