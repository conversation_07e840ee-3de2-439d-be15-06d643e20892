import { COVER_CON } from '@htElectronSDK/constant/ipc';
import { mainIPC } from '@htElectronSDK/main/ipc';
import {
  LINKFLOW_COPY_TO_DECLARATION_STAGE,
  LINKFLOW_CROSS_STAGE_COPY_COMPLETE,
} from '@lightenSDK/constant/crossStageCopy';
import { getDuplicateTaskStatus } from './utils.ts/crossStageCopyApi';
import { EnumCopyStatusType } from './utils.ts/interface';

export const initLightenCrossStageCopy = () => {
  mainIPC.addListener(
    LINKFLOW_COPY_TO_DECLARATION_STAGE,
    (msg: any) => {
      notification(msg.duplicateTaskBaseId);
    },
    COVER_CON
  );
};
/**
 * 复制至申报阶段任务完成，发送通知
 * @param key 任务key
 */
let timer = null;
const notification = async (key: string) => {
  const collectionId = Number(key);
  const copyStatus = await getDuplicateTaskStatus(collectionId);
  if (!copyStatus) {
    return;
  }
  if (copyStatus.status === EnumCopyStatusType.Completed) {
    mainIPC.send({
      id: LINKFLOW_CROSS_STAGE_COPY_COMPLETE,
      params: {
        message: '同步至申报阶段已完成',
        description: `本次复制成功文件数为${copyStatus.fileSuccessNumber}，失败文件数为${copyStatus.fileFailNumber}，可以到“操作记录”菜单下查询任务详细情况`,
      },
    });
    clearTimeout(timer);
    timer = null;
  } else if (copyStatus.status === EnumCopyStatusType.Processing) {
    timer = setTimeout(async () => {
      notification(String(collectionId));
    }, 3000);
  }
};
