import Request from '@utils/request';
import { SUCCESS_CON } from '@common/constant';
import LOG from '@htElectronSDK/main/log';
import { CopyFilesStatus } from './interface';

/**
 * 查询复制文件任务状态
 * @param duplicateTaskBaseId 复制任务id
 * @returns
 */
export const getDuplicateTaskStatus = async (
  duplicateTaskBaseId: number
): Promise<CopyFilesStatus> => {
  return new Promise(async (resolve) => {
    const res = await Request<CopyFilesStatus>({
      url: `/crossStageDuplicate/queryDuplicateTaskStatus`,
      params: { duplicateTaskBaseId },
    });
    const { code, data, errMsg } = res;
    //  底稿接口的data由三部分组成 {code,t,msg}=data
    // !important: 这里code=0足以判断是否成功，成功时t是允许为null，0，false等情况
    if (code === SUCCESS_CON && data.code === 0) {
      resolve(data.t);
    } else {
      LOG.error(errMsg);
      resolve(undefined);
    }
  });
};
