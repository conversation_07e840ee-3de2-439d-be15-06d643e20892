import { CANCELED_CON, PAUSED_CON } from '@common/constant';
import {
  getUserBatchExportStore,
  IUserBatchExportStore,
} from '@globalState/localStore/batchExportStore';
import { COVER_CON } from '@htElectronSDK/constant/ipc';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { IDownloadItem } from '@lightenModule/downloadManager';
import { CTaskManager } from '@lightenModule/taskManager';
import {
  LINKFLOW_ADD_BATCH_EXPORT,
  LINKFLOW_CANCEL_BATCH_EXPORT,
  LINKFLOW_CONTINUE_BATCH_EXPORT,
  LINKFLOW_GET_BATCH_EXPORT_CATALOGLIST_DETAIL,
  LINKFLOW_GET_BATCH_EXPORT_DETAIL,
  LINKFLOW_GET_BATCH_EXPORT_INFO,
  LINKFLOW_PAUSE_BATCH_EXPORT,
  LINKFLOW_RESTART_BATCH_EXPORT,
  LINKFLOW_UPDATE_PROJECT_NAME,
} from '@lightenSDK/constant/batchExport';
import { LowSync } from 'lowdb/lib';
import { runBatchDownloadTask } from './runBatchDownloadTask';
import { bETaskChangCallBack } from './utils/bETaskChangCallBack';
import { createBETaskAndRun } from './utils/createBETaskAndRun';
import {
  getBatchExportTaskCatalogListDetail,
  getBatchExportTaskDetail,
  getBatchExportTaskInfo,
  getCompleteTaskByKey,
} from './utils/getBatchExportInfo';
import { commitTaskStatus } from './utils/taskReport';
import { updateProjectName } from './utils/updateProjectName';

export interface ICataDownloadItem {
  neid: number;
  list: IDownloadItem[];
  totalNum: number;
  status: TTaskStatus; // 任务状态
  catalogId?: string; // 目录id和名称，用于展示和跳转
  catalogName?: string;
}
export interface IBatchDownloadTask {
  key: string;
  projectId: number;
  projectName: string;
  status: TTaskStatus; // 任务状态
  stageCode: string; // 所属阶段信息
  stageName: string;
  savePath: string; // 本地保存路径
  startTime: string; // 创建下载任务时间戳
  createTime: string; // 创建时间
  totalNum: number; // 总数
  creator?: string; // 创建人
  errMsg?: string; // 错误信息
  completeTime?: string; // 完成时间
}
const BATCH_DOWNLOAD_CONCURRENT_TASK_NUM = 5;

const batchDownloadAbortControllers = new Map<string, AbortController>();

const taskEqualFun = (
  vTask1: IBatchDownloadTask,
  vTask2: IBatchDownloadTask
) => {
  if (vTask1?.key && vTask1?.startTime && vTask2?.key && vTask2?.startTime) {
    return vTask1.key === vTask2.key && vTask1.startTime === vTask2.startTime;
  }
  return false;
};

const initBETaskManager = (batchStore: LowSync<IUserBatchExportStore>) => {
  // 将正在进行和等待都转为暂停
  batchStore.read();
  batchStore.data.processingList?.forEach((item) => {
    batchStore.data.pauseList?.push({
      ...item,
      status: PAUSED_CON,
    });
  });
  batchStore.data.waitingList?.forEach((item) => {
    batchStore.data.pauseList?.push({
      ...item,
      status: PAUSED_CON,
    });
  });
  batchStore.data.processingList = [];
  batchStore.data.waitingList = [];
  batchStore.write();
  const initPauseList = new Map<string, IBatchDownloadTask>();
  batchStore.data.pauseList?.forEach((item: IBatchDownloadTask) => {
    initPauseList.set(item.key, item);
  });

  return new CTaskManager<string, IBatchDownloadTask>(
    BATCH_DOWNLOAD_CONCURRENT_TASK_NUM,
    runBatchDownloadTask,
    initPauseList,
    batchStore.data.completeList,
    batchDownloadAbortControllers,
    bETaskChangCallBack,
    taskEqualFun
  );
};

export const initLightenBatchExporter = () => {
  const batchStore = getUserBatchExportStore();
  const batchExportManager = initBETaskManager(batchStore);

  mainIPC.addListener(
    LINKFLOW_ADD_BATCH_EXPORT,
    (msg: any) => {
      const {
        key,
        startTime,
        createTime,
        projectId,
        projectName,
        savePath,
        stageCode,
        stageName,
        totalNum,
        catalogInfos,
      } = msg;
      createBETaskAndRun(
        {
          key,
          startTime,
          createTime,
          projectId,
          projectName,
          savePath,
          stageCode,
          stageName,
          totalNum,
          catalogInfos,
        },
        batchExportManager
      );
    },
    COVER_CON
  );

  mainIPC.addListener(
    LINKFLOW_GET_BATCH_EXPORT_INFO,
    () => {
      return getBatchExportTaskInfo();
    },
    COVER_CON
  );

  mainIPC.addListener(
    LINKFLOW_GET_BATCH_EXPORT_DETAIL,
    (key: string) => {
      return getBatchExportTaskDetail(key);
    },
    COVER_CON
  );

  mainIPC.addListener(
    LINKFLOW_GET_BATCH_EXPORT_CATALOGLIST_DETAIL,
    (key: string) => {
      return getBatchExportTaskCatalogListDetail(key);
    },
    COVER_CON
  );

  mainIPC.addListener(
    LINKFLOW_PAUSE_BATCH_EXPORT,
    (key: string) => {
      // 暂停：进行中的任务走controller取消，等待的任务走addWaitingTaskToPauseTasks
      const controller = batchDownloadAbortControllers.get(key);
      if (controller) {
        controller.abort(CANCELED_CON);
      }
      batchExportManager.addWaitingTaskToPauseTasks(key);
    },
    COVER_CON
  );

  mainIPC.addListener(
    LINKFLOW_CONTINUE_BATCH_EXPORT,
    (key: string) => {
      batchExportManager.addTaskToRun(key);
    },
    COVER_CON
  );

  mainIPC.addListener(
    LINKFLOW_CANCEL_BATCH_EXPORT,
    (key: string) => {
      const controller = batchDownloadAbortControllers.get(key);
      if (controller) {
        controller.abort(CANCELED_CON);
      }
      // controller.abort会把任务添加到暂停队列，所以移除做了延时处理
      setTimeout(() => {
        batchExportManager.removeFromTasks(key);
        commitTaskStatus(key, CANCELED_CON);
      }, 200);
    },
    COVER_CON
  );

  mainIPC.addListener(
    LINKFLOW_RESTART_BATCH_EXPORT,
    (key: string) => {
      const task = getCompleteTaskByKey(key);
      if (task) {
        batchExportManager.removeFromCompleteTasks(task);
        batchExportManager.addTaskToRun(task.key, task);
      }
    },
    COVER_CON
  );
};

mainIPC.addListener(
  LINKFLOW_UPDATE_PROJECT_NAME,
  (params: {
    key: string;
    projectId: number;
    oldProjectName: string;
    projectName: string;
  }) => {
    return updateProjectName(params);
  },
  COVER_CON
);

export const pauseAllBatchExport = () => {
  for (const controller of batchDownloadAbortControllers.values()) {
    controller.abort(CANCELED_CON);
  }
  batchDownloadAbortControllers.clear();
};
