import {
  CANCELED_CON,
  FAILED_CON,
  PAUSED_CON,
  RUNNING_CON,
  SUCCESS_CON,
} from '@common/constant';
import { getUserBatchExportCatalogListStore } from '@globalState/localStore/batchExportStore';
import { TRunTaskReturn } from '@lightenModule/taskManager';
import { ensureDir } from '@utils/folderUtils';
import { getErrorMsg } from '@utils/stringUtils';
import LOG from '@htElectronSDK/main/log';
import moment from 'moment';
import { IBatchDownloadTask } from '.';
import { downloadCatalog } from './utils/downloadCatalog';

export const runBatchDownloadTask = (
  vTask: IBatchDownloadTask,
  batchDownloadAbortControllers: Map<string, AbortController>
) => {
  return new Promise<TRunTaskReturn<string, IBatchDownloadTask>>(
    // eslint-disable-next-line max-statements
    async (resolve) => {
      try {
        vTask.status = RUNNING_CON;
        const catalogListStore = getUserBatchExportCatalogListStore(vTask.key);
        const { cataList } = catalogListStore.data;
        // 任务下没有子目录
        if (!cataList.length) {
          const { code, errMsg } = ensureDir(vTask.savePath);
          if (code === SUCCESS_CON) {
            vTask.status = SUCCESS_CON;
            vTask.completeTime = moment().format('YYYY_MM_DD_hh_mm_ss_SSS');
            resolve({ code: SUCCESS_CON, key: vTask.key, task: vTask });
          } else {
            LOG.error('runBatchDownloadTask ensureDir', errMsg);
            vTask.errMsg = errMsg;
            vTask.status = FAILED_CON;
            resolve({ code: FAILED_CON, key: vTask.key, task: vTask });
          }
          return;
        }
        const unCompList = cataList.filter(
          (item) => item.status !== SUCCESS_CON
        );

        // 暂停token
        const curController = new AbortController();
        batchDownloadAbortControllers.set(vTask.key, curController);

        // 记录任务下每个文件的下载结果
        const resList: TFunReturnFlag[] = [];
        let taskErrMsg = '';

        for (const downloadCata of unCompList) {
          const downloadCataRes = await downloadCatalog(
            vTask.key,
            downloadCata.neid,
            vTask.savePath,
            curController
          );

          if (downloadCataRes.code === SUCCESS_CON) {
            downloadCata.status = SUCCESS_CON;
            resList.push(SUCCESS_CON);
          } else if (downloadCataRes.code === CANCELED_CON) {
            // task中一个文件下载失败或者取消，取消的情况，task的状态置为暂停
            downloadCata.status = PAUSED_CON;
            resList.push(downloadCataRes.code);
            taskErrMsg = downloadCataRes.errMsg;
            break;
          } else {
            // 底稿导出，一个文件失败，继续任务
            downloadCata.status = FAILED_CON;
            resList.push(downloadCataRes.code);
            taskErrMsg = downloadCataRes.errMsg;
          }
        }
        batchDownloadAbortControllers.delete(vTask.key);
        // 任务运行完成，写入状态
        if (resList.includes(CANCELED_CON)) {
          // 暂停
          vTask.errMsg = taskErrMsg;
          vTask.status = PAUSED_CON;
          resolve({ code: PAUSED_CON, key: vTask.key, task: vTask });
        } else if (resList.includes(FAILED_CON)) {
          // 失败
          vTask.status = FAILED_CON;
          vTask.errMsg = taskErrMsg;
          resolve({ code: FAILED_CON, key: vTask.key, task: vTask });
        } else {
          // 成功
          vTask.errMsg = taskErrMsg;
          vTask.status = SUCCESS_CON;
          vTask.completeTime = moment().format('YYYY_MM_DD_hh_mm_ss_SSS');
          resolve({ code: SUCCESS_CON, key: vTask.key, task: vTask });
        }
      } catch (error) {
        vTask.status = FAILED_CON;
        vTask.errMsg = getErrorMsg(error);
        resolve({ code: FAILED_CON, key: vTask.key, task: vTask });
      }
    }
  );
};
