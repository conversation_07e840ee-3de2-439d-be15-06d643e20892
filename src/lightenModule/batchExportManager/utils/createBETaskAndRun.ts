import { WAITING_CON } from '@common/constant';
import { getUserBatchExportCatalogListStore } from '@globalState/localStore/batchExportStore';
import ioneUser from '@globalState/user';
import { IDownloadItem } from '@lightenModule/downloadManager';
import { CTaskManager } from '@lightenModule/taskManager';
import path from 'path';
import { IBatchDownloadTask } from '..';

export const createBETaskAndRun = (
  data: {
    key: string;
    startTime: string;
    createTime: string;
    projectId: number;
    projectName: string;
    savePath: string;
    stageCode: string;
    stageName: string;
    totalNum: number;
    catalogInfos: { neid: number; catalogId?: string; catalogName?: string }[];
  },
  batchExportManager: CTaskManager<string, IBatchDownloadTask>
) => {
  const {
    key,
    startTime,
    createTime,
    projectId,
    projectName,
    savePath,
    stageCode,
    stageName,
    totalNum,
    catalogInfos,
  } = data;
  const creator = ioneUser.getUser()?.username || '';
  const task: IBatchDownloadTask = {
    key,
    projectId,
    projectName,
    status: WAITING_CON,
    stageCode,
    stageName,
    savePath: path.join(savePath, `${stageName}-${startTime}`),
    startTime,
    createTime,
    creator,
    totalNum,
  };
  // 初始化任务的目录列表
  const catalogListStore = getUserBatchExportCatalogListStore(key);
  catalogListStore.read();
  catalogListStore.data.cataList = catalogInfos.map((item) => ({
    neid: item.neid,
    totalNum: 0,
    completedNum: 0,
    list: [] as IDownloadItem[],
    status: WAITING_CON,
    catalogId: item.catalogId,
    catalogName: item.catalogName,
  }));
  catalogListStore.write();
  // run
  batchExportManager.addTaskToRun(task.key, task);
};
