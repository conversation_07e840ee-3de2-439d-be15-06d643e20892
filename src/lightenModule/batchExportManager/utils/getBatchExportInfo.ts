import {
  getUserBatchExportCatalogListStore,
  getUserBatchExportStore,
} from '@globalState/localStore/batchExportStore';
import { IBatchDownloadTask } from '..';

/**
 * 获取主任务列表
 * @returns
 */
export const getBatchExportTaskInfo = (): {
  processingList: IBatchDownloadTask[];
  waitingList: IBatchDownloadTask[];
  pauseList: IBatchDownloadTask[];
  completeList: IBatchDownloadTask[];
} => {
  const batchStore = getUserBatchExportStore();
  return { ...batchStore.data };
};

/**
 * 获取任务详情
 * @param key
 * @returns
 */
export const getBatchExportTaskDetail = (key: string) => {
  const batchStore = getUserBatchExportStore();
  return batchStore.data.processingList
    .concat(batchStore.data.waitingList)
    .concat(batchStore.data.pauseList)
    .concat(batchStore.data.completeList)
    .find((item) => item.key === key);
};

/**
 * 根据key获取任务，用于
 * 1. 失败重试，从完成队列中取出任务，并添加到运行队列
 * @param key
 */
export const getCompleteTaskByKey = (key: string) => {
  const batchStore = getUserBatchExportStore();
  return batchStore.data.completeList.find((item) => item.key === key);
};
/**
 * 获取任务下目录和文件详情
 * @param key
 * @returns
 */
export const getBatchExportTaskCatalogListDetail = (key: string) => {
  const catalogListStore = getUserBatchExportCatalogListStore(key);
  return [...catalogListStore.data.cataList];
};
