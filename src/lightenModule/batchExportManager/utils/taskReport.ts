import LOG from '@htElectronSDK/main/log';
import Request from '@utils/request';

export interface ITaskDetailParam {
  fileNeid: number;
  fileName: string;
  status: TTaskStatus;
  failReason?: string;
}

export const MTaskStatus = new Map<TTaskStatus | TCANCELED_FLAG, number>([
  ['SUCCESS', 5],
  ['FAILED', 0],
  ['RUNNING', 1],
  ['WAITING', 2],
  ['PAUSED', 3],
  ['CANCELED', 4],
]);

/**
 * 新增/更新任务明细
 * @param options
 */
export const commitTaskDetail = async (batchDetails: {
  key: string;
  batchExportDetailList: ITaskDetailParam[];
}) => {
  const { key, batchExportDetailList } = batchDetails;
  const res = await Request<{ total: number; list: any[] }>({
    url: '/pc/batch-export/detail',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      key,
      batchExportDetailList: batchExportDetailList.map((item) => ({
        ...item,
        status: MTaskStatus.get(item.status) || 0,
      })),
    },
  });
  LOG.info('commitTaskDetail-res', batchDetails, res);
};

/**
 * 更新任务状态
 * @param key
 * @param status
 */
export const commitTaskStatus = async (
  key: string,
  status: TTaskStatus | TCANCELED_FLAG
) => {
  const res = await Request<{ total: number; list: any[] }>({
    url: '/pc/batch-export/task-status',
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: { key, status: MTaskStatus.get(status) || 0 },
  });
  LOG.info('commitTaskStatus-res', key, '--状态', status, res);
};
