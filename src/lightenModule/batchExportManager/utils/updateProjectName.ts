import {
  getUserBatchExportCatalogListStore,
  getUserBatchExportStore,
} from '@globalState/localStore/batchExportStore';

/**
 * 更新项目名称
 */
export const updateProjectName = (options: {
  key: string;
  projectId: number;
  oldProjectName: string;
  projectName: string;
}) => {
  const { key, projectId, oldProjectName, projectName } = options;
  const exportStore = getUserBatchExportStore();
  const list = exportStore.data.processingList
    .concat(exportStore.data.pauseList)
    .concat(exportStore.data.waitingList)
    .concat(exportStore.data.completeList);
  list.forEach((item) => {
    if (item.key === key) {
      item.projectName = projectName;
    }
  });
  exportStore.write();

  const catalogListStore = getUserBatchExportCatalogListStore(key);

  catalogListStore.data.cataList.forEach((item) => {
    item.list.forEach((file) => {
      file.file.path = file.file.path.replace(oldProjectName, projectName);
      file.savePath = file.savePath.replace(oldProjectName, projectName);
    });
  });

  catalogListStore.write();
};
