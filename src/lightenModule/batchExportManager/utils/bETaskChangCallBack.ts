import {
  ADD_CON,
  COMPLETE_CON,
  PAUSED_CON,
  RUNNING_CON,
  WAITING_CON,
} from '@common/constant';
import { LowSync } from 'lowdb/lib';
import {
  IUserBatchExportStore,
  getUserBatchExportCatalogListStore,
  getUserBatchExportStore,
} from '@globalState/localStore/batchExportStore';
import { mainIPC } from '@htElectronSDK/main/ipc';
import {
  LINKFLOW_BATCH_EXPORT_UPDATE_STATUS,
  LINKFLOW_ON_BATCH_COMPLETE,
} from '@lightenSDK/constant/batchExport';
import { TTaskChangeCallBackFun } from '@lightenModule/taskManager';
import { IBatchDownloadTask } from '..';
import { commitTaskStatus } from './taskReport';

export const bETaskChangCallBack: TTaskChangeCallBackFun<IBatchDownloadTask> = (
  changeTaskInfos
) => {
  const batchStore = getUserBatchExportStore();
  batchStore.read();
  
  for (const changeTaskInfo of changeTaskInfos) {
    // 更新存储数据
    updateBatchStore(batchStore, changeTaskInfo);

    // 新增动作：修改状态
    if (changeTaskInfo.operationType === ADD_CON) {
      // 提交变更状态，完成状态包括成功和失败，修改为具体状态
      commitTaskStatus(
        changeTaskInfo.Task.key,
        changeTaskInfo.changeType === COMPLETE_CON
          ? changeTaskInfo.Task.status
          : changeTaskInfo.changeType
      );
      // 完成时发送通知
      if (changeTaskInfo.changeType === COMPLETE_CON) {
        notification(changeTaskInfo.Task.key);
      }
    } else {
      // 删除动作无需操作（取消任务时，外部将任务状态改为已取消，任务中的文件状态不处理忽略））
    }
  }

  batchStore.write();

  // 更新主任务状态
  mainIPC.send({ id: LINKFLOW_BATCH_EXPORT_UPDATE_STATUS });
};

const updateBatchStore = (
  batchStore: LowSync<IUserBatchExportStore>,
  changeTaskInfo: {
    changeType: TCOMPLETE_FLAG | TPAUSED_FLAG | TRUNNING_FLAG | TWAITING_FLAG;
    operationType: TADD_FLAG | TDELETE_FLAG;
    Task: IBatchDownloadTask;
  }
) => {
  switch (changeTaskInfo.changeType) {
    case RUNNING_CON:
      if (changeTaskInfo.operationType === ADD_CON) {
        batchStore.data.processingList?.push({
          ...changeTaskInfo.Task,
          status: RUNNING_CON,
        });
      } else {
        batchStore.data.processingList = batchStore.data.processingList?.filter(
          (item) => item.key !== changeTaskInfo.Task.key
        );
      }
      break;
    case WAITING_CON:
      if (changeTaskInfo.operationType === ADD_CON) {
        batchStore.data.waitingList?.push({
          ...changeTaskInfo.Task,
          status: WAITING_CON,
        });
      } else {
        batchStore.data.waitingList = batchStore.data.waitingList?.filter(
          (item) => item.key !== changeTaskInfo.Task.key
        );
      }
      break;
    case PAUSED_CON:
      if (changeTaskInfo.operationType === ADD_CON) {
        batchStore.data.pauseList?.push({
          ...changeTaskInfo.Task,
          status: PAUSED_CON,
        });
      } else {
        batchStore.data.pauseList = batchStore.data.pauseList?.filter(
          (item) => item.key !== changeTaskInfo.Task.key
        );
      }
      break;
    case COMPLETE_CON:
      if (changeTaskInfo.operationType === ADD_CON) {
        batchStore.data.completeList?.push(changeTaskInfo.Task);
      } else {
        batchStore.data.completeList = batchStore.data.completeList?.filter(
          (item) =>
            item.key !== changeTaskInfo.Task.key ||
            item.startTime !== changeTaskInfo.Task.startTime
        );
      }
      break;
    default:
      break;
  }
};

/**
 * 批量导出任务完成，发送通知
 * @param key 任务key
 */
const notification = (key: string) => {
  const catalogListStore = getUserBatchExportCatalogListStore(key);
  let successCount = 0;
  let failureCount = 0;
  catalogListStore.data.cataList.forEach((cata) => {
    cata.list.forEach((file) => {
      if (file.status === 'SUCCESS') {
        successCount += 1;
      } else {
        failureCount += 1;
      }
    });
  });
  mainIPC.send({
    id: LINKFLOW_ON_BATCH_COMPLETE,
    params: {
      message: '文件导出任务已完成',
      description: `本次导出成功文件数为${successCount}，导出失败文件数为${failureCount}`,
    },
  });
};
