import {
  CANCELED_CON,
  FAILED_CON,
  PAUSED_CON,
  RUNNING_CON,
  SUCCESS_CON,
  WAITING_CON,
} from '@common/constant';
import { getFilesFromNeidCancelable } from '@utils/lightenApi/download';
import { getErrorMsg } from '@utils/stringUtils';
import path from 'path';
import { downloadFile } from '@lightenModule/downloadManager/utils/downloadFile';
import { getUserBatchExportCatalogListStore } from '@globalState/localStore/batchExportStore';
import { ITaskDetailParam, commitTaskDetail } from './taskReport';

export interface IDownloadCatalogRet {
  code: TFunReturnFlag;
  errMsg?: string;
}

/**
 * 下载某个底稿叶子目录下文件的函数
 * @param cataLogInfo 底稿叶子目录信息
 * @returns
 */
export const downloadCatalog = (
  key: string,
  catalogNeid: number,
  rootPath: string,
  curController: AbortController
) => {
  return new Promise<IDownloadCatalogRet>(async (resolve) => {
    try {
      const catalogListStore = getUserBatchExportCatalogListStore(key);
      const cataLogInfo = catalogListStore.data.cataList.find(
        (item) => item.neid === catalogNeid
      );
      cataLogInfo.status = RUNNING_CON;
      const resInfo: IDownloadCatalogRet = {
        code: SUCCESS_CON,
      };
      // 首次下载
      if (cataLogInfo.totalNum === 0) {
        // 只有底稿会批量导出，所以pathType为固定的ent
        const filesRep = await getFilesFromNeidCancelable(
          { neid: cataLogInfo.neid, pathType: 'ent' },
          curController
        );
        if (filesRep.code === SUCCESS_CON) {
          cataLogInfo.totalNum = filesRep.total;
          cataLogInfo.list = filesRep.fileModels.map((item) => ({
            file: item,
            downloadSize: 0,
            savePath: path.join(rootPath, item.path),
            status: WAITING_CON,
          }));
          catalogListStore.write();
        } else {
          resolve(filesRep);
          return;
        }
      }
      // 筛选未完成的任务
      const unComp = cataLogInfo.list.filter(
        (item) => item.status !== SUCCESS_CON
      );
      const batchDetails: ITaskDetailParam[] = [];
      for (const downItem of unComp) {
        // 状态设置为正在执行
        downItem.status = RUNNING_CON;
        const downloadFileRes = await downloadFile(downItem, curController);

        if (downloadFileRes.code === SUCCESS_CON) {
          downItem.status = SUCCESS_CON;
          batchDetails.push({
            fileNeid: downItem.file.neid,
            fileName: downItem.file.name,
            status: downItem.status,
          });
        } else if (downloadFileRes.code === CANCELED_CON) {
          // task中一个文件下载失败或者取消，取消的情况，task的状态置为暂停
          downItem.status = PAUSED_CON;
          resInfo.code = CANCELED_CON;
          resInfo.errMsg = downloadFileRes.message;
          batchDetails.push({
            fileNeid: downItem.file.neid,
            fileName: downItem.file.name,
            status: downItem.status,
            failReason: downloadFileRes.message,
          });
          break;
        } else {
          // 底稿导出，一个文件失败，继续任务
          downItem.status = FAILED_CON;
          resInfo.code = FAILED_CON;
          resInfo.errMsg = downloadFileRes.message;
          batchDetails.push({
            fileNeid: downItem.file.neid,
            fileName: downItem.file.name,
            status: downItem.status,
            failReason: downloadFileRes.message,
          });
        }

        catalogListStore.write();
      }
      cataLogInfo.status =
        resInfo.code === CANCELED_CON ? PAUSED_CON : resInfo.code;
      commitTaskDetail({ key, batchExportDetailList: batchDetails });
      catalogListStore.write();
      resolve(resInfo);
    } catch (error) {
      resolve({
        code: FAILED_CON,
        errMsg: getErrorMsg(error),
      });
    }
  });
};
