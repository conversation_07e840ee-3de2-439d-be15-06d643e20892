import ioneUser, { IIoneUser } from '@globalState/user';
import { COVER_CON } from '@htElectronSDK/constant/ipc';
import { mainIPC } from '@htElectronSDK/main/ipc';
import {
  initLightenBatchExporter,
  pauseAllBatchExport,
} from '@lightenModule/batchExportManager';
import {
  initLightenLocalCollecter,
  pauseAllLocalCollection,
} from '@lightenModule/localCollectionManager';
import {
  initLightenDownloader,
  pauseAllDownload,
} from '@lightenModule/downloadManager';
import { initUploadFileAtOnce } from '@lightenModule/uploadFileAtOnce';
import {
  initLightenUploader,
  pauseAllUpload,
} from '@lightenModule/uploadManager';
import {
  LINKFLOW_LOGIN_SUCCESS,
  LINKFLOW_LOGOUT_SUCCESS,
} from '@lightenSDK/constant/global';
import { initLightenCrossStageCopy } from '@lightenModule/crossStageCopy';

export const initLoginModule = () => {
  mainIPC.addListener(
    LINKFLOW_LOGIN_SUCCESS,
    (vIoneUser: IIoneUser) => {
      ioneUser.initUser(vIoneUser);
      initLightenUploader();
      initLightenDownloader();
      // 独立上传文件接口，本地归集等功能使用
      initUploadFileAtOnce();
      initLightenBatchExporter();
      initLightenLocalCollecter();
      initLightenCrossStageCopy();

      // 拦截富文本的getImage,加入token
      mainIPC
        .getMainWindowWebContents()
        .session.webRequest.onBeforeSendHeaders((details, callback) => {
          if (details.url.includes('ueditor/getImage')) {
            details.requestHeaders.ioneauthorization =
              ioneUser.getUser()?.ioneauthorization || '';
            callback({ cancel: false, requestHeaders: details.requestHeaders });
          } else {
            callback({ cancel: false, requestHeaders: details.requestHeaders });
          }
        });
    },
    COVER_CON
  );

  mainIPC.addListener(
    LINKFLOW_LOGOUT_SUCCESS,
    () => {
      pauseAllUpload();
      pauseAllDownload();
      pauseAllBatchExport();
      pauseAllLocalCollection();
    },
    COVER_CON
  );
};
