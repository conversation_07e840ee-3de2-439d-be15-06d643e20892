import {
  ADD_CON,
  COMPLETE_CON,
  DELETE_CON,
  FAILED_CON,
  PAUSED_CON,
  RUNNING_CON,
  SUCCESS_CON,
  WAITING_CON,
} from '@common/constant';

export type TTaskRunFunReturnFlag = TSUCCESS_FLAG | TPAUSED_FLAG | TFAILED_FLAG;

/**
 * 任务执行函数返回值，只有完成和暂停，成功和失败都属于完成
 */
export type TRunTaskReturn<T, V> = {
  code: TTaskRunFunReturnFlag;
  key: T;
  task: V;
};

export type TTaskChangeCallBackFun<V> = (
  changeTaskInfos: {
    changeType: TCOMPLETE_FLAG | TPAUSED_FLAG | TRUNNING_FLAG | TWAITING_FLAG;
    operationType: TADD_FLAG | TDELETE_FLAG;
    Task: V;
  }[]
) => void;

/**
 * 任务管理器
 * __concurrentTaskNum: 并行任务数
 * __processingTasks: 进行中的任务
 * __waitingTasks: 等待中的任务
 * __pauseTasks: 暂停中的任务
 * __completeTasks: 已完成的任务
 * __runTask: 任务执行函数
 * __taskChangeCallBack: 任务列表变动的回调函数
 */
export class CTaskManager<T, V> {
  private readonly __concurrentTaskNum: number;

  private readonly __processingTasks: Map<T, V>;

  private readonly __waitingTasks: Map<T, V>;

  private readonly __pauseTasks: Map<T, V>;

  private readonly __completeTasks: V[];

  private readonly __abortControllers: Map<T, AbortController>;

  private readonly __runTask: (
    vTask: V,
    abortControllers: Map<T, AbortController>
  ) => Promise<TRunTaskReturn<T, V>>;

  /**
   * 任务列表变动的回调函数
   * 返回各个列表的深拷贝
   * 使用场景：比如列表变动，需要写入文件等
   */
  private readonly __taskChangeCallBack: TTaskChangeCallBackFun<V>;

  private readonly __taskEqualFun: (vTask1: V, vTask2: V) => boolean;

  /**
   * 构造函数
   * @param vConcurrentTaskNum 并行任务数
   * @param runTaskFun 任务执行函数
   * @param vPauseTasks 暂停中的任务
   * @param vCompleteTasks 已完成的任务
   * @param taskChangeCallBack 任务列表变动的回调函数
   */
  constructor(
    vConcurrentTaskNum: number,
    runTaskFun: (
      vTask: V,
      abortControllers: Map<T, AbortController>
    ) => Promise<TRunTaskReturn<T, V>>,
    vPauseTasks: Map<T, V>,
    vCompleteTasks: V[],
    vAbortControllers: Map<T, AbortController>,
    taskChangeCallBack: TTaskChangeCallBackFun<V>,
    taskEqualFun: (vTask1: V, vTask2: V) => boolean
  ) {
    this.__concurrentTaskNum = vConcurrentTaskNum;
    this.__processingTasks = new Map<T, V>();
    this.__waitingTasks = new Map<T, V>();
    this.__pauseTasks = vPauseTasks || new Map<T, V>();
    this.__completeTasks = vCompleteTasks || [];
    this.__abortControllers = vAbortControllers;

    runTaskFun && (this.__runTask = runTaskFun);

    if (taskChangeCallBack) {
      this.__taskChangeCallBack = taskChangeCallBack;
    }
    taskEqualFun && (this.__taskEqualFun = taskEqualFun);
  }

  /**
   * 开始下一个任务
   */
  private __startNextTask() {
    if (
      this.__processingTasks.size < this.__concurrentTaskNum &&
      this.__waitingTasks.size > 0
    ) {
      const [firstTask] = this.__waitingTasks.entries();
      const [key, curTask] = firstTask;

      this.__waitingTasks.delete(key);
      this.__taskChangeCallBack([
        {
          changeType: WAITING_CON,
          operationType: DELETE_CON,
          Task: curTask,
        },
      ]);
      this.__startTask(key, curTask);
    }
  }

  /**
   * 开始任务
   */
  private async __startTask(key: T, vTask: V) {
    // 如果队列中进行的任务已经饱和，则进等待队列
    if (this.__processingTasks.size < this.__concurrentTaskNum) {
      this.__processingTasks.set(key, vTask);

      this.__taskChangeCallBack([
        {
          changeType: RUNNING_CON,
          operationType: ADD_CON,
          Task: vTask,
        },
      ]);

      const resTask = await this.__runTask(vTask, this.__abortControllers);
      // 任务结束，从正在进行的任务中删除任务，先判断一下正在进行有没有这个数据，因为可能在任务完成前，用户点了删除或者全部删除的按钮
      // 如果已经从正在进行中删除，则跳过这个处理
      if (this.__processingTasks.has(key)) {
        this.__processingTasks.delete(key);

        this.__taskChangeCallBack([
          {
            changeType: RUNNING_CON,
            operationType: DELETE_CON,
            Task: resTask.task,
          },
        ]);
        // 根据任务运行结果将任务放到对应的列表中
        if (resTask.code === SUCCESS_CON || resTask.code === FAILED_CON) {
          this.__completeTasks.push(resTask.task);

          this.__taskChangeCallBack([
            {
              changeType: COMPLETE_CON,
              operationType: ADD_CON,
              Task: resTask.task,
            },
          ]);
        } else {
          this.__pauseTasks.set(resTask.key, resTask.task);

          this.__taskChangeCallBack([
            {
              changeType: PAUSED_CON,
              operationType: ADD_CON,
              Task: resTask.task,
            },
          ]);
        }
      }

      this.__startNextTask();
    } else {
      this.__waitingTasks.set(key, vTask);
      this.__taskChangeCallBack([
        {
          changeType: WAITING_CON,
          operationType: ADD_CON,
          Task: vTask,
        },
      ]);
    }
  }

  /**
   * 添加任务来执行
   */
  public addTaskToRun(key: T, vTask?: V) {
    // 任务正在进行或者队列等待中
    if (this.__processingTasks.has(key) || this.__waitingTasks.has(key)) {
      return '任务正在进行，无需重复添加！';
    }

    // 任务在暂停任务中，重新拉起
    if (this.__pauseTasks.has(key)) {
      const curTask = this.__pauseTasks.get(key);
      this.__pauseTasks.delete(key);

      this.__taskChangeCallBack([
        {
          changeType: PAUSED_CON,
          operationType: DELETE_CON,
          Task: curTask,
        },
      ]);

      this.__startTask(key, curTask);
      return;
    }
    if (vTask) {
      this.__startTask(key, vTask);
    }
  }

  /**
   * 增加等待任务到暂停列表
   * @param key 标识符
   * @param vTask 任务
   */
  public addWaitingTaskToPauseTasks(key: T) {
    if (this.__waitingTasks.has(key)) {
      const curTask = this.__waitingTasks.get(key);
      this.__pauseTasks.set(key, curTask);
      this.__waitingTasks.delete(key);
      this.__taskChangeCallBack([
        {
          changeType: PAUSED_CON,
          operationType: ADD_CON,
          Task: curTask,
        },
        {
          changeType: WAITING_CON,
          operationType: DELETE_CON,
          Task: curTask,
        },
      ]);
    }
  }

  /**
   * 删除进行中、等待或者暂停的任务, 在这三种任务中，key是唯一的，所以只会删除一次
   * @param index
   */
  public removeFromTasks(key: T) {
    if (this.__processingTasks.has(key)) {
      const cTask = this.__processingTasks.get(key);
      this.__processingTasks.delete(key);
      this.__taskChangeCallBack([
        {
          changeType: RUNNING_CON,
          operationType: DELETE_CON,
          Task: cTask,
        },
      ]);
    } else if (this.__waitingTasks.has(key)) {
      const cTask = this.__waitingTasks.get(key);
      this.__waitingTasks.delete(key);
      this.__taskChangeCallBack([
        {
          changeType: WAITING_CON,
          operationType: DELETE_CON,
          Task: cTask,
        },
      ]);
    } else if (this.__pauseTasks.has(key)) {
      const cTask = this.__pauseTasks.get(key);
      this.__pauseTasks.delete(key);
      this.__taskChangeCallBack([
        {
          changeType: PAUSED_CON,
          operationType: DELETE_CON,
          Task: cTask,
        },
      ]);
    }
  }

  /**
   * 删除某个完成的任务，因为完成的任务key值有可能重复
   * @param index
   */
  public removeFromCompleteTasks(vTask: V | undefined) {
    const fIndex = this.__completeTasks.findIndex((item) =>
      this.__taskEqualFun(item, vTask)
    );
    if (fIndex !== -1) {
      const delTask = this.__completeTasks.splice(fIndex, 1);
      this.__taskChangeCallBack([
        {
          changeType: COMPLETE_CON,
          operationType: DELETE_CON,
          Task: delTask[0],
        },
      ]);
    }
  }
}
