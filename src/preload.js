/**
 * 暴露给渲染进程的方法和属性
 * 官网文档：https://www.electronjs.org/zh/docs/latest/tutorial/tutorial-preload
 */
import * as htElectronSDK from './rendererSDK';
// import fs from 'fs';
// import path from 'path';
// import { contextBridge, ipcRenderer } from 'electron';
// import '@openim/electron-client-sdk/lib/preload';
// import { Platform } from '@openim/wasm-client-sdk';
// import { isProd } from '../utils';

const fs = require('fs');
const path = require('path');
const { contextBridge, ipcRenderer } = require('electron');
require('@ht/openim-electron-client-sdk/lib/preload');
const { Platform } = require('@ht/openim-wasm-client-sdk');

const isProd = false;

const getPlatform = () => {
  if (process.platform === 'darwin') {
    return Platform.MacOSX;
  }
  if (process.platform === 'win32') {
    return Platform.Windows;
  }
  return Platform.Linux;
};

const getDataPath = (key) => {
  switch (key) {
    case 'public':
      return isProd ? ipcRenderer.sendSync('getDataPath', 'public') : '';
    case 'sdkResources':
      return isProd ? ipcRenderer.sendSync('getDataPath', 'sdkResources') : '';
    case 'logsPath':
      return isProd ? ipcRenderer.sendSync('getDataPath', 'logsPath') : '';
    default:
      return '';
  }
};

const subscribe = (channel, callback) => {
  const subscription = (_, ...args) => callback(...args);
  ipcRenderer.on(channel, subscription);
  return () => ipcRenderer.removeListener(channel, subscription);
};

const subscribeOnce = (channel, callback) => {
  ipcRenderer.once(channel, (_, ...args) => callback(...args));
};

const unsubscribeAll = (channel) => {
  ipcRenderer.removeAllListeners(channel);
};

const ipcInvoke = (channel, ...arg) => {
  return ipcRenderer.invoke(channel, ...arg);
};

const ipcSendSync = (channel, ...arg) => {
  return ipcRenderer.sendSync(channel, ...arg);
};

const getUniqueSavePath = (originalPath) => {
  let counter = 0;
  let savePath = originalPath;
  const fileDir = path.dirname(originalPath);
  let fileName = path.basename(originalPath);
  const fileExt = path.extname(originalPath);
  const baseName = path.basename(fileName, fileExt);

  while (fs.existsSync(savePath)) {
    counter++;
    fileName = `${baseName}(${counter})${fileExt}`;
    savePath = path.join(fileDir, fileName);
  }

  return savePath;
};

const getFileByPath = async (filePath) => {
  try {
    const filename = path.basename(filePath);
    const data = await fs.promises.readFile(filePath);
    return new File([data], filename);
  } catch (error) {
    console.log(error);
    return null;
  }
};

const saveFileToDisk = async ({ file, sync }) => {
  const arrayBuffer = await file.arrayBuffer();
  const saveDir = ipcRenderer.sendSync('getDataPath', 'sdkResources');
  const savePath = path.join(saveDir, file.name);
  const uniqueSavePath = getUniqueSavePath(savePath);
  if (!fs.existsSync(saveDir)) {
    fs.mkdirSync(saveDir, { recursive: true });
  }
  if (sync) {
    await fs.promises.writeFile(uniqueSavePath, Buffer.from(arrayBuffer));
  } else {
    fs.promises.writeFile(uniqueSavePath, Buffer.from(arrayBuffer));
  }
  return uniqueSavePath;
};

const ipcSend = (channel, ...arg) => {
  return ipcRenderer.send(channel, ...arg);
};

const Api = {
  getDataPath,
  getVersion: () => process.version,
  getPlatform,
  getSystemVersion: process.getSystemVersion,
  subscribe,
  subscribeOnce,
  unsubscribeAll,
  ipcInvoke,
  ipcSendSync,
  getFileByPath,
  saveFileToDisk,
  ipcSend,
};

console.log('from preload from preload from preload from preload');

contextBridge.exposeInMainWorld('electronAPI', Api);

// 所有的 Node.js API接口 都可以在 preload 进程中被调用.
// 它拥有与Chrome扩展一样的沙盒。

window.addEventListener('DOMContentLoaded', () => {
  // const replaceText = (selector, text) => {
  //   const element = document.getElementById(selector);
  //   if (element) {
  //     element.innerText = text;
  //   }
  // };
  // for (const dependency of ['chrome', 'node', 'electron']) {
  //   replaceText(`${dependency}-version`, process.versions[dependency]);
  // }
});

contextBridge.exposeInMainWorld('htElectronSDK', {
  ...htElectronSDK,
});
