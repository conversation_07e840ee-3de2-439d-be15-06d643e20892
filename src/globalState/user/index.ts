import LOG from '@htElectronSDK/main/log';
import { toStringSafely } from '@utils/stringUtils';

export interface IIoneUser {
  ioneauthorization: string;
  userid: string;
  username: string;
  userDeptList: string[];
  ezid: EUserType;
}

/**
 * 用户类型
 * 300-华泰联合-联合用户
 * 100-华泰集团-债融用户
 */
export enum EUserType {
  union = 300,
  bond = 100,
  ibd = 501,
}

let currentUser: IIoneUser | null = null;

const ioneUser = {
  initUser: (user: IIoneUser) => {
    ioneUser.setUser(user);
  },
  getUser: () => {
    if (currentUser) {
      return currentUser;
    }
    return null;
  },
  setUser: (user?: IIoneUser) => {
    if (user?.userid && user.username && user.ioneauthorization) {
      LOG.info(`setUser: ${toStringSafely(user)}`);
      currentUser = user;
    }
  },
};

export default ioneUser;
