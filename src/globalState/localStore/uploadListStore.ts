import ioneUser, { IIoneUser } from '@globalState/user';
import { getAppLocalPath } from '@lightenModule/configFile';
import { IUploadTask } from '@lightenModule/uploadManager';
import { LowSync } from 'lowdb';
import { JSONFileSync } from 'lowdb/node';
import path from 'path';

export interface IUserUploadStore {
  processingList: IUploadTask[];
  waitingList: IUploadTask[];
  pauseList: IUploadTask[];
  completeList: IUploadTask[];
}

const userUploadListStore = new Map<string, LowSync<IUserUploadStore>>();
const getStorKey = (user: IIoneUser) => `${user.userid} - ${user.ezid}`;

export const getUserUploadListStore = () => {
  const curUser = ioneUser.getUser();

  if (userUploadListStore.has(getStorKey(curUser))) {
    return userUploadListStore.get(getStorKey(curUser));
  } else {
    const lightenStoreDir = path.join(
      getAppLocalPath(),
      'config',
      'upload',
      `${curUser.userid}-${curUser.ezid}-upload.json`
    ); // 配置文件根目录

    const cStore = new LowSync<IUserUploadStore>(
      new JSONFileSync<IUserUploadStore>(lightenStoreDir),
      {
        processingList: [],
        waitingList: [],
        pauseList: [],
        completeList: [],
      }
    );
    userUploadListStore.set(getStorKey(curUser), cStore);
    return cStore;
  }
};
