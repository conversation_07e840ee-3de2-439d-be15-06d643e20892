import ioneUser, { IIoneUser } from '@globalState/user';
import { getAppLocalPath } from '@lightenModule/configFile';
import { LowSync } from 'lowdb';
import { JSONFileSync } from 'lowdb/node';
import path from 'path';
import { ensureDir } from '@utils/folderUtils';
import {
  IBatchDownloadTask,
  ICataDownloadItem,
} from '../../lightenModule/batchExportManager';

export interface IUserBatchExportStore {
  processingList: IBatchDownloadTask[];
  waitingList: IBatchDownloadTask[];
  pauseList: IBatchDownloadTask[];
  completeList: IBatchDownloadTask[];
}

export interface IUserBatchExportCatalogListStore {
  cataList: ICataDownloadItem[];
}

const userBatchExportStore = new Map<string, LowSync<IUserBatchExportStore>>();
const userBatchExportCatalogListStore = new Map<
  string,
  LowSync<IUserBatchExportCatalogListStore>
>();
const getStorKey = (user: IIoneUser) => `${user.userid}-${user.ezid}`;

/**
 * 存储任务列表
 * @returns
 */
export const getUserBatchExportStore = () => {
  const curUser = ioneUser.getUser();

  if (userBatchExportStore.has(getStorKey(curUser))) {
    return userBatchExportStore.get(getStorKey(curUser));
  } else {
    const lightenStoreDir = path.join(
      getAppLocalPath(),
      'config',
      'download',
      `${curUser.userid}-${curUser.ezid}-batchexport.json`
    ); // 配置文件根目录
    const cStore = new LowSync<IUserBatchExportStore>(
      new JSONFileSync<IUserBatchExportStore>(lightenStoreDir),
      {
        processingList: [],
        waitingList: [],
        pauseList: [],
        completeList: [],
      }
    );
    cStore.read();
    userBatchExportStore.set(getStorKey(curUser), cStore);
    return cStore;
  }
};

/**
 * 存储任务的目录和文件
 * 底稿导出需要的下载项，以子目录为最小单元
 * @param key task key
 * @returns
 */
export const getUserBatchExportCatalogListStore = (key: string) => {
  const curUser = ioneUser.getUser();

  if (userBatchExportCatalogListStore.has(key)) {
    return userBatchExportCatalogListStore.get(key);
  } else {
    const lightenStoreParentDir = path.join(
      getAppLocalPath(),
      'config',
      'download',
      `${curUser.userid}-${curUser.ezid}-batchexport`
    );
    ensureDir(lightenStoreParentDir);
    const cStore = new LowSync<IUserBatchExportCatalogListStore>(
      new JSONFileSync<IUserBatchExportCatalogListStore>(
        path.join(lightenStoreParentDir, `${encodeURIComponent(key)}.json`)
      ),
      {
        cataList: [],
      }
    );
    cStore.read();
    userBatchExportCatalogListStore.set(key, cStore);
    return cStore;
  }
};
