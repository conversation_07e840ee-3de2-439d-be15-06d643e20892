import ioneUser, { IIoneUser } from '@globalState/user';
import { getAppLocalPath } from '@lightenModule/configFile';
import { LowSync } from 'lowdb';
import { JSONFileSync } from 'lowdb/node';
import path from 'path';
import { ILocalCollectionTask } from '../../lightenModule/localCollectionManager';

export interface IUserLocalCollectionStore {
  pollingList: ILocalCollectionTask[]; // 未返回匹配结果的归集任务
  waitingList: ILocalCollectionTask[]; // 已经返回匹配结果的归集任务
  processingList: ILocalCollectionTask[];
  pauseList: ILocalCollectionTask[];
  completeList: ILocalCollectionTask[];
}

const userLocalCollectionStore = new Map<
  string,
  LowSync<IUserLocalCollectionStore>
>();
const getStorKey = (user: IIoneUser) => `${user.userid}-${user.ezid}`;

/**
 * 存储任务列表
 * @returns
 */
export const getUserLocalCollectionStore = () => {
  const curUser = ioneUser.getUser();

  if (userLocalCollectionStore.has(getStorKey(curUser))) {
    return userLocalCollectionStore.get(getStorKey(curUser));
  } else {
    const lightenStoreDir = path.join(
      getAppLocalPath(),
      'config',
      'upload',
      `${curUser.userid}-${curUser.ezid}-localCollection.json`
    ); // 配置文件根目录
    const cStore = new LowSync<IUserLocalCollectionStore>(
      new JSONFileSync<IUserLocalCollectionStore>(lightenStoreDir),
      {
        pollingList: [],
        pauseList: [],
        waitingList: [],
        processingList: [],
        completeList: [],
      }
    );
    cStore.read();
    userLocalCollectionStore.set(getStorKey(curUser), cStore);
    return cStore;
  }
};
