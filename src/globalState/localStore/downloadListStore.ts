import ioneUser, { IIoneUser } from '@globalState/user';
import { getAppLocalPath } from '@lightenModule/configFile';
import { IDownloadTask } from '@lightenModule/downloadManager';
import { LowSync } from 'lowdb';
import { JSONFileSync } from 'lowdb/node';
import path from 'path';

export interface IUserDownloadStore {
  processingList: IDownloadTask[];
  waitingList: IDownloadTask[];
  pauseList: IDownloadTask[];
  completeList: IDownloadTask[];
}

const userDownloadListStore = new Map<string, LowSync<IUserDownloadStore>>();
const getStorKey = (user: IIoneUser) => `${user.userid}-${user.ezid}`;

export const getUserDownloadListStore = () => {
  const curUser = ioneUser.getUser();

  if (userDownloadListStore.has(getStorKey(curUser))) {
    return userDownloadListStore.get(getStorKey(curUser));
  } else {
    const lightenStoreDir = path.join(
      getAppLocalPath(),
      'config',
      'download',
      `${curUser.userid}-${curUser.ezid}-download.json`
    ); // 配置文件根目录
    const cStore = new LowSync<IUserDownloadStore>(
      new JSONFileSync<IUserDownloadStore>(lightenStoreDir),
      {
        processingList: [],
        waitingList: [],
        pauseList: [],
        completeList: [],
      }
    );
    userDownloadListStore.set(getStorKey(curUser), cStore);
    return cStore;
  }
};
