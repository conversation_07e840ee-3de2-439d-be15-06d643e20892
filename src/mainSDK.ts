/**
 * htElectronSDK的接口，在主进程中的实现处理
 */

import '@htElectronSDK/main/app';
import '@htElectronSDK/main/browserWindow';
import '@htElectronSDK/main/clipboard';
import '@htElectronSDK/main/logicWindow';
import { mainIPC } from '@htElectronSDK/main/ipc';
import '@htElectronSDK/main/log';
import '@htElectronSDK/main/menu';
import { configNetwork } from '@htElectronSDK/main/network';
import '@htElectronSDK/main/shell';
import { initLightenMain } from '@lightenSDK/main';
import { initNetworkListener } from '@lightenSDK/main/net';
import { BrowserWindow } from 'electron';
import { getLibSuffix } from '@utils/openim';
import path from 'node:path';
import OpenIMSDKMain from '@ht/openim-electron-client-sdk';

// 主进程初始化用
export const initMain = (mainWindow: BrowserWindow) => {
  mainIPC.init(mainWindow);
  // 异常网络监听， 可选
  configNetwork();
  // 当前应用的自定义初始化，如果有独有的能力提供给渲染进程，需要自己实现
  initLightenMain();
  initNetworkListener(mainWindow);
  // const libPath = path.join(
  //   __dirname,
  //   '../../node_modules/@ht/openim-electron-client-sdk/assets',
  //   getLibSuffix()
  // );
  // console.log('libPath', libPath);
  // const openIM = new OpenIMSDKMain(libPath, mainWindow.webContents);
  // new OpenIMSDKMain(libPath, mainWindow.webContents);
};
