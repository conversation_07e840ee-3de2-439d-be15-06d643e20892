export const LightenEnv = {
  baseUrl: '',
  baseEitUrl: '',
  htdubboTag: '',
};

if (process.env.SERVER === 'sit') {
  LightenEnv.baseUrl = 'http://eipdev.htsc.com.cn/linkflow/chat';
  LightenEnv.baseEitUrl = 'http://eipdev.htsc.com.cn';
  LightenEnv.htdubboTag = '40300';
} else if (process.env.SERVER === 'uat') {
  // 内网准生产环境
  LightenEnv.baseUrl = 'http://eipsit.htsc.com.cn/linkflow/chat'; // UAT环境
  LightenEnv.baseEitUrl = 'http://eipsit.htsc.com.cn'; // UAT环境
} else if (process.env.SERVER === 'prod') {
  // 生产环境
  LightenEnv.baseUrl = 'http://eip.htsc.com.cn/linkflow/chat';
  LightenEnv.baseEitUrl = 'http://eip.htsc.com.cn';
} else if (process.env.SERVER === 'lite') {
  // eiplite环境
  LightenEnv.baseUrl = 'http://eiplite.htsc.com.cn/linkflow/chat';
  LightenEnv.baseEitUrl = 'http://eiplite.htsc.com.cn';
}

export function changeBaseUrl(vBaseUrl, vBaseEitUrl, vHtdubboTag) {
  if (process.env.NODE_ENV !== 'development') {
    LightenEnv.baseUrl = vBaseUrl;
    LightenEnv.baseEitUrl = vBaseEitUrl;
    LightenEnv.htdubboTag = vHtdubboTag;
  } else {
    // 开发环境只修改tag就行，修改服务器地址，除非重启，不然代理失效，会有跨域问题
    LightenEnv.htdubboTag = vHtdubboTag;
  }
}
