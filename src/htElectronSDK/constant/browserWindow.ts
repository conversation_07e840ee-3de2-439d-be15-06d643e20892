export const BROWSERWINDOW_CREATE = 'browserWindow.create';

export const BROWSERWINDOW_CLOSE = 'browserWindow.close';
export const BROWSERWINDOW_SHOW = 'browserWindow.show';
export const BROWSERWINDOW_HIDE = 'browserWindow.hide';
export const BROWSERWINDOW_RELOAD = 'browserWindow.reload';
export const BROWSERWINDOW_MIN = 'browserWindow.min';
export const BROWSERWINDOW_MAX = 'browserWindow.max';
export const BROWSERWINDOW_TOGGLE_MAXIMIZE = 'browserWindow.toggleMaximize';
export const BROWSERWINDOW_GET_BROWSERWINDOW_STATE =
  'browserWindow.getBrowserWindowState';
export const BROWSERWINDOW_IS_DESTROYED = 'browserWindow.isDestroyed';
export const BROWSERWINDOW_GET_CURRENT_WINDOW_ID =
  'browserWindow.getCurrentWindowId';
export const BROWSERWINDOW_GET_MAIN_WINDOW_ID = 'browserWindow.getMainWindowId';
export const BROWSERWINDOW_GET_POSITION = 'browserWindow.getPosition';
export const BROWSERWINDOW_GET_SIZE = 'browserWindow.getSize';
export const BROWSERWINDOW_SEND_MESSAGE = 'browserWindow.sendMessage';
export const BROWSERWINDOW_SHOW_BROWSWEWIN = 'browserWindow.showBrowserWin';
export const BROWSERWINDOW_CAPTURE_CLICK = 'browserWindow.onCaptureClick';
