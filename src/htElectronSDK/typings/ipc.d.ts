type TIpcMsgCallback = (msg: any) => any;
type TIPCMsgID = string;
type TIPCMsgListenerID = number;
type TIPCListener = {
  callbackId: TIPCMsgListenerID;
  callback: TIpcMsgCallback;
};
/**
 * 'append', 一个事情可以有多个监听者
 * 'cover', 一个事件只会有一个监听者，最后一次添加的监听者会覆盖前面的
 * 'exclusive', 一个事件只会有一个监听者，首次添加监听后，后面添加监听会失败
 */

type TAPPEND_FLAG = 'APPEND';
type TCOVER_FLAG = 'COVER';
type TEXCLUSIVE_FLAG = 'EXCLUSIVE';

type TListenerAddFlag = TAPPEND_FLAG | TCOVER_FLAG | TEXCLUSIVE_FLAG;

interface IIPCMessage {
  id: TIPCMsgID;
  params?: Record<string, any>;
}
