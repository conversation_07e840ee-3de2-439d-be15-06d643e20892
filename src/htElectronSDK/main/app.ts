/**
 * htElectronSDK中app相关属性的实现
 */
import {
  APP_BEFORE_QUIT,
  APP_BROWSER_WINDOW_BLUR,
  APP_BROWSER_WINDOW_FOCUS,
  APP_EXIT,
  APP_FOCUS,
  APP_GET_APP_PATH,
  APP_GET_PATH,
  APP_GET_VERSION,
  APP_QUIT,
  APP_RELAUNCH,
  APP_WILL_QUIT,
  ELECTRON_BEFORE_QUIT,
  ELECTRON_BROWSER_WINDOW_BLUR,
  ELECTRON_BROWSER_WINDOW_FOCUS,
  ELECTRON_QUIT,
  ELECTRON_WILL_QUIT,
} from '@htElectronSDK/constant/app';
import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';
import { app, BrowserWindow } from 'electron';

import { mainIPC } from './ipc';

mainIPC.addListener(
  APP_RELAUNCH,
  ({ arg }) => {
    app.relaunch(
      arg ? { args: process.argv.slice(1).concat([`${arg}`]) } : undefined
    );
    app.exit(0);
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  APP_EXIT,
  ({ exitCode }) => {
    app.exit(exitCode);
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  APP_QUIT,
  () => {
    app.quit();
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  APP_FOCUS,
  () => {
    app.focus();
  },
  EXCLUSIVE_CON
);

// 这里获取electron的版本
mainIPC.addListener(
  APP_GET_VERSION,
  () => {
    return app.getVersion();
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(APP_GET_APP_PATH, () => {
  return app.getAppPath();
});

mainIPC.addListener(
  APP_GET_PATH,
  ({ name }) => {
    return app.getPath(name);
  },
  EXCLUSIVE_CON
);

app.on(ELECTRON_BEFORE_QUIT, (event) => {
  mainIPC.send({ id: APP_BEFORE_QUIT });
});

app.on(ELECTRON_WILL_QUIT, (event) => {
  mainIPC.send({ id: APP_WILL_QUIT });
});

app.on(ELECTRON_QUIT, (event, exitCode) => {
  mainIPC.send({ id: APP_QUIT, params: { exitCode } });
});

app.on(ELECTRON_BROWSER_WINDOW_BLUR, (event, window: BrowserWindow) => {
  mainIPC.send({ id: APP_BROWSER_WINDOW_BLUR, params: { id: window.id } });
});

app.on(ELECTRON_BROWSER_WINDOW_FOCUS, (event, window: BrowserWindow) => {
  mainIPC.send({ id: APP_BROWSER_WINDOW_FOCUS, params: { id: window.id } });
});
