/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
/* eslint-disable max-statements */
/**
 * 子窗口处理
 */
import {
  BROWSERWINDOW_CLOSE,
  BROWSERWINDOW_CREATE,
  BROWSERWINDOW_GET_CURRENT_WINDOW_ID,
  BROWSERWINDOW_GET_MAIN_WINDOW_ID,
  BROWSERWINDOW_GET_POSITION,
  BROWSERWINDOW_GET_SIZE,
  BROWSERWINDOW_HIDE,
  BROWSERWINDOW_RELOAD,
  BROWSERWINDOW_IS_DESTROYED,
  BROWSERWINDOW_MAX,
  BROWSERWINDOW_MIN,
  BROWSERWINDOW_TOGGLE_MAXIMIZE,
  BROWSERWINDOW_GET_BROWSERWINDOW_STATE,
  BROWSERWINDOW_SHOW,
  BROWSERWINDOW_SHOW_BROWSWEWIN,
  BROWSERWINDOW_CAPTURE_CLICK,
} from '@htElectronSDK/constant/browserWindow';
import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';
import { BrowserWindow, ipcMain, webContents } from 'electron';

import { startScreenshot } from '@utils/capture/capturetools';
import { mainIPC } from './ipc';
import logicWindow from './logicWindow';
import LOG from './log';

declare const LINKFLOW_PRELOAD_WEBPACK_ENTRY: string; //

// 创建子窗口
mainIPC.addListener(
  BROWSERWINDOW_CREATE,
  ({ route, options, customOptions }) => {
    const createOptions = {
      ...(options || {}),
      parent: options?.parent
        ? BrowserWindow.fromId(options.parent)
        : undefined, // 渲染进程里传的是窗口ID，要转成对象
      webPreferences: {
        ...(options?.webPreferences || {}),
        nodeIntegration: true, // 是否集成 Nodejs,把之前预加载的js去了，发现也可以运行
        preload: LINKFLOW_PRELOAD_WEBPACK_ENTRY, // Path.join(__dirname, '../renderer/lighten/preload.js'),
      },
    };
    const child = new BrowserWindow(createOptions);

    let childUrl;
    if (route.startsWith('http://') || route.startsWith('https://')) {
      childUrl = route;
    } else if (route.startsWith('/')) {
      const parentWindowWebContents = createOptions.parent
        ? BrowserWindow.fromId(createOptions.parent).webContents
        : mainIPC.getMainWindowWebContents();
      const parentUrl = parentWindowWebContents.getURL(); // 父窗口的url
      if (parentUrl.indexOf('#/')) {
        // hash路由，替换#后面的path
        childUrl = `${parentUrl.substring(0, parentUrl.indexOf('#'))}#${route}`;
      } else {
        //
        const { host } = new URL(parentUrl);
        childUrl = host + route;
      }
    } else {
      LOG.error('browserWindow.create: route传参非法！');
      childUrl = route;
    }
    child.loadURL(childUrl);

    handleBrowserWindowDetail(child, options, customOptions);

    return child.webContents.id;
  },
  EXCLUSIVE_CON
);

const handleBrowserWindowDetail = (
  child: BrowserWindow,
  options,
  customOptions
) => {
  if (!options?.webPreferences?.devTools && process.env.SERVER === 'sit') {
    child.webContents.openDevTools(); // sit包都打开调试器---批注窗口不打开，如需要打开，设置isShowDevtool为false
  }

  if (options.show !== undefined && !options.show) {
    child.webContents.once('did-finish-load', () => {
      child.show();
    });
  }
  if (customOptions?.minimizeWhenBlur) {
    child.on('blur', () => {
      child.minimize();
      // 如果发现没有最小化，则延时200ms再执行一次。
      // 经验证，点击主窗口内容区域（非顶部标题栏）后，子窗口调用最小化无效，需要延时执行才有效果。
      if (!child.isMinimized()) {
        setTimeout(() => {
          if (child && !child.isDestroyed()) {
            child.minimize();
          }
        }, 200);
      }
    });
  }
};

// 获取主窗口ID
mainIPC.addListener(
  BROWSERWINDOW_GET_MAIN_WINDOW_ID,
  () => {
    return mainIPC.getMainWindowWebContents().id;
  },
  EXCLUSIVE_CON
);

// 获取窗口position
mainIPC.addListener(
  BROWSERWINDOW_GET_POSITION,
  ({ id }) => {
    const child = getBrowserWindowByWebContentsId(id);
    if (!child) {
      LOG.error('窗口已销毁！');
      return null;
    }
    const position = child.getPosition();
    return position;
  },
  EXCLUSIVE_CON
);

// 获取窗口size
mainIPC.addListener(
  BROWSERWINDOW_GET_SIZE,
  ({ id }) => {
    const child = getBrowserWindowByWebContentsId(id);
    if (!child) {
      LOG.error('窗口已销毁！');
      return null;
    }
    const size = child.getSize();
    return size;
  },
  EXCLUSIVE_CON
);

// 最小化窗口
mainIPC.addListener(
  BROWSERWINDOW_MIN,
  ({ id }) => {
    const child = getBrowserWindowByWebContentsId(id);
    if (child && !child.isDestroyed()) {
      child.minimize();
    }
  },
  EXCLUSIVE_CON
);

// 最大化窗口
mainIPC.addListener(
  BROWSERWINDOW_MAX,
  ({ id }) => {
    const child = getBrowserWindowByWebContentsId(id);
    if (child && !child.isDestroyed()) {
      child.maximize();
    }
  },
  EXCLUSIVE_CON
);

// 最大化窗口/取消最大化
mainIPC.addListener(
  BROWSERWINDOW_TOGGLE_MAXIMIZE,
  ({ id }) => {
    const child = getBrowserWindowByWebContentsId(id);
    if (child && !child.isDestroyed()) {
      const isMaximized = child.isMaximized();
      if (isMaximized) {
        child.unmaximize();
      } else {
        child.maximize();
      }
    }
  },
  EXCLUSIVE_CON
);

// 获取当前窗口状态
mainIPC.addListener(
  BROWSERWINDOW_GET_BROWSERWINDOW_STATE,
  ({ id }) => {
    const child = getBrowserWindowByWebContentsId(id);
    if (child && !child.isDestroyed()) {
      const state = {
        isMaximized: child.isMaximized(),
        isFullScreen: child.isFullScreen(),
        isMinimized: child.isMinimized(),
      };
      return state;
    } else {
      return {
        isMaximized: false,
        isFullScreen: false,
        isMinimized: true,
      };
    }
  },
  EXCLUSIVE_CON
);

// 关闭子窗口
mainIPC.addListener(
  BROWSERWINDOW_CLOSE,
  ({ id }) => {
    const child = getBrowserWindowByWebContentsId(id);
    if (child && !child.isDestroyed()) {
      child.destroy();
    }
  },
  EXCLUSIVE_CON
);

// 显示子窗口
mainIPC.addListener(
  BROWSERWINDOW_SHOW,
  ({ id }) => {
    const child = getBrowserWindowByWebContentsId(id);
    if (child && !child.isDestroyed()) {
      child.show();
    }
  },
  EXCLUSIVE_CON
);

// 隐藏子窗口
mainIPC.addListener(
  BROWSERWINDOW_HIDE,
  ({ id }) => {
    const child = getBrowserWindowByWebContentsId(id);
    if (child && !child.isDestroyed()) {
      child.hide();
    }
  },
  EXCLUSIVE_CON
);

// 刷新窗口
mainIPC.addListener(
  BROWSERWINDOW_RELOAD,
  ({ id, ignoringCache }) => {
    const child = getBrowserWindowByWebContentsId(id);
    if (child && !child.isDestroyed()) {
      if (ignoringCache) {
        child.webContents.reloadIgnoringCache();
      } else {
        child.webContents.reload();
      }
    }
  },
  EXCLUSIVE_CON
);

// 子窗口是否已销毁
mainIPC.addListener(
  BROWSERWINDOW_IS_DESTROYED,
  ({ id }) => {
    const child = getBrowserWindowByWebContentsId(id);
    if (child && !child.isDestroyed()) {
      return false;
    } else {
      return true;
    }
  },
  EXCLUSIVE_CON
);

// 展示子窗口
mainIPC.addListener(
  BROWSERWINDOW_SHOW_BROWSWEWIN,
  async (props) => {
    let winId = null;
    // 如果类似同一张图片只打开一个窗口 需要传已打开过的imgWinId
    const { imgWinId } = props.params;
    if (imgWinId) {
      const child = getBrowserWindowByWebContentsId(imgWinId);
      if (child && !child.isDestroyed()) {
        child.show();
        winId = imgWinId;
        return;
      }
    }
    if (props.type === 33) {
      const res = await logicWindow.showBrowserWin_Img(props);
      winId = res;
    } else if (props.type === 34) {
      const res = await logicWindow.showBrowserWin_Other(props);
      winId = res;
    }
    return winId;
  },
  EXCLUSIVE_CON
);

// 截图
mainIPC.addListener(
  BROWSERWINDOW_CAPTURE_CLICK,
  async ({ hideWindow }) => {
    if (hideWindow) {
      mainIPC.hide();
      // 防止窗口还没来得及隐藏
      setTimeout(() => {
        startScreenshot(hideWindow);
      }, 150);
    } else {
      startScreenshot(hideWindow);
    }
  },
  EXCLUSIVE_CON
);

// 获取当前窗口ID
ipcMain.on(BROWSERWINDOW_GET_CURRENT_WINDOW_ID, (event) => {
  event.returnValue = event?.sender?.id;
  return event.sender.id;
});

const getBrowserWindowByWebContentsId = (
  id: number
): Electron.BrowserWindow | null => {
  const webcontents = webContents.fromId(id);
  if (webcontents) {
    return BrowserWindow.fromWebContents(webcontents);
  }
  return null;
};
