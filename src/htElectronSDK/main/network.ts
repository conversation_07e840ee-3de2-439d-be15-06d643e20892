/**
 * 网络相关的配置，异常捕获，request拦截处理等
 */
import * as IpcMsgId from '@lightenSDK/constant/global';
import { LightenEnv } from '@common/env';
import { HttpStatusCode } from 'axios';
import { BrowserWindow, net } from 'electron';
import { EventEmitter } from 'events';
import { forceReload } from '@lightenSDK/main/menu';
import { mainIPC } from './ipc';
import LOG from './log';
import packInfo from '../../../package.json';

declare const LINKFLOW_WEBPACK_ENTRY: string; // LINKFLOW_ 是窗口名字

type TNetworkErrCallback = (msg: any) => void;

export const networkErrEvent = new EventEmitter();

const networkErrListenerList: Map<string, string> = new Map<string, string>();

/**
 * 某些url请求失败的临时监听，比如webContents.downloadURL中的无法捕获，可以通过这种方式捕获
 * @param url 需要监听网络报错的url
 * @param callback 网络报错的回调函数
 */
export const addNetworkErrListener = (
  url: string,
  callback: TNetworkErrCallback
) => {
  networkErrListenerList.set(url, url);
  networkErrEvent.once(url, callback);
};

export const configNetwork = () => {
  const renderWindow = BrowserWindow.fromWebContents(
    mainIPC.getMainWindowWebContents()
  );

  // 网络错误捕获
  renderWindow.webContents.session.webRequest.onErrorOccurred((details) => {
    // 2023.11.30 为了弄清楚接口异常中大量的状态码为0的情况；和大量的资源异常,将这些数据通过xlog的自定义异常上报上来
    mainIPC.send({
      id: IpcMsgId.LINKFLOW_CUSTOM_CATCH_UPLOAD,
      params: {
        name: details.url || '',
        message: details.error || '',
        labels: [
          net.isOnline(),
          details.timestamp,
          details.method,
          details.fromCache,
          `mainVersion: ${packInfo.version}`,
        ],
        ext: details?.resourceType,
      },
    });
    if (networkErrListenerList.has(details.url)) {
      networkErrEvent.emit(details.url, details);
      networkErrListenerList.delete(details.url);
    }

    if (
      details.resourceType === 'script' &&
      (details.url.includes('lighten.lhzq.com') ||
        details.url.includes('lighten-renderer.sit.saas.htsc') ||
        details.url.includes('lighten-renderer.uat.saas.htsc'))
    ) {
      // js因为网络原因加载失败会导致简富白屏，尝试重新reload页面
      LOG.info(
        'webRequest.onErrorOccurred',
        details.url,
        details.error,
        net.isOnline(),
        details.method,
        details.fromCache,
        details?.resourceType
      );
      // 清除缓存后重载简富
      forceReload();
      // renderWindow.webContents.session
      //   .clearStorageData({
      //     storages: ['cachestorage', 'filesystem', 'cookies'],
      //   })
      //   .then(() => {
      //     renderWindow.loadURL(`${LINKFLOW_WEBPACK_ENTRY}?retry=true`);
      //   })
      //   .catch((reason) => {
      //     LOG.info('webRequest.onErrorOccurred.catch', reason);
      //     renderWindow.loadURL(`${LINKFLOW_WEBPACK_ENTRY}?retry=true`);
      //   });
    }
  });

  // 404，401等错误捕获
  renderWindow.webContents.session.webRequest.onResponseStarted((details) => {
    if (
      details.statusCode !== HttpStatusCode.Ok &&
      networkErrListenerList.has(details.url)
    ) {
      networkErrEvent.emit(details.url, details);
      networkErrListenerList.delete(details.url);
    }
  });

  // dev或sit环境下设置downloadByUrl的tag
  renderWindow.webContents.session.webRequest.onBeforeRequest(
    (details, callback) => {
      if (
        process.env.SERVER === 'sit' ||
        process.env.NODE_ENV === 'development'
      ) {
        if (
          (details.url.includes('storageGateway/download') ||
            details.url.includes('dueNote/download') ||
            details.url.includes('knowledgeStorage/downloadFile')) &&
          !details.url.includes('htdubboTag') &&
          LightenEnv.htdubboTag
        ) {
          const url = new URL(details.url);
          url.searchParams.set('htdubboTag', LightenEnv.htdubboTag);
          details.url = url.toString();

          // 打印地址
          LOG.info('webContents.downloadURL:', url.toString());
        }
      }
      callback({ cancel: false });
    }
  );
};
