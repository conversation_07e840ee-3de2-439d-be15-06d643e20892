import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';
import {
  MENU_POPUP,
  MENU_POPUP_MENUITEM_CLICK,
} from '@htElectronSDK/constant/menu';
import { Menu, MenuItem } from 'electron';
import { mainIPC } from './ipc';

mainIPC.addListener(
  MENU_POPUP,
  ({ menuItems }) => {
    const menu = new Menu();
    for (const item of menuItems) {
      menu.append(
        new MenuItem({
          ...item,
          click: (menuItem, browserWindow, event) => {
            mainIPC.send({
              id: MENU_POPUP_MENUITEM_CLICK,
              params: { label: menuItem.label },
            });
          },
        })
      );
    }

    menu.popup();
  },
  EXCLUSIVE_CON
);
