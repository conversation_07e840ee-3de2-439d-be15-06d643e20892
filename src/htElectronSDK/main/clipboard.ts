/**
 * clipboard
 */

import {
  CL<PERSON>BOARD_READBOOKMARK,
  CLIP<PERSON>ARD_READHTML,
  CLIPBOARD_READRTF,
  CLIPBOARD_READTEXT,
  CLIPBOARD_WRITEBOOKMARK,
  CLIPBOARD_WRITEHTML,
  CLIPBOARD_WRITERTF,
  CLIPBOARD_WRITETEXT,
} from '@htElectronSDK/constant/clipboard';
import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';
import { clipboard } from 'electron';
import { mainIPC } from './ipc';

mainIPC.addListener(
  CLIPBOARD_READTEXT,
  () => {
    return clipboard.readText();
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  CLIPBOARD_WRITETEXT,
  ({ text }) => {
    clipboard.writeText(text);
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  CLIPBOARD_READHTML,
  () => {
    return clipboard.readHTML();
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  CLIPBOARD_WRITEHTML,
  ({ markup }) => {
    clipboard.writeHTML(markup);
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  CLIPBOARD_READRTF,
  () => {
    return clipboard.readRTF();
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  CLIPBOARD_WRITERTF,
  ({ text }) => {
    clipboard.writeRTF(text);
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  CLIPBOARD_READBOOKMARK,
  () => {
    return clipboard.readBookmark();
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  CLIPBOARD_WRITEBOOKMARK,
  ({ title, url }) => {
    clipboard.writeBookmark(title, url);
  },
  EXCLUSIVE_CON
);
