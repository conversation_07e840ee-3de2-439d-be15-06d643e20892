/**
 * shell
 */
import { getAppLocalPath } from '@lightenModule/configFile';
import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';
import {
  SHELL_OPEN_EXTERNAL,
  SHELL_OPEN_PATH,
  SHELL_SHOWITEMINFOLDER,
  SHELL_SHOWLOGSINFOLDER,
} from '@htElectronSDK/constant/shell';
import { shell } from 'electron';
import path from 'path';
import { mainIPC } from './ipc';

mainIPC.addListener(
  SHELL_OPEN_EXTERNAL,
  ({ url }) => {
    shell.openExternal(url);
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  SHELL_OPEN_PATH,
  async ({ path: vPath }) => {
    return shell.openPath(vPath);
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  SHELL_SHOWITEMINFOLDER,
  ({ fullPath }) => {
    shell.showItemInFolder(fullPath);
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  SHELL_SHOWLOGSINFOLDER,
  () => {
    shell.showItemInFolder(path.join(getAppLocalPath(), 'logs'));
  },
  EXCLUSIVE_CON
);
