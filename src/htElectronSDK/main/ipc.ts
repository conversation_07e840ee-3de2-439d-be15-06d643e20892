/**
 * 和render process通信的消息中心
 */
import {
  APPEND_CON,
  COVER_CON,
  EXCLUSIVE_CON,
  MAIN_TO_RENDER_IPC_CHANNEL,
  RENDER_TO_MAIN_IPC_CHANNEL,
} from '@htElectronSDK/constant/ipc';
import { ipcMain } from 'electron';

const ipcMsgListenerList = new Map<TIPCMsgID, TIPCListener[]>();
let mainWindow: Electron.BrowserWindow;
const childWindowMap = new Map(); // 子窗口列表
const pendingMap = new Map(); // 正在创建的窗口列表

ipcMain.on(
  RENDER_TO_MAIN_IPC_CHANNEL,
  async (event, msg: IIPCMessage, isAsync: boolean) => {
    const list: TIPCListener[] = ipcMsgListenerList.get(msg.id) || [];
    if (!isAsync) {
      // 同步消息只取最后一个绑定的监听者回调
      if (list.length > 0) {
        const ret = list[list.length - 1].callback(msg.params);
        if (ret instanceof Promise) {
          event.returnValue = await ret;
        } else {
          event.returnValue = ret;
        }
      }
    } else {
      list.forEach((item) => {
        item.callback(msg.params);
      });
    }
  }
);

export const mainIPC = {
  // 初始化，ipcMain对象没法主动给render发消息。需要通过webContents才能发消息
  init(browserWindow: Electron.BrowserWindow): void {
    mainWindow = browserWindow;
  },

  // 获取ipc监听的renderwindow
  getMainWindowWebContents(): Electron.WebContents {
    return mainWindow.webContents;
  },

  // 异步发送ipc消息给render进程
  send(msg: IIPCMessage): void {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send(MAIN_TO_RENDER_IPC_CHANNEL, msg);
    }
  },

  show(): void {
    mainWindow.show();
  },

  hide(): void {
    mainWindow.hide();
  },

  getMainWindow(): Electron.BrowserWindow {
    return mainWindow;
  },

  // 监听,返回一个监听者的唯一标识ID，后续可用此ID移除这个监听者
  addListener(
    id: TIPCMsgID,
    callback: TIpcMsgCallback,
    flag: TListenerAddFlag = APPEND_CON
  ): number {
    const listenerId = Date.now();

    if (flag === COVER_CON) {
      ipcMsgListenerList.set(id, [{ callbackId: listenerId, callback }]);
      return listenerId;
    }

    const list: TIPCListener[] = ipcMsgListenerList.get(id) || [];

    if (flag === EXCLUSIVE_CON) {
      if (list.length > 0) {
        return 0;
      } else {
        ipcMsgListenerList.set(id, [{ callbackId: listenerId, callback }]);
        return listenerId;
      }
    }

    list.push({
      callbackId: listenerId,
      callback,
    });
    ipcMsgListenerList.set(id, list);
    return listenerId;
  },

  // 移除监听,如果没有指定listenerId，则移除对该msgid的所有监听者
  removeListener(msgId: TIPCMsgID, listenerId?: TIPCMsgListenerID): void {
    if (!listenerId) {
      ipcMsgListenerList.set(msgId, []);
    } else {
      const list: TIPCListener[] = ipcMsgListenerList.get(msgId) || [];
      ipcMsgListenerList.set(
        msgId,
        list.filter((item) => item.callbackId !== listenerId)
      );
    }
  },
};

export const childIPC = {
  // 所有子窗口Map
  getChildWindowMap() {
    return childWindowMap;
  },
  // 所有正在创建的窗口Map
  getPendingMap() {
    return pendingMap;
  },
  // 修改子窗口Map
  updateChildWindow(
    actionType?: string,
    winType?: number,
    win?: Electron.BrowserWindow
  ) {
    switch (actionType) {
      case 'set':
        childWindowMap.set(winType, win);
        break;
      case 'delete':
        childWindowMap.delete(winType);
        break;
      default:
        break;
    }
  },
};
