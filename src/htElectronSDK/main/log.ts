import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';
import { LOG_ERROR, LOG_INFO, LOG_WARN } from '@htElectronSDK/constant/log';
import { getAppLocalPath } from '@lightenModule/configFile';
import CryptoJS from 'crypto-js';
import log from 'electron-log';
import path from 'path';
import { mainIPC } from './ipc';

let log_secret_key = '';

// 按月新建日志文件（按天新建使用久了日志文件会太多；如果一直只放一个文件里，时间久了文件会太大也不好）
const getLogFileName = () => {
  const date = new Date();
  return `${date.getFullYear()}-${date.getMonth() + 1}.txt`;
};

const encryptLog = (vLog: string) => {
  if (
    process.env.NODE_ENV === 'development' ||
    process.env.SERVER === 'sit' ||
    process.env.SERVER === 'uat'
  ) {
    return vLog;
  } else {
    return CryptoJS.AES.encrypt(vLog, log_secret_key).toString();
  }
};

export const formatLog = (...args: unknown[]) => {
  return args
    .map((item) => {
      let param = '';
      try {
        param = JSON.stringify(item);
      } catch (e) {
        param = param.toString();
      }
      return param;
    })
    .join('|');
};

const LOG = {
  init: (key: string) => {
    log.transports.file.resolvePath = () =>
      path.join(getAppLocalPath(), 'logs', getLogFileName());
    // 只能设置一次
    if (!log_secret_key) {
      log_secret_key = key;
    }
  },
  info: (...args: unknown[]) => {
    log.info(encryptLog(formatLog(args)));
  },
  warn: (...args: unknown[]) => {
    log.warn(encryptLog(formatLog(args)));
  },
  error: (...args: unknown[]) => {
    log.error(encryptLog(formatLog(args)));
  },
};

mainIPC.addListener(
  LOG_INFO,
  ({ args }) => {
    LOG.info(args);
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  LOG_WARN,
  ({ args }) => {
    LOG.warn(args);
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  LOG_ERROR,
  ({ args }) => {
    LOG.error(args);
  },
  EXCLUSIVE_CON
);

export default LOG;
