/**
 * 剪贴板操作
 */

import {
  CLIPBOARD_READBOOKMARK,
  CLIPBOARD_READHTML,
  CLIPBOARD_READRTF,
  CLIPBOARD_READTEXT,
  CLIPBOARD_WRITEBOOKMARK,
  CLIPBOARD_WRITEHTML,
  CLIPBOARD_WRITERTF,
  CLIPBOARD_WRITETEXT,
} from '@htElectronSDK/constant/clipboard';
import { rendererIPC } from './ipc';

const Clipboard = {
  /**
   * 读取剪贴板的纯文本数据
   * @returns:  剪贴板中的纯文本内容
   */
  readText: (): string => {
    return rendererIPC.sendSync({ id: CLIPBOARD_READTEXT });
  },
  /**
   * 写纯文本信息到剪贴板
   * @param text 写到剪贴板的纯文本
   */
  writeText: (text: string): void => {
    rendererIPC.send({ id: CLIPBOARD_WRITETEXT, params: { text } });
  },
  /**
   * 读取剪贴板的html信息
   * @returns:  剪贴板中的内容为纯文本
   */
  readHTML: (): string => {
    return rendererIPC.sendSync({ id: CLIPBOARD_READHTML });
  },
  /**
   * 写html文本信息到剪贴板
   * @param markup 写到剪贴板的html标签文本
   */
  writeHTML: (markup: string): void => {
    rendererIPC.send({ id: CLIPBOARD_WRITEHTML, params: { markup } });
  },
  /**
   * 读取剪贴板的RTF文本
   * @returns:  剪贴板中的内容为RTF
   */
  readRTF: (): string => {
    return rendererIPC.sendSync({ id: CLIPBOARD_READRTF });
  },
  /**
   * 向剪贴板中写入RTF格式的文本
   * @param text  写到剪贴板的RTF格式文本
   */
  writeRTF: (text: string): void => {
    rendererIPC.send({ id: CLIPBOARD_WRITERTF, params: { text } });
  },
  /**
   * 读取剪贴板的书签文本
   * @returns:  返回一个对象, 其中包含表示剪贴板中书签 title 和 url 。 当书签不可用时, title 和 url 值将为空字符串。 Windows上的 title 值将永远是空的。
   */
  readBookmark: (): { title: string; url: string } => {
    return rendererIPC.sendSync({ id: CLIPBOARD_READBOOKMARK });
  },
  /**
   * 向剪贴板中写书签
   * @param title  Windows 未使用
   * @param url
   */
  writeBookmark: (title: string, url: string): void => {
    rendererIPC.send({ id: CLIPBOARD_WRITEBOOKMARK, params: { title, url } });
  },
};

export default Clipboard;
