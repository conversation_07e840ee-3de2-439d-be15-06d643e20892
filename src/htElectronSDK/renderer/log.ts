/**
 * 日志能力: 调试时，会输出到devtools的控制台和vscode的控制台中。非调试态会同时输出到控制台和本地日志文件中
 */

import { LOG_ERROR, LOG_INFO, LOG_WARN } from '@htElectronSDK/constant/log';
import { rendererIPC } from './ipc';

export const formatLog = (...args: unknown[]) => {
  return args
    .map((item) => {
      let param = '';
      try {
        param = JSON.stringify(item);
      } catch (e) {
        param = param.toString();
      }
      return param;
    })
    .join('|');
};

const LOG = {
  info(...args: any[]) {
    rendererIPC.send({
      id: LOG_INFO,
      params: { args: formatLog('renderer', args) },
    });
  },
  warn(...args: any[]) {
    rendererIPC.send({
      id: LOG_WARN,
      params: { params: { args: formatLog('renderer', args) } },
    });
  },
  error(...args: any[]) {
    rendererIPC.send({
      id: LOG_ERROR,
      params: { params: { args: formatLog('renderer', args) } },
    });
  },
};
export default LOG;
