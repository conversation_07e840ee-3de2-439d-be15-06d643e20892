/**
 * 弹出系统菜单
 */

import {
  MENU_POPUP,
  MENU_POPUP_MENUITEM_CLICK,
} from '@htElectronSDK/constant/menu';
import { COVER_CON } from '@htElectronSDK/constant/ipc';
import { MenuItem } from 'electron';
import { rendererIPC } from './ipc';
import LOG from './log';

// 菜单项定义（只暴露了一部分可能会用到的属性，更多属性定义参看官网文档)
type clickCallback = () => void;

const Menu = {
  /**
   * 在当前鼠标位置弹出系统菜单
   * @param menuItems：菜单项集合
   */
  popup(menuItems: MenuItem[]): void {
    const itemsMap = new Map<string, clickCallback>();
    const process = (items: MenuItem[]) => {
      const ret = [];
      for (const item of items) {
        if (itemsMap.has(item.label)) {
          LOG.error('Menu.popup 有重复ID!!!', item.label);
        }
        itemsMap.set(item.label, item.click as clickCallback);
        ret.push({
          ...item,
          click: undefined,
          submenu: item.submenu
            ? process(item.submenu as unknown as MenuItem[])
            : undefined,
        });
      }
      return ret;
    };
    const newItems: Electron.MenuItemConstructorOptions[] = process(menuItems);
    const listenerId = rendererIPC.addListener(
      MENU_POPUP_MENUITEM_CLICK,
      ({ label }) => {
        const onClick = itemsMap.get(label);
        if (onClick) {
          onClick();
          rendererIPC.removeListener(MENU_POPUP_MENUITEM_CLICK, listenerId);
        }
      },
      COVER_CON
    );
    rendererIPC.send({
      id: MENU_POPUP,
      params: {
        menuItems: newItems, // 传给主进程时需要把回调函数去掉
      },
    });
  },
};

export default Menu;
