/**
 * shell相关操作
 */

import {
  SHELL_OPEN_EXTERNAL,
  SHELL_OPEN_PATH,
  SHELL_SHOWITEMINFOLDER,
  SHELL_SHOWLOGSINFOLDER,
} from '@htElectronSDK/constant/shell';
import { rendererIPC } from './ipc';

const Shell = {
  /**
   * 以桌面的默认方式打开给定的文件。 (例如，mailto：用户默认邮件代理中的URL)。
   * @param url ：要打开的链接, windows平台最大2081字节
   */
  openExternal: (url: string): void => {
    rendererIPC.send({ id: SHELL_OPEN_EXTERNAL, params: { url } });
  },

  /**
   * 以桌面的默认方式打开给定的文件(老版本的openItem)
   * @param path: 文件路径
   */
  openPath: (path: string): string => {
    return rendererIPC.sendSync({ id: SHELL_OPEN_PATH, params: { path } });
  },

  /**
   * 在文件管理器中显示指定的文件。 如果可能，请选择文件
   * @param fullPath: 文件路径
   */
  showItemInFolder: (fullPath: string): void => {
    rendererIPC.send({ id: SHELL_SHOWITEMINFOLDER, params: { fullPath } });
  },

  /**
   * 在文件管理器中打开日志所在文件夹。
   */
  showLogsInFolder: () => {
    rendererIPC.send({ id: SHELL_SHOWLOGSINFOLDER });
  },
};
export default Shell;
