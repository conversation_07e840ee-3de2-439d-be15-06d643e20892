/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
/**
 * 浏览器窗口的相关操作
 */
import {
  BROWSERWINDOW_CLOSE,
  BROWSERWINDOW_CREATE,
  BROWSERWINDOW_GET_CURRENT_WINDOW_ID,
  BROWSERWINDOW_GET_MAIN_WINDOW_ID,
  BROWSERWINDOW_GET_POSITION,
  BROWSERWINDOW_GET_SIZE,
  BROWSERWINDOW_HIDE,
  BROWSERWINDOW_RELOAD,
  BROWSERWINDOW_IS_DESTROYED,
  BROWSERWINDOW_MAX,
  BROWSERWINDOW_MIN,
  BROWSERWINDOW_TOGGLE_MAXIMIZE,
  BROWSERWINDOW_GET_BROWSERWINDOW_STATE,
  BROWSERWINDOW_SEND_MESSAGE,
  BROWSERWINDOW_SHOW,
  BROWSERWINDOW_SHOW_BROWSWEWIN,
  BROWSERWINDOW_CAPTURE_CLICK,
} from '@htElectronSDK/constant/browserWindow';
import { ipc<PERSON><PERSON><PERSON> } from 'electron';
import fse from 'fs-extra';
import path from 'path';
import crypto from 'crypto';

import { rendererIPC } from './ipc';
import App from './app';

// // 窗口标识
type WindowId = number;
type Message = { msgId: string; params?: any };
type MessageCallback = (msg: Message) => void;
type MessageListener = { callbackId: number; callback: MessageCallback };
const messageListenerList = new Map<WindowId, MessageListener[]>();

ipcRenderer.on(BROWSERWINDOW_SEND_MESSAGE, (event, params: any) => {
  // const srcId = event.senderId;
  // const list = messageListenerList.get(srcId) || [];
  // list.forEach((item) => {
  //   const { callback } = item;
  //   callback(params);
  // });
});

const BrowserWindow = {
  /**
   * 创建一个子窗口，返回值为创建的子窗口的ID
   *
   * @param route :窗口的url，也可以是以/开头的路由路径，比如： /project，此时若当前窗口url含有#/,则将拼接在#后面，否则拼接在origin后
   * @param options :创建窗口的参数，除了枚举的常用的一些参数外，其余更多定制化参数见官网文档：https://www.electronjs.org/zh/docs/latest/api/browser-window#new-browserwindowoptions
   *
   *
   *
   */
  create(
    route: string,
    options: Electron.BrowserWindowConstructorOptions,
    customOptions: any
  ): WindowId {
    const webContentsId: WindowId = rendererIPC.sendSync({
      id: BROWSERWINDOW_CREATE,
      params: {
        route,
        options,
        customOptions,
      },
    });
    return webContentsId;
  },

  /**
   * 关闭一个窗口
   *
   * @param id 要关闭的窗口id
   */
  close(id: WindowId): void {
    rendererIPC.send({ id: BROWSERWINDOW_CLOSE, params: { id } });
    // 20230202: 关闭子窗口时，移除窗口的所有监听
    BrowserWindow.removeListener(id);
  },

  /**
   * 显示一个窗口
   *
   * @param id 要显示的窗口id
   */
  show(id: WindowId): void {
    rendererIPC.send({ id: BROWSERWINDOW_SHOW, params: { id } });
  },

  /**
   * 隐藏一个窗口
   *
   * @param id 要隐藏的窗口id
   */
  hide(id: WindowId): void {
    rendererIPC.send({ id: BROWSERWINDOW_HIDE, params: { id } });
  },

  /**
   * 刷新一个窗口
   *
   * @param id 要刷新的窗口id
   * @param ignoringCache 是否忽略缓存强制刷新，默认否
   */
  reload(id: WindowId, ignoringCache = false): void {
    rendererIPC.send({
      id: BROWSERWINDOW_RELOAD,
      params: { id, ignoringCache },
    });
  },

  /**
   * 最小化一个窗口
   *
   * @param id 要最小化的窗口id
   */
  min(id: WindowId): void {
    rendererIPC.send({ id: BROWSERWINDOW_MIN, params: { id } });
  },

  /**
   * 最大化一个窗口
   *
   * @param id 要最大化的窗口id
   */
  max(id: WindowId): void {
    rendererIPC.send({ id: BROWSERWINDOW_MAX, params: { id } });
  },

  /**
   * 最大化窗口/取消最大化
   * @param id 要最大化窗口/取消最大化的窗口id
   */
  toggleMaximize(id: WindowId): void {
    rendererIPC.send({ id: BROWSERWINDOW_TOGGLE_MAXIMIZE, params: { id } });
  },

  /**
   * 获取当前窗口状态
   * @param id
   */
  getBrowserWindowState(id: WindowId): void {
    const browserWindowState = rendererIPC.sendSync({
      id: BROWSERWINDOW_GET_BROWSERWINDOW_STATE,
      params: { id },
    });
    return browserWindowState;
  },

  /**
   * 窗口是否已销毁
   *
   * @param id 窗口id
   */
  isDestroyed(id: WindowId): boolean {
    const isDestroy = rendererIPC.sendSync({
      id: BROWSERWINDOW_IS_DESTROYED,
      params: { id },
    });
    return isDestroy;
  },

  /**
   * 获取当前窗口的id
   * @returns 当前窗口的id
   */
  getCurrentWindowId(): WindowId {
    const currentWindowId = ipcRenderer.sendSync(
      BROWSERWINDOW_GET_CURRENT_WINDOW_ID
    );
    return currentWindowId;
  },

  /**
   * 获取主窗口的id
   * @returns 主窗口的id
   */
  getMainWindowId(): WindowId {
    const mainWindowId = rendererIPC.sendSync({
      id: BROWSERWINDOW_GET_MAIN_WINDOW_ID,
    });
    return mainWindowId;
  },

  /**
   * 获取窗口的position
   * @param id 窗口id
   * @returns 窗口的position
   */
  geWindowPosition(id: WindowId): number[] {
    const position = rendererIPC.sendSync({
      id: BROWSERWINDOW_GET_POSITION,
      params: { id },
    });
    return position;
  },

  /**
   * 获取窗口的size
   * @param id 窗口id
   * @returns 窗口的size
   */
  getWindowSize(id: WindowId): number[] {
    const mainWindowId = rendererIPC.sendSync({
      id: BROWSERWINDOW_GET_SIZE,
      params: { id },
    });
    return mainWindowId;
  },

  /**
   * 给一个窗口发消息(异步)
   *
   * @param id 要最大化的窗口id
   * @param params 要发送的消息内容
   */
  sendMessage(id: WindowId, msg: { msgId: string; params?: any }): void {
    // ipcRenderer.sendTo(id, BROWSERWINDOW_SEND_MESSAGE, msg);
  },

  /**
   * 监听一个窗口发过来的消息（返回值为监听者ID，移除监听时需要用到）
   *
   * @param id 要监听的窗口id
   * @param onMessage 消息回调
   */
  addListener(
    id: WindowId,
    onMessage: (msg: { msgId: string; params?: any }) => void
  ): number {
    const listenerId = Date.now();
    const list: MessageListener[] = messageListenerList.get(id) || [];
    list.push({ callbackId: listenerId, callback: onMessage });
    messageListenerList.set(id, list);
    return listenerId;
  },

  /**
   * 移除窗口消息监听
   * @param id 要移除监听的窗口id
   * @param listenerId 要移除的监听者id
   */
  removeListener(id: WindowId, listenerId?: number): void {
    let list: MessageListener[] = messageListenerList.get(id) || [];
    // 20230202: 1.传listenerId则筛选list，2.不传listenerId则清空list
    if (listenerId) {
      list = list.filter((item) => item.callbackId !== listenerId);
    }
    if (list.length > 0) {
      messageListenerList.set(id, list);
    } else {
      messageListenerList.delete(id);
    }
  },

  /**
   * 获取文件缓存地址
   * @param url 文件地址
   */
  async imageLocalUrl(url: string) {
    const generateFilename = (imageUrl: string) => {
      const urlWithoutQuery = imageUrl.split('?')[0]?.split('#')[0] || imageUrl;
      const basename = path.basename(urlWithoutQuery);
      const extMatch = /\.([a-z0-9]+)$/i.exec(basename);
      const ext = extMatch ? extMatch[1] : 'png';

      const hash = crypto
        .createHash('md5')
        .update(imageUrl)
        .digest('hex')
        .slice(0, 8);

      return `${hash}.${ext}`;
    };

    const cacheDir = path.join(App.getAppPath(), 'image_cache');
    await fse.mkdir(cacheDir, { recursive: true });
    fse.ensureDirSync(cacheDir);
    // 生成唯一文件名
    const filename = generateFilename(url);

    const filepath = path.join(cacheDir, filename);
    return filepath;
  },

  /**
   * 将图片写入本地文件夹
   * @param filepath 文件缓存地址
   * @param data 图片数据
   */
  async writeImageLocal(filepath: string, data: Buffer) {
    await fse.writeFile(filepath, data);
  },

  /**
   * 判断本地是否有该图片/文件
   * @param filepath 文件缓存地址
   */
  existsFileLocal(filepath: string) {
    return fse.existsSync(filepath);
  },

  /**
   * 从本地文件读取数据
   * @param filepath 文件缓存地址
   */
  async readFileLocal(filepath: string) {
    const res = await fse.readFile(filepath);
    return res;
  },

  /**
   * 展示子窗口
   */
  async showBrowserWin(props: {
    type: number;
    title: string;
    option?: any;
    params?: any;
  }) {
    const winId = await rendererIPC.sendSync({
      id: BROWSERWINDOW_SHOW_BROWSWEWIN,
      params: props,
    });
    return winId;
  },

  // 子窗口监听图片更新
  onUpdateImage: (callback) => {
    ipcRenderer.on('update-image', (event, imageName) => callback(imageName));
  },

  // 子窗口监听页面类型改变
  onUpdateBrowserWin: (callback) => {
    ipcRenderer.on('update-browserWin', (event, param) => {
      const { type, title, params } = param;
      callback(type, title, params);
    });
  },

  // 点击截图按钮
  onCaptureClick: (hideWindow) => {
    rendererIPC.send({
      id: BROWSERWINDOW_CAPTURE_CLICK,
      params: { hideWindow },
    });
  },

  // 子窗口监听截图返回的图片
  onUpdateCaptureImage: (callback) => {
    ipcRenderer.on('update-capture-image', (event, imageData, imagePath) =>
      callback(imageData, imagePath)
    );
  },

  // tip提示触发
  onMessageTipShow: (callback) => {
    ipcRenderer.on('show-message-tip', (event, tipType, tipInfo) =>
      callback(tipType, tipInfo)
    );
  },
};

export default BrowserWindow;
