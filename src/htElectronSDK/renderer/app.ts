/**
 * 暴露给渲染进程的app相关的能力
 */

import {
  APP_BROWSER_WINDOW_BLUR,
  APP_BROWSER_WINDOW_FOCUS,
  APP_EXIT,
  APP_FOCUS,
  APP_GET_APP_PATH,
  APP_GET_PATH,
  APP_GET_VERSION,
  APP_QUIT,
  APP_RELAUNCH,
} from '@htElectronSDK/constant/app';
import { rendererIPC } from './ipc';

const App = {
  /**
   * 重启应用
   * @param args ：重启时的命令行参数，缺省时使用当前相同的命令行参数
   */
  relaunch: (args?: string): void => {
    rendererIPC.send({ id: APP_RELAUNCH, params: { args } });
  },

  /**
   * 立即退出进程
   * @param exitCode: 进程退出码，默认为0,
   */
  exit: (exitCode?: number): void => {
    rendererIPC.send({ id: APP_EXIT, params: { exitCode } });
  },

  /**
   * 尝试退出进程
   * 将首先发出 before-quit 事件。 如果所有窗口都已成功关闭, 则将发出 will-quit 事件, 并且默认情况下应用程序将终止。
   * 此方法会确保执行所有beforeunload 和 unload事件处理程序。可以在退出窗口之前的beforeunload事件处理程序中返回false取消退出
   */
  quit: (): void => {
    rendererIPC.send({ id: APP_QUIT });
  },

  /**
   * 使第一个可见窗口获得焦点
   * 在 Linux 上，使第一个可见窗口获得焦点。 在 macOS上，将应用程序变成激活的app。 在 Windows上，使应用程序的第一个窗口获得焦点。
   */
  focus: (): void => {
    rendererIPC.send({ id: APP_FOCUS });
  },

  /**
   * 获取应用程序的版本号
   * @returns：这里返回的是electron的版本号
   */
  getVersion: (): string => {
    const appVersion = rendererIPC.sendSync({ id: APP_GET_VERSION });
    return appVersion;
  },

  /**
   * 获取当前应用程序目录
   * @returns：当前应用程序目录
   */
  getAppPath: (): string => {
    const appPath = rendererIPC.sendSync({ id: APP_GET_APP_PATH });
    return appPath;
  },

  /**
   * 获取用户下载目录的路径
   * @returns： 用户下载目录的路径
   */
  getDownloadsPath: (): string => {
    const downloadsPath = rendererIPC.sendSync({
      id: APP_GET_PATH,
      params: { name: 'downloads' },
    });
    return downloadsPath;
  },

  /**
   * 获取当前用户的桌面文件夹路径
   * @returns： 当前用户的桌面文件夹
   */
  getDesktopPath: (): string => {
    const desktopPath = rendererIPC.sendSync({
      id: APP_GET_PATH,
      params: { name: 'desktop' },
    });
    return desktopPath;
  },

  /**
   * 程序退出时关闭窗口前事件
   * @param callback：事件的回调函数：返回true 会正常关闭窗口；返回false将阻止应用程序的默认行为
   * 注：在 Windows 系统中，如果应用程序因系统关机/重启或用户注销而关闭，那么这个事件不会被触发。
   */
  // onBeforeQuit: (callback: () => boolean): void => {
  //   rendererIPC.addListener('app.before-quit', () => {
  //     callback();
  //   });
  // },

  /**
   * 程序退出时关闭窗口后事件
   * @param callback：事件的回调函数：返回true 会正常退出；返回false将阻止应用程序的退出行为
   * 注：在 Windows 系统中，如果应用程序因系统关机/重启或用户注销而关闭，那么这个事件不会被触发。
   */
  // onWilQuit: (callback: () => boolean): void => {
  //   rendererIPC.addListener('app.will-quit', () => {
  //     callback();
  //   });
  // },

  /**
   * 程序退出时事件
   * @param callback：事件的回调函数：exitCode: 进程退出码
   * 注：在 Windows 系统中，如果应用程序因系统关机/重启或用户注销而关闭，那么这个事件不会被触发。
   */
  // onQuit: (callback: (exitCode: number) => void): void => {
  //   rendererIPC.addListener('app.quit', ({ exitCode }) => {
  //     callback(exitCode);
  //   });
  // },

  /**
   * 窗口失焦时事件
   * @param callback：事件的回调函数：id: 失焦的窗口id
   *
   */
  onBlur: (callback: (id: number) => void): void => {
    rendererIPC.addListener(APP_BROWSER_WINDOW_BLUR, ({ id }) => {
      callback(id);
    });
  },

  /**
   * 窗口获得焦点时事件
   * @param callback：事件的回调函数：id: 获得焦点的窗口id
   *
   */
  onFocus: (callback: (id: number) => void): void => {
    rendererIPC.addListener(APP_BROWSER_WINDOW_FOCUS, ({ id }) => {
      callback(id);
    });
  },
};
export default App;
