/**
 * 和主进程通信的消息中心
 */

import {
  APPEND_CON,
  COVER_CON,
  EXCLUSIVE_CON,
  MAIN_TO_RENDER_IPC_CHANNEL,
  RENDER_TO_MAIN_IPC_CHANNEL,
} from '@htElectronSDK/constant/ipc';
import { ipcRenderer } from 'electron';

const ipcMsgListenerList = new Map<TIPCMsgID, TIPCListener[]>();

ipcRenderer.on(MAIN_TO_RENDER_IPC_CHANNEL, (event, msg: IIPCMessage) => {
  const list: TIPCListener[] = ipcMsgListenerList.get(msg.id) || [];
  list.forEach((item) => {
    item.callback(msg.params);
  });
});

export const rendererIPC = {
  // 异步发送ipc消息给主进程
  send(msg: IIPCMessage): void {
    ipcRenderer.send(RENDER_TO_MAIN_IPC_CHANNEL, msg, true);
  },

  // 同步发送ipc消息给主进程
  sendSync(msg: IIPCMessage): any {
    return ipcRenderer.sendSync(RENDER_TO_MAIN_IPC_CHANNEL, msg, false);
  },

  // 监听,返回一个监听者的唯一标识ID，后续可用此ID移除这个监听者
  addListener(
    id: TIPCMsgID,
    callback: TIpcMsgCallback,
    flag: TListenerAddFlag = APPEND_CON
  ): number {
    const listenerId = Date.now();

    if (flag === COVER_CON) {
      ipcMsgListenerList.set(id, [{ callbackId: listenerId, callback }]);
      return listenerId;
    }

    const list: TIPCListener[] = ipcMsgListenerList.get(id) || [];

    if (flag === EXCLUSIVE_CON) {
      if (list.length > 0) {
        return 0;
      } else {
        ipcMsgListenerList.set(id, [{ callbackId: listenerId, callback }]);
        return listenerId;
      }
    }

    list.push({
      callbackId: listenerId,
      callback,
    });
    ipcMsgListenerList.set(id, list);
    return listenerId;
  },

  // 移除监听,如果没有指定listenerId，则移除对该msgid的所有监听者
  removeListener(msgId: TIPCMsgID, listenerId?: TIPCMsgListenerID): void {
    if (!listenerId) {
      ipcMsgListenerList.set(msgId, []);
    } else {
      const list: TIPCListener[] = ipcMsgListenerList.get(msgId) || [];
      ipcMsgListenerList.set(
        msgId,
        list.filter((item) => item.callbackId !== listenerId)
      );
    }
  },
};
