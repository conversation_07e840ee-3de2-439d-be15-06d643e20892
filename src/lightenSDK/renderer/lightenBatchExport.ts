import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';
import { rendererIPC } from '@htElectronSDK/renderer/ipc';
import {
  LINKFLOW_ADD_BATCH_EXPORT,
  LINKFLOW_BATCH_EXPORT_UPDATE_STATUS,
  LINKFLOW_CANCEL_BATCH_EXPORT,
  LINKFLOW_CONTINUE_BATCH_EXPORT,
  LINKFLOW_GET_BATCH_EXPORT_CATALOGLIST_DETAIL,
  LINKFLOW_GET_BATCH_EXPORT_DETAIL,
  LINKFLOW_GET_BATCH_EXPORT_INFO,
  LINKFLOW_ON_BATCH_COMPLETE,
  LINKFLOW_PAUSE_BATCH_EXPORT,
  LINKFLOW_RESTART_BATCH_EXPORT,
  LINKFLOW_UPDATE_PROJECT_NAME,
} from '@lightenSDK/constant/batchExport';

export const lightenBatchExport = {
  addBatchExport: (params) => {
    rendererIPC.send({
      id: LINKFLOW_ADD_BATCH_EXPORT,
      params,
    });
  },
  pauseBatchExport: (params) => {
    rendererIPC.send({
      id: LINKFLOW_PAUSE_BATCH_EXPORT,
      params,
    });
  },
  continueBatchExport: (params) => {
    rendererIPC.send({
      id: LINKFLOW_CONTINUE_BATCH_EXPORT,
      params,
    });
  },
  cancelBatchExport: (params) => {
    rendererIPC.send({
      id: LINKFLOW_CANCEL_BATCH_EXPORT,
      params,
    });
  },
  restartBatchExport: (params) => {
    rendererIPC.send({
      id: LINKFLOW_RESTART_BATCH_EXPORT,
      params,
    });
  },
  getBatchExport: () => {
    return rendererIPC.sendSync({
      id: LINKFLOW_GET_BATCH_EXPORT_INFO,
    });
  },
  getBatchExportDetail: (params) => {
    return rendererIPC.sendSync({
      id: LINKFLOW_GET_BATCH_EXPORT_DETAIL,
      params,
    });
  },
  getBatchExportTaskCatalogListDetail: (params) => {
    return rendererIPC.sendSync({
      id: LINKFLOW_GET_BATCH_EXPORT_CATALOGLIST_DETAIL,
      params,
    });
  },
  onBatchExportUpdateStatus: (callback) => {
    const lId = rendererIPC.addListener(
      LINKFLOW_BATCH_EXPORT_UPDATE_STATUS,
      (msg) => {
        callback?.(msg);
      }
    );
    return lId;
  },
  removeListenBEUpdateStatus: (lId) => {
    rendererIPC.removeListener(LINKFLOW_BATCH_EXPORT_UPDATE_STATUS, lId);
  },

  onBatchComplete: (callback) => {
    const lId = rendererIPC.addListener(
      LINKFLOW_ON_BATCH_COMPLETE,
      (msg) => {
        callback?.(msg);
      },
      EXCLUSIVE_CON
    );
    return lId;
  },
  removeBatchCompleteListener: (lId) => {
    rendererIPC.removeListener(LINKFLOW_ON_BATCH_COMPLETE, lId);
  },
  updateProjectName: (params) => {
    return rendererIPC.sendSync({
      id: LINKFLOW_UPDATE_PROJECT_NAME,
      params,
    });
  },
};
