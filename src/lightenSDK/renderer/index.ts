// eslint-disable-next-line eslint-comments/disable-enable-pair
/* eslint-disable max-lines */
/**
 * 简富独有的SDK,暴露给渲染进程用
 */
import { SUCCESS_CON } from '@common/constant';
import {
  APPEND_CON,
  COVER_CON,
  EXCLUSIVE_CON,
} from '@htElectronSDK/constant/ipc';
import { rendererIPC } from '@htElectronSDK/renderer/ipc';
import {
  LINKFLOW_CATCH_UPLOAD,
  LINKFLOW_CUSTOM_CATCH_UPLOAD,
  LINKFLOW_CHANGE_SERVER,
  LINKFLOW_FILE_UTIL_CREATE_DIR,
  LINKFLOW_FILE_UTIL_DELETE_DIR_SYNC,
  LINKFLOW_FILE_UTIL_DELETE_UNTIL_SUCCESS,
  LINKFLOW_FILE_UTIL_DELETE_UNTIL_SUCCESS_CALLBACK,
  LINKFLOW_FILE_UTIL_IS_FILE_CHANGED_WHEN_CLOSED,
  LINKFLOW_FILE_UTIL_IS_FILE_CHANGED_WHEN_CLOSED_CALLBACK,
  LINKFLOW_FILE_UTIL_IS_FILE_SIZE_EXCEEDED,
  LINKFLOW_FILE_UTIL_MKDir_Temp,
  LINKFLOW_FILE_UTIL_SAVE_FILE,
  LINKFLOW_LOGIN_SUCCESS,
  LINKFLOW_LOGOUT_SUCCESS,
  LINKFLOW_MESSAGE_ERROR,
  LINKFLOW_MESSAGE_INFO,
  LINKFLOW_MESSAGE_SUCCESS,
  LINKFLOW_MESSAGE_WARN,
  LINKFLOW_ON_CLOSE_RENAME_MODAL,
  LINKFLOW_ON_INIT_TO_RENDERER,
  LINKFLOW_ON_REDIRECT_LOGIN_PAGE,
  LINKFLOW_ON_SHOW_MODAL,
  LINKFLOW_ON_SHOW_NOTIFICATION,
  LINKFLOW_ON_SHOW_RENAME_MODAL,
  LINKFLOW_ON_WAKE_UP_RENDERER_WITH_USER,
  LINKFLOW_OPEN_EASY_BOARD,
  LINKFLOW_SEND_INIT_TO_MAIN,
  LINKFLOW_SET_AGENT_CONFIG,
  LINKFLOW_TRANSFER_LIST_INFO,
  LINKFLOW_OPEN_DEV_TOOLS,
  LINKFLOW_RELOADAPP,
  LINKFLOW_LOG,
  LINKFLOW_GET_VERSION,
  LINKFLOW_GET_IP,
  LINKFLOW_CLEARCACHE,
} from '@lightenSDK/constant/global';
import { getFileOrDirInfo, isFileExist } from '@utils/folderUtils';
import { lightenUpdater } from './autoUpdater';
import { lightenBatchExport } from './lightenBatchExport';
import { lightenLocalCollection } from './lightenLocalCollection';
import { lightenDownload } from './lightenDownload';
import { lightenUpload } from './lightenUpload';
import { lightenCrossStageCopy } from './lightenCrossStageCopy';

const Lighten = {
  message: {
    onError: (callback: (msg: string) => void): void => {
      rendererIPC.addListener(
        LINKFLOW_MESSAGE_ERROR,
        ({ text }) => {
          callback?.(text);
        },
        EXCLUSIVE_CON
      );
    },
    onInfo: (callback: (msg: string) => void): void => {
      rendererIPC.addListener(
        LINKFLOW_MESSAGE_INFO,
        ({ text }) => {
          callback?.(text);
        },
        EXCLUSIVE_CON
      );
    },
    onWarn: (callback: (msg: string) => void): void => {
      rendererIPC.addListener(
        LINKFLOW_MESSAGE_WARN,
        ({ text }) => {
          callback?.(text);
        },
        EXCLUSIVE_CON
      );
    },
    onSuccess: (callback: (msg: string) => void): void => {
      rendererIPC.addListener(
        LINKFLOW_MESSAGE_SUCCESS,
        ({ text }) => {
          callback?.(text);
        },
        EXCLUSIVE_CON
      );
    },
    removeMessageListeners: () => {
      rendererIPC.removeListener(LINKFLOW_MESSAGE_ERROR);
      rendererIPC.removeListener(LINKFLOW_MESSAGE_INFO);
      rendererIPC.removeListener(LINKFLOW_MESSAGE_WARN);
      rendererIPC.removeListener(LINKFLOW_MESSAGE_SUCCESS);
    },
  },

  fileUtil: {
    deleteDirSync: (dir: string): boolean => {
      return rendererIPC.sendSync({
        id: LINKFLOW_FILE_UTIL_DELETE_DIR_SYNC,
        params: { dir },
      });
    },
    createDirSync: (dir: string): boolean => {
      return rendererIPC.sendSync({
        id: LINKFLOW_FILE_UTIL_CREATE_DIR,
        params: { dir },
      });
    },

    isMaxFileSizeExceeded: (files: string | string[]): boolean => {
      return rendererIPC.sendSync({
        id: LINKFLOW_FILE_UTIL_IS_FILE_SIZE_EXCEEDED,
        params: { files },
      });
    },
    mkdirTemp: () => {
      return rendererIPC.sendSync({
        id: LINKFLOW_FILE_UTIL_MKDir_Temp,
      });
    },
    pathIsExistsSync: (path) => {
      const accessInfo = isFileExist(path);
      if (accessInfo === SUCCESS_CON) {
        return true;
      } else {
        return false;
      }
    },
    getFileInfoFromSys: async (path) => {
      const fileInfoRes = await getFileOrDirInfo(path);
      if (fileInfoRes.code === SUCCESS_CON) {
        return fileInfoRes.file;
      } else {
        return fileInfoRes;
      }
    },
    deleteFileAndFolderUntilSucceed: (parentPath: string): Promise<void> => {
      return new Promise((resolve) => {
        const listenerId = rendererIPC.addListener(
          LINKFLOW_FILE_UTIL_DELETE_UNTIL_SUCCESS_CALLBACK,
          (msg) => {
            rendererIPC.removeListener(
              LINKFLOW_FILE_UTIL_DELETE_UNTIL_SUCCESS_CALLBACK,
              listenerId
            );
            resolve();
          }
        );

        rendererIPC.sendSync({
          id: LINKFLOW_FILE_UTIL_DELETE_UNTIL_SUCCESS,
          params: { parentPath },
        });
      });
    },
    isFileChangedWhenClosed(filePath: string, mtimeMs, callback): void {
      const listenerId = rendererIPC.addListener(
        LINKFLOW_FILE_UTIL_IS_FILE_CHANGED_WHEN_CLOSED_CALLBACK,
        (msg) => {
          if (msg?.filePath === filePath) {
            rendererIPC.removeListener(
              LINKFLOW_FILE_UTIL_IS_FILE_CHANGED_WHEN_CLOSED_CALLBACK,
              listenerId
            );
            callback?.(msg);
          }
        }
      );

      rendererIPC.sendSync({
        id: LINKFLOW_FILE_UTIL_IS_FILE_CHANGED_WHEN_CLOSED,
        params: { filePath, mtimeMs },
      });
    },
  },

  onShowModal: (callback) => {
    rendererIPC.addListener(
      LINKFLOW_ON_SHOW_MODAL,
      (info) => {
        callback?.(info);
      },
      EXCLUSIVE_CON
    );
  },
  removeShowModalListener: () => {
    rendererIPC.removeListener(LINKFLOW_ON_SHOW_MODAL);
  },
  onShowNotification: (callback) => {
    rendererIPC.addListener(
      LINKFLOW_ON_SHOW_NOTIFICATION,
      (info) => {
        callback?.(info);
      },
      EXCLUSIVE_CON
    );
  },
  removeShowNotificationListener: () => {
    rendererIPC.removeListener(LINKFLOW_ON_SHOW_NOTIFICATION);
  },
  saveFile: (
    buffer: Uint8Array,
    name: string,
    ext: string,
    open: boolean,
    customSavePath
  ): void => {
    rendererIPC.send({
      id: LINKFLOW_FILE_UTIL_SAVE_FILE,
      params: { buffer, name, ext, open, customSavePath },
    });
  },

  openEasyBord: (
    vUseridOrigin: string,
    vUserId: string,
    vUserName: string,
    vDeptName: string
  ): void => {
    rendererIPC.send({
      id: LINKFLOW_OPEN_EASY_BOARD,
      params: { vUseridOrigin, vUserId, vUserName, vDeptName },
    });
  },

  openDevTools: (): void => {
    rendererIPC.send({
      id: LINKFLOW_OPEN_DEV_TOOLS,
    });
  },

  reloadApp: (): void => {
    rendererIPC.send({
      id: LINKFLOW_RELOADAPP,
    });
  },

  log: (...logs): void => {
    rendererIPC.send({
      id: LINKFLOW_LOG,
      params: logs,
    });
  },

  clearCache: (): void => {
    rendererIPC.send({
      id: LINKFLOW_CLEARCACHE,
    });
  },

  loginSuccess: (ioneUser): void => {
    rendererIPC.send({
      id: LINKFLOW_LOGIN_SUCCESS,
      params: ioneUser,
    });
  },
  logoutSuccess: (): void => {
    rendererIPC.send({
      id: LINKFLOW_LOGOUT_SUCCESS,
    });
  },
  setAgentConfig: (params) => {
    rendererIPC.send({ id: LINKFLOW_SET_AGENT_CONFIG, params });
  },
  onRedirectLoginPage: (callback) => {
    rendererIPC.addListener(
      LINKFLOW_ON_REDIRECT_LOGIN_PAGE,
      (msg) => {
        callback?.(msg);
      },
      EXCLUSIVE_CON
    );
  },
  removeRedirectLoginPage: (listenerId) => {
    rendererIPC.removeListener(LINKFLOW_ON_REDIRECT_LOGIN_PAGE, listenerId);
  },
  updater: { ...lightenUpdater },
  changeServer: (
    vBaseUrl: string,
    vBaseEitUrl: string,
    vHtdubboTag: string
  ): void => {
    rendererIPC.send({
      id: LINKFLOW_CHANGE_SERVER,
      params: {
        vBaseUrl,
        vBaseEitUrl,
        vHtdubboTag,
      },
    });
  },
  onCatchUpload: (version, catchCallback) => {
    rendererIPC.addListener(
      LINKFLOW_CATCH_UPLOAD,
      (msg) => {
        catchCallback({ ...msg, version });
      },
      EXCLUSIVE_CON
    );
  },
  onCustomCatchUpload: (version, catchCallback) => {
    // msg类型如下
    // name: string; 必填
    // message?: any;
    // labels?: string[];
    // ext?: any
    rendererIPC.addListener(
      LINKFLOW_CUSTOM_CATCH_UPLOAD,
      (msg) => {
        if (msg.labels && msg.labels instanceof Array) {
          const labels = msg.labels.concat(version);
          catchCallback({ ...msg, labels });
        } else {
          catchCallback({ ...msg, labels: [version] });
        }
      },
      EXCLUSIVE_CON
    );
  },
  onShowRenameModal: (callback) => {
    const id = rendererIPC.addListener(
      LINKFLOW_ON_SHOW_RENAME_MODAL,
      (info) => {
        callback?.(info);
      },
      APPEND_CON
    );
    return id;
  },
  removeShowRenameModal: (id) => {
    rendererIPC.removeListener(LINKFLOW_ON_SHOW_RENAME_MODAL, id);
  },
  onCloseRenameModal: (callback) => {
    const id = rendererIPC.addListener(
      LINKFLOW_ON_CLOSE_RENAME_MODAL,
      (info) => {
        callback?.(info);
      },
      APPEND_CON
    );
    return id;
  },
  removeCloseRenameModal: (id) => {
    rendererIPC.removeListener(LINKFLOW_ON_CLOSE_RENAME_MODAL, id);
  },
  onGlobalInitToRenderer: (callback) => {
    rendererIPC.addListener(
      LINKFLOW_ON_INIT_TO_RENDERER,
      (info) => {
        callback?.(info);
      },
      EXCLUSIVE_CON
    );
  },
  removeGlobalInitToRenderer: () => {
    rendererIPC.removeListener(LINKFLOW_ON_INIT_TO_RENDERER);
  },
  sendInitToMain: () => {
    rendererIPC.send({ id: LINKFLOW_SEND_INIT_TO_MAIN });
  },
  onWakeUpRendererWithUser: (callback) => {
    rendererIPC.addListener(
      LINKFLOW_ON_WAKE_UP_RENDERER_WITH_USER,
      (info) => {
        callback?.(info);
      },
      EXCLUSIVE_CON
    );
  },
  removeWakeUpRWithUserListener: () => {
    rendererIPC.removeListener(LINKFLOW_ON_WAKE_UP_RENDERER_WITH_USER);
  },
  onTransferListInfo: (callback) => {
    rendererIPC.addListener(
      LINKFLOW_TRANSFER_LIST_INFO,
      (msg) => {
        callback?.(msg);
      },
      COVER_CON
    );
  },
  getLightenVersion: (): void => {
    const lightenVersion = rendererIPC.sendSync({ id: LINKFLOW_GET_VERSION });
    return lightenVersion;
  },

  getIp: (): void => {
    const lightenIp = rendererIPC.sendSync({ id: LINKFLOW_GET_IP });
    return lightenIp;
  },

  upload: { ...lightenUpload },
  download: { ...lightenDownload },
  batchExport: { ...lightenBatchExport },
  localCollection: { ...lightenLocalCollection },
  crossStageCopy: { ...lightenCrossStageCopy },
};

export default Lighten;
