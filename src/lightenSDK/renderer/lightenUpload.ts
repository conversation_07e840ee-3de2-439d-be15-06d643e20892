import { APPEND_CON, COVER_CON } from '@htElectronSDK/constant/ipc';
import { rendererIPC } from '@htElectronSDK/renderer/ipc';
import {
  LINKFLOW_ON_UPLOAD_CALLBACK,
  LINKFLOW_ON_UPLOAD_END,
  LINKFLOW_ON_UPLOAD_ACHIEVEMENT_CALLBACK,
  LINKFLOW_UPLOAD_AT_ONCE,
  LINKFLOW_UPLOAD_BY_DRAG,
  LINKFLOW_UPLOAD_CHOOSE_FILE,
  LINKFLOW_UPLOAD_CONFIRM_DUPLICATE_DEAL,
  LINKFLOW_UPLOAD_PAUSE,
  LINKFLOW_UPLOAD_REMOVE,
  LINKFLOW_UPLOAD_RESTART,
} from '@lightenSDK/constant/upload';

export const lightenUpload = {
  onUploadProgress: (callback) => {
    rendererIPC.addListener(
      LINKFLOW_ON_UPLOAD_CALLBACK,
      (msg) => {
        callback?.(msg);
      },
      COVER_CON
    );
  },
  onUploadEnd: (callback) => {
    return rendererIPC.addListener(
      LINKFLOW_ON_UPLOAD_END,
      (info) => {
        callback?.(info);
      },
      APPEND_CON
    );
  },
  removeUploadEnd: (id) => {
    rendererIPC.removeListener(LINKFLOW_ON_UPLOAD_END, id);
  },
  chooseFile: (params) => {
    rendererIPC.send({
      id: LINKFLOW_UPLOAD_CHOOSE_FILE,
      params,
    });
  },
  confirmDuplicateDeal: (params) => {
    rendererIPC.send({
      id: LINKFLOW_UPLOAD_CONFIRM_DUPLICATE_DEAL,
      params,
    });
  },
  uploadByDrag: (params) => {
    rendererIPC.send({
      id: LINKFLOW_UPLOAD_BY_DRAG,
      params,
    });
  },
  reStart: (params) => {
    rendererIPC.send({
      id: LINKFLOW_UPLOAD_RESTART,
      params,
    });
  },
  pause: (params) => {
    rendererIPC.send({
      id: LINKFLOW_UPLOAD_PAUSE,
      params,
    });
  },
  remove: (params) => {
    rendererIPC.send({
      id: LINKFLOW_UPLOAD_REMOVE,
      params,
    });
  },
  uploadAtOnce: (params) => {
    rendererIPC.send({
      id: LINKFLOW_UPLOAD_AT_ONCE,
      params,
    });
  },
  onUploadCallback: (callback) => {
    const listenerId = rendererIPC.addListener(
      LINKFLOW_ON_UPLOAD_CALLBACK,
      (msg) => {
        callback?.(msg);
      }
    );
    return listenerId;
  },
  // 传输列表完成后相关回调，例如：任务成果上传成功后checkAchievement
  onUploadAchievementCallback: (callback) => {
    rendererIPC.addListener(
      LINKFLOW_ON_UPLOAD_ACHIEVEMENT_CALLBACK,
      (msg) => {
        callback?.(msg);
      },
      COVER_CON
      // yan COVER_CON这个参数不太明白
    );
  },
  removeUploadCallback: (listenerId) => {
    rendererIPC.removeListener(LINKFLOW_ON_UPLOAD_CALLBACK, listenerId);
  },
};
