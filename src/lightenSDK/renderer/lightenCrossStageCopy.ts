import { rendererIP<PERSON> } from '@htElectronSDK/renderer/ipc';
import {
  LINKFLOW_COPY_TO_DECLARATION_STAGE,
  LINKFLOW_CROSS_STAGE_COPY_COMPLETE,
} from '@lightenSDK/constant/crossStageCopy';
import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';

export const lightenCrossStageCopy = {
  copyToDeclarationStage: (params) => {
    rendererIPC.send({
      id: LINKFLOW_COPY_TO_DECLARATION_STAGE,
      params,
    });
  },
  onCrossStageCopyComplete: (callback) => {
    const lId = rendererIPC.addListener(
      LINKFLOW_CROSS_STAGE_COPY_COMPLETE,
      (msg) => {
        callback?.(msg);
      },
      EXCLUSIVE_CON
    );
    return lId;
  },
  removeCrossStageCopyCompleteListener: (lId) => {
    rendererIPC.removeListener(LINKFLOW_CROSS_STAGE_COPY_COMPLETE, lId);
  },
};
