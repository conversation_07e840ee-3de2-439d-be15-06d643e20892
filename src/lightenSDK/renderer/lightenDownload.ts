import { rendererIPC } from '@htElectronSDK/renderer/ipc';
import {
  LINKFLOW_DOWNLOAD_ADD,
  LINKFLOW_DOWNLOAD_AT_ONCE,
  LINKFLOW_DOWNLOAD_AT_ONCE_CANCEL,
  LINKFLOW_DOWNLOAD_BY_URL_PROCESS,
  LINKFLOW_DOWNLOAD_FILE_BY_URL,
  LINKFLOW_DOWNLOAD_PAUSE,
  LINKFLOW_DOWNLOAD_REMOVE,
  LINKFLOW_DOWNLOAD_RESTART,
  LINKFLOW_DOWNLOAD_START,
  LINKFLOW_ON_DOWNLOAD_CALLBACK,
  LINKFLOW_ON_DOWNLOAD_SAVE_PATH_DIALOG_CLOSE,
  LINKFLOW_ON_SHOW_DOWNLOAD_MODAL,
  LINKFLOW_SHOW_DOWNLOAD_SAVE_PATH_DIALOG,
} from '@lightenSDK/constant/download';

export const lightenDownload = {
  start: (params) => {
    rendererIPC.send({
      id: LINK<PERSON>OW_DOWNLOAD_START,
      params,
    });
  },
  reStart: (params) => {
    rendererIPC.send({
      id: LIN<PERSON>FLOW_DOWNLOAD_RESTART,
      params,
    });
  },
  pause: (params) => {
    rendererIPC.send({
      id: LINKFLOW_DOWNLOAD_PAUSE,
      params,
    });
  },
  remove: (params) => {
    rendererIPC.send({
      id: LINKFLOW_DOWNLOAD_REMOVE,
      params,
    });
  },
  downloadAtOnce: (params) => {
    rendererIPC.send({
      id: LINKFLOW_DOWNLOAD_AT_ONCE,
      params,
    });
  },
  cancelDownloadAtOnce: (params) => {
    rendererIPC.send({
      id: LINKFLOW_DOWNLOAD_AT_ONCE_CANCEL,
      params,
    });
  },
  onDownloadCallback: (callback) => {
    const listenerId = rendererIPC.addListener(
      LINKFLOW_ON_DOWNLOAD_CALLBACK,
      (msg: any) => {
        callback?.(msg);
      }
    );
    return listenerId;
  },
  removeDownloadCallback: (listenerId) => {
    rendererIPC.removeListener(LINKFLOW_ON_DOWNLOAD_CALLBACK, listenerId);
  },
  add: (params) => {
    rendererIPC.send({
      id: LINKFLOW_DOWNLOAD_ADD,
      params,
    });
  },
  onDownloadSavePathDialogClose: (callback) => {
    const listenerId = rendererIPC.addListener(
      LINKFLOW_ON_DOWNLOAD_SAVE_PATH_DIALOG_CLOSE,
      (msg: any) => {
        callback?.(msg);
      }
    );
    return listenerId;
  },
  removeSavePathDialogClose: (listenerId) => {
    rendererIPC.removeListener(
      LINKFLOW_ON_DOWNLOAD_SAVE_PATH_DIALOG_CLOSE,
      listenerId
    );
  },
  showDownloadSavePathDialog: () => {
    rendererIPC.send({
      id: LINKFLOW_SHOW_DOWNLOAD_SAVE_PATH_DIALOG,
    });
  },
  onShowDownloadModal: (callback) => {
    rendererIPC.addListener(LINKFLOW_ON_SHOW_DOWNLOAD_MODAL, (msg: any) => {
      callback?.(msg);
    });
  },
  downloadFileByUrl: (fileName: string, downloadPath: string): void => {
    rendererIPC.send({
      id: LINKFLOW_DOWNLOAD_FILE_BY_URL,
      params: { fileName, downloadPath },
    });
  },
  onDownloadByUrlProcess: (callback) => {
    return rendererIPC.addListener(
      LINKFLOW_DOWNLOAD_BY_URL_PROCESS,
      (msg: any) => {
        callback?.(msg);
      }
    );
  },
  removeDownloadByUrlProcessListener: (listenerId) => {
    rendererIPC.removeListener(LINKFLOW_DOWNLOAD_BY_URL_PROCESS, listenerId);
  },
};
