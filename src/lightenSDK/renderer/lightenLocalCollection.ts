import { rendererIPC } from '@htElectronSDK/renderer/ipc';
import {
  LINKFLOW_CREATE_LOCAL_COLLECTION,
  LINKFLOW_ON_CREATE_LOCAL_COLLECTION_CALLBACK,
  LINKFLOW_CANCEL_LOCAL_COLLECTION,
  LINKFLOW_ON_LC_COMPLETE,
  LINKFLOW_LOCAL_COLLECTION_UPDATE_STATUS,
  LINKFLOW_GET_LOCAL_COLLECTION_DETAIL,
} from '@lightenSDK/constant/localCollection';
import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';

export const lightenLocalCollection = {
  createLocalCollection: (params) => {
    rendererIPC.send({
      id: LINKFLOW_CREATE_LOCAL_COLLECTION,
      params,
    });
  },
  oncreateLocalCollectionCallback: (callback) => {
    const lId = rendererIPC.addListener(
      LINKFLOW_ON_CREATE_LOCAL_COLLECTION_CALLBACK,
      (msg) => {
        callback?.(msg);
      },
      EXCLUSIVE_CON
    );
    return lId;
  },
  cancelLocalCollection: (params) => {
    const flag = rendererIPC.sendSync({
      id: LINKFLOW_CANCEL_LOCAL_COLLECTION,
      params,
    });
    return flag;
  },
  getLocalCollectionDetail: (params) => {
    const res = rendererIPC.sendSync({
      id: LINKFLOW_GET_LOCAL_COLLECTION_DETAIL,
      params,
    });
    return res;
  },
  onLocalCollectionComplete: (callback) => {
    const lId = rendererIPC.addListener(
      LINKFLOW_ON_LC_COMPLETE,
      (msg) => {
        callback?.(msg);
      },
      EXCLUSIVE_CON
    );
    return lId;
  },
  removeLCCompleteListener: (lId) => {
    rendererIPC.removeListener(LINKFLOW_ON_LC_COMPLETE, lId);
  },

  onLocalCollectionUpdateStatus: (callback) => {
    const lId = rendererIPC.addListener(
      LINKFLOW_LOCAL_COLLECTION_UPDATE_STATUS,
      (msg) => {
        callback?.(msg);
      }
    );
    return lId;
  },
  removeListenLCUpdateStatus: (lId) => {
    rendererIPC.removeListener(LINKFLOW_LOCAL_COLLECTION_UPDATE_STATUS, lId);
  },
};
