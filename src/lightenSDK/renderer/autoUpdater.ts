import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';
import { rendererIPC } from '@htElectronSDK/renderer/ipc';
import {
  LINKFLOW_UPDATER_SHOW_MODAL,
  LINKFLOW_UPDATER_QUIT_INSTALL,
} from '@lightenSDK/constant/autoUpdater';

export const lightenUpdater = {
  onShowModal: (callback) => {
    rendererIPC.addListener(
      LINKFLOW_UPDATER_SHOW_MODAL,
      () => {
        callback?.();
      },
      EXCLUSIVE_CON
    );
  },
  quitAndInstall: () => {
    rendererIPC.send({
      id: LINKFLOW_UPDATER_QUIT_INSTALL,
    });
  },
};
