/**
 * 进程间通信消息常量定义
 */

// 注册全局的监听
export const LINKFLOW_ON_REDIRECT_LOGIN_PAGE = 'lighten.onRedirectLoginPage'; // 主进程触发401时，重定向到登录界面
export const LINKFLOW_SEND_INIT_TO_MAIN = 'lighten.sendInitToMain'; // 初始化，渲染进程准备就绪，发送拿取用户信息请求的消息
export const LINKFLOW_ON_INIT_TO_RENDERER = 'lighten.onInitToRenderer'; // 初始化，主进程返回用户信息，或空信息
export const LINKFLOW_ON_WAKE_UP_RENDERER_WITH_USER =
  'lighten.onWakeUpRendererWithUser'; // 协议登录，二次唤醒，有用户信息才通知渲染进程

// 登陆相关IPC MSG ID 定义
export const LINKFLOW_LOGIN_SUCCESS = 'lighten.loginSuccess'; // 登陆成功
export const LINKFLOW_LOGOUT_SUCCESS = 'lighten.logoutSuccess'; // 用户注销或登录失效，在跳转到登录页面后触发

// 代理配置
export const LINKFLOW_SET_AGENT_CONFIG = 'lighten.setAgentConfig'; // 登录成功，向主进程发送代理配置数据

// Message&Modal
export const LINKFLOW_MESSAGE_ERROR = 'lighten.message.error';
export const LINKFLOW_MESSAGE_INFO = 'lighten.message.info';
export const LINKFLOW_MESSAGE_WARN = 'lighten.message.warn';
export const LINKFLOW_MESSAGE_SUCCESS = 'lighten.message.success';
export const LINKFLOW_ON_SHOW_MODAL = 'lighten.onShowModal'; // main通知render显示一个modal
export const LINKFLOW_ON_SHOW_NOTIFICATION = 'lighten.onShowNotification'; // main通知render显示一个modalnotification
export const LINKFLOW_ON_SHOW_RENAME_MODAL = 'lighten.onShowRenameModal'; // main通知render显示一个重名的提示框
export const LINKFLOW_ON_CLOSE_RENAME_MODAL = 'lighten.onCloseRenameModal'; // main通知render关闭一个重名的提示框

// 易董链接
export const LINKFLOW_OPEN_EASY_BOARD = 'lighten.openEasyBoard';
// 切换服务器
export const LINKFLOW_CHANGE_SERVER = 'lighten.changeServer';

// 主进程异常处理通过消息传递给渲染进程，通过xlog上报
export const LINKFLOW_CATCH_UPLOAD = 'lighten.catchUpload';
export const LINKFLOW_CUSTOM_CATCH_UPLOAD = 'lighten.customCatchUpload'; // 主进程自定义异常上报

// 文件相关的API给渲染进程调用
export const LINKFLOW_FILE_UTIL_DELETE_DIR_SYNC =
  'lighten.fileUtil.deleteDirSync';
export const LINKFLOW_FILE_UTIL_CREATE_DIR = 'lighten.fileUtil.createDir';
export const LINKFLOW_FILE_UTIL_IS_FILE_SIZE_EXCEEDED =
  'lighten.fileUtil.isFileSizeExceeded';
export const LINKFLOW_FILE_UTIL_SAVE_FILE = 'lighten.saveFile';
export const LINKFLOW_FILE_UTIL_DELETE_UNTIL_SUCCESS =
  'lighten.fileUtil.deleteUntilSuccess';
export const LINKFLOW_FILE_UTIL_DELETE_UNTIL_SUCCESS_CALLBACK =
  'lighten.fileUtil.deleteUntilSuccess.callback';
export const LINKFLOW_FILE_UTIL_IS_FILE_CHANGED_WHEN_CLOSED =
  'lighten.fileUtil.isFileChangedWhenClosed';
export const LINKFLOW_FILE_UTIL_IS_FILE_CHANGED_WHEN_CLOSED_CALLBACK =
  'lighten.fileUtil.isFileChangedWhenClosed.callback';
export const LINKFLOW_FILE_UTIL_MKDir_Temp = 'lighten.fileUtil.mkdirTemp';

// 传输列表相关的数据传递
export const LINKFLOW_TRANSFER_LIST_INFO = 'lighten.transferListInfo';

// 打开调试窗口
export const LINKFLOW_OPEN_DEV_TOOLS = 'lighten.openDevTools';

// 重载应用
export const LINKFLOW_RELOADAPP = 'lighten.reloadApp';
// 清除缓存/强制刷新
export const LINKFLOW_CLEARCACHE = 'lighten.clearCache';
// 记录日志
export const LINKFLOW_LOG = 'lighten.log';

// 版本信息获取
export const LINKFLOW_GET_VERSION = 'lighten.getVersion';

// ip获取
export const LINKFLOW_GET_IP = 'lighten.getIp';
