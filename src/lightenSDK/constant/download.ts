export const LINKFLOW_DOWNLOAD_START = 'lighten.downloadStart';
export const LINKFLOW_DOWNLOAD_RESTART = 'lighten.downloadRestart';
export const LINKFLOW_DOWNLOAD_PAUSE = 'lighten.downloadPause';
export const LINKFLOW_DOWNLOAD_REMOVE = 'lighten.downloadRemove';
export const LINKFLOW_DOWNLOAD_AT_ONCE = 'lighten.downloadAtOnce';
export const LINKFLOW_DOWNLOAD_AT_ONCE_CANCEL = 'lighten.downloadAtOnceCancel';
export const LINKFLOW_ON_DOWNLOAD_CALLBACK = 'lighten.onDownloadCallback';
export const LINKFLOW_DOWNLOAD_ADD = 'lighten.downloadAdd';
export const LINKFLOW_ON_DOWNLOAD_SAVE_PATH_DIALOG_CLOSE =
  'lighten.onDownloadSavePathDialogClose';
export const LINKFLOW_SHOW_DOWNLOAD_SAVE_PATH_DIALOG =
  'lighten.showDownloadSavePathDialog';
export const LINKFLOW_ON_SHOW_DOWNLOAD_MODAL = 'lighten.onShowDownloadModal';
export const LINKFLOW_DOWNLOAD_FILE_BY_URL = 'lighten.downloadFileByUrl';
export const LINKFLOW_DOWNLOAD_BY_URL_PROCESS = 'lighten.downloadByUrlProgress';
