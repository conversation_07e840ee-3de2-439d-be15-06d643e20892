export const LINKFLOW_ON_UPLOAD_END = 'lighten.onUploadEnd';
export const LINKFLOW_UPLOAD_CHOOSE_FILE = 'lighten.uploadChooseFile';
export const LINKFLOW_UPLOAD_CONFIRM_DUPLICATE_DEAL =
  'lighten.uploadConfirmDuplicateDeal';
export const LINKFLOW_UPLOAD_BY_DRAG = 'lighten.uploadByDrag';
export const LINKFLOW_UPLOAD_RESTART = 'lighten.uploadRestart';
export const LINKFLOW_UPLOAD_PAUSE = 'lighten.uploadPause';
export const LINKFLOW_UPLOAD_REMOVE = 'lighten.uploadRemove';
export const LINKFLOW_UPLOAD_AT_ONCE = 'lighten.uploadAtOnce';
export const LINKFLOW_ON_UPLOAD_CALLBACK = 'lighten.onUploadFileCallBack';
export const LINKFLOW_ON_UPLOAD_ACHIEVEMENT_CALLBACK =
  'lighten.onUploadAchievementCallback';
