import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';
import { mainIPC } from '@htElectronSDK/main/ipc';
import LOG from '@htElectronSDK/main/log';
import {
  LINKFLOW_CLEARCACHE,
  LINKFLOW_OPEN_DEV_TOOLS,
  LINKFLOW_RELOADAPP,
  LINKFLOW_LOG,
} from '@lightenSDK/constant/global';
import { forceReload } from './menu';

mainIPC.addListener(
  LINKFLOW_OPEN_DEV_TOOLS,
  () => {
    mainIPC.getMainWindowWebContents().openDevTools();
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  LINKFLOW_RELOADAPP,
  () => {
    mainIPC.getMainWindowWebContents().reload();
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  LINKFLOW_CLEARCACHE,
  async () => {
    forceReload();
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  LINKFLOW_LOG,
  async (...logs) => {
    LOG.info(logs);
  },
  EXCLUSIVE_CON
);
