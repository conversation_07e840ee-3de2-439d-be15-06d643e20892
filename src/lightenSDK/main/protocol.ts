import { EUserType, IIoneUser } from '@globalState/user';
import * as IpcMsgId from '@lightenSDK/constant/global';
import { app } from 'electron';
import * as Path from 'path';
import { mainIPC } from '@htElectronSDK/main/ipc';
import Log from '@htElectronSDK/main/log';
import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';

// 启动程序时，如果是协议登录，缓存用户信息
let FirstProtocolLoginUser: IIoneUser | null = null;
const PROTOCOL = 'lighten';

/**
 * 注册协议
 */
export const setAsProtocolLaunchClient = () => {
  const args: string[] = [];
  if (!app.isPackaged) {
    args.push(Path.resolve(process.argv[1]));
  }
  args.push('--');
  app.setAsDefaultProtocolClient(PROTOCOL, process.execPath, args);
};

/**
 * windows常规启动/协议启动
 * @param argv
 */
export const handleProtocolLaunch4Win = (argv: string[]) => {
  Log.info('handleProtocolLaunch4Win', argv);
  const prefix = `${PROTOCOL}:`;
  const offset = app.isPackaged ? 1 : 2;
  const urlStr = argv.find((arg, i) => i >= offset && arg.startsWith(prefix));
  handleProtocolLaunch(urlStr);
};

/**
 * macos 初次启动，或者协议登录，或者协议二次唤醒，都会触发，需要区分不同场景
 * @param urlStr
 */
export function handleProtocolLaunch(urlStr?: string) {
  let currentUser: IIoneUser | null = null;
  Log.info('handleProtocolLaunch', urlStr);
  if (urlStr) {
    const urlObj = new URL(urlStr);
    const { searchParams } = urlObj;
    const params: { [key: string]: string } = {};
    for (const [key, value] of searchParams) {
      params[key] = value;
    }
    if (
      params.ioneauthorization &&
      params.userDeptList &&
      params.userid &&
      params.username &&
      params.ezid
    ) {
      try {
        const user: IIoneUser = {
          ...params,
          userid: params.userid,
          username: params.username,
          ioneauthorization: params.ioneauthorization,
          userDeptList: JSON.parse(params.userDeptList),
          ezid: Number(params.ezid) as EUserType,
        };
        Log.info('handleProtocolLaunch-IoneUser', user);
        currentUser = user;
        // 初次启动程序时，需要缓存用户信息，等待渲染进程主动询问，其他时候没有 用到
        FirstProtocolLoginUser = user;
      } catch (error) {
        Log.info('handleProtocolLaunch-JSON-error', error);
      }
    } else {
      Log.info('handleProtocolLaunch-IoneUser-params-error', params);
    }
  }
  // combo2：二次唤醒时触发，直接通知渲染进程（启动程序也会触发，但是渲染进程未就绪，消息会丢失）
  mainIPC.send({
    id: IpcMsgId.LINKFLOW_ON_WAKE_UP_RENDERER_WITH_USER,
    params: currentUser || undefined,
  });
}

/**
 * 注册“渲染进程已就绪”的监听
 */
export const initRendererReadyIPC = () => {
  // combo1：仅在程序启动时触发：渲染进程就绪，请求查询是否有用户信息
  mainIPC.addListener(
    IpcMsgId.LINKFLOW_SEND_INIT_TO_MAIN,
    () => {
      mainIPC.send({
        id: IpcMsgId.LINKFLOW_ON_INIT_TO_RENDERER,
        params: FirstProtocolLoginUser || undefined,
      });
    },
    EXCLUSIVE_CON
  );
};
