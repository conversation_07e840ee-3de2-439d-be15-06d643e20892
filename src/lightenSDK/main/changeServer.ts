import { changeBaseUrl, LightenEnv } from '@common/env';
import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { LINKFLOW_CHANGE_SERVER } from '@lightenSDK/constant/global';
import axios from 'axios';

axios.interceptors.request.use((reqConfig) => {
  if (
    (process.env.SERVER === 'sit' || process.env.SERVER === 'uat') &&
    LightenEnv.htdubboTag
  ) {
    reqConfig.params = {
      ...reqConfig.params,
      htdubboTag: LightenEnv.htdubboTag,
    };
  }
  return reqConfig;
});

mainIPC.addListener(
  LINKFLOW_CHANGE_SERVER,
  ({ vBaseUrl, vBaseEitUrl, vHtdubboTag }) => {
    changeBaseUrl(vBaseUrl, vBaseEitUrl, vHtdubboTag);
  },
  EXCLUSIVE_CON
);
