import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';
import { mainIPC } from '@htElectronSDK/main/ipc';
import LOG from '@htElectronSDK/main/log';
import {
  LINKFLOW_UPDATER_QUIT_INSTALL,
  LINKFLOW_UPDATER_SHOW_MODAL,
} from '@lightenSDK/constant/autoUpdater';
import Request from '@utils/request';
import { autoUpdater, app } from 'electron';
import { setRealQuit } from '../../main';

const url = 'https://lighten.lhzq.com:14444/lightenupdate';
let updateTimerId = null;

if (process.platform === 'darwin') {
  autoUpdater.setFeedURL({ url: `${url}/RELEASES.json`, serverType: 'json' });
} else {
  autoUpdater.setFeedURL({ url });
}

// 只有生产环境才检查更新
if (process.env.NODE_ENV === 'production' && process.env.SERVER === 'prod') {
  // 应用启动，30秒后check一下更新
  setTimeout(() => {
    LOG.info(autoUpdater.getFeedURL());
    autoUpdater.checkForUpdates();
  }, 10 * 1000);

  // 一个小时检查更新一次
  updateTimerId = setInterval(() => {
    autoUpdater.checkForUpdates();
  }, 60 * 60 * 1000);
}

autoUpdater.on('error', (message) => {
  LOG.info('There was a problem updating the application');
  message?.message && LOG.error(message.message);
  LOG.error(message);
});

autoUpdater.on('update-downloaded', async (__event) => {
  LOG.info('update-downloaded');
  const res: any = await Request<any>({ url: `${url}/RELEASES.json` });
  LOG.info('updateUrl-res?.data?.currentRelease', res?.data?.currentRelease);
  LOG.info('app.getVersion()', app.getVersion());
  if (res?.data?.currentRelease === app.getVersion()) {
    return;
  }
  mainIPC.send({
    id: LINKFLOW_UPDATER_SHOW_MODAL,
  });
});

// autoUpdater.on('update-available', (__event) => {
//   LOG.info('update-available');
// });

mainIPC.addListener(
  LINKFLOW_UPDATER_QUIT_INSTALL,
  () => {
    setRealQuit();
    autoUpdater.quitAndInstall();
  },
  EXCLUSIVE_CON
);
