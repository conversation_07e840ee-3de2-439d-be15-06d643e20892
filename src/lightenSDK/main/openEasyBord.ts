import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { LINKFLOW_OPEN_EASY_BOARD } from '@lightenSDK/constant/global';
import { shell } from 'electron';
import md5 from 'js-md5';

mainIPC.addListener(
  LINKFLOW_OPEN_EASY_BOARD,
  ({ vUseridOrigin, vUserId, vUserName, vDeptName }) => {
    function getToken(userid: string): string {
      let key = 'NWO4F0OLKT0TPGX9ETMELGXBX1ZX5CQE'; // 双方约定加密的key
      if (process.env.SERVER === 'prod') {
        key = '99WKGTYMPG51QVM91XJTVKLFIO1TO4MD';
      }
      const dateV: string = gerCurDate();
      const mdKey: string = userid + key + dateV;
      const tokenV: string = md5(mdKey);

      return tokenV;
    }

    function gerCurDate() {
      const date: Date = new Date();
      const seperator1 = '';
      const year: number = date.getFullYear();
      const month: number = date.getMonth() + 1;
      const numDate: number = date.getDate();
      let monthStr = month.toString();
      let strDate = numDate.toString();
      if (month >= 1 && month <= 9) {
        monthStr = `0${month}`;
      }
      if (numDate >= 0 && numDate <= 9) {
        strDate = `0${numDate}`;
      }
      const currentdate = year + seperator1 + monthStr + seperator1 + strDate;
      return currentdate;
    }

    const token = getToken(vUseridOrigin);

    let openUrl = '';
    if (process.env.SERVER === 'prod') {
      openUrl = 'https://th0517.easy-board.com.cn/loginVerify';
      openUrl = `${openUrl}?loginType=36&a5f0452b51c5bffd1bfa27685487917d=htlhssouser&xb5z2vzzg7v20b8ahwvpp9j8r91ep6b1=1&89ebb4141d68404c82810b2aeccbfc20=${vUserId}&89ebb4141d68404c82810b2aeccbfc29=${token}&654e6788485a4bff83678bb9cdf63fab=${vUserName}&wcfk4cc032l5qs8770nw970vk6svx99l=${vDeptName}`;
    } else {
      openUrl = 'https://th0001.valueonline.cn/loginVerify';
      openUrl = `${openUrl}?loginType=36&a5f0452b51c5bffd1bfa27685487917d=jzzxssouser&xb5z2vzzg7v20b8ahwvpp9j8r91ep6b1=1&89ebb4141d68404c82810b2aeccbfc20=${vUserId}&89ebb4141d68404c82810b2aeccbfc29=${token}&654e6788485a4bff83678bb9cdf63fab=${vUserName}&wcfk4cc032l5qs8770nw970vk6svx99l=${vDeptName}`;
    }
    shell.openExternal(openUrl);
  },
  EXCLUSIVE_CON
);
