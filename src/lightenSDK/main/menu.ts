/**
 * Top menu bar
 */
import { app, Menu, MenuItemConstructorOptions } from 'electron';
import { getAppLocalPath } from '@lightenModule/configFile';
import { readdir, stat, unlink } from 'fs/promises';
import path from 'path';
import LOG from '@htElectronSDK/main/log';
import { mainIPC } from '@htElectronSDK/main/ipc';

const templateMenu: MenuItemConstructorOptions[] = [
  {
    label: 'Edit',
    submenu: [
      { role: 'undo' },
      { role: 'redo' },
      { type: 'separator' },
      { role: 'cut' },
      { role: 'copy' },
      { role: 'paste' },
      { role: 'pasteAndMatchStyle' },
      { role: 'delete' },
      { role: 'selectAll' },
    ],
  },
  {
    label: 'View',
    submenu: [
      { role: 'reload' },
      { role: 'forceReload' },
      { role: 'toggleDevTools' },
      { type: 'separator' },
      { role: 'resetZoom' },
      { role: 'zoomIn' },
      { role: 'zoomOut' },
      { type: 'separator' },
      { role: 'togglefullscreen' },
    ],
  },
  {
    label: 'Window',
    role: 'window',
    submenu: [{ role: 'minimize' }, { role: 'close' }],
  },
  {
    label: 'Help',
    submenu: [
      {
        label: 'doc',
        submenu: [
          {
            label: 'doc',
          },
        ],
      },
    ],
  },
];

const getMenuData: () => MenuItemConstructorOptions[] = () => {
  if (process.env.SERVER === 'prod') {
    if (process.platform === 'darwin') {
      return [
        {
          label: app.getName(),
          submenu: [
            {
              role: 'quit',
              label: 'quit',
            },
          ],
        },
        {
          label: 'Edit',
          submenu: [
            { role: 'undo' },
            { role: 'redo' },
            { type: 'separator' },
            { role: 'cut' },
            { role: 'copy' },
            { role: 'paste' },
            { role: 'pasteAndMatchStyle' },
            { role: 'delete' },
            { role: 'selectAll' },
          ],
        },
      ];
    } else {
      return [];
    }
  } else {
    if (process.platform === 'darwin') {
      templateMenu.unshift({
        label: app.getName(),
        submenu: [
          {
            role: 'about',
            label: 'about',
          },
          {
            type: 'separator',
          },
          {
            label: 'preferences',
          },
          {
            type: 'separator',
          },
          {
            role: 'quit',
            label: 'quit',
          },
        ],
      });
    }
    return templateMenu;
  }
};

export const initMenu = () => {
  const menu = Menu.buildFromTemplate(getMenuData());
  Menu.setApplicationMenu(menu);
};

// 强制刷新
export const forceReload = async () => {
  // 删除文件缓存
  // const paths = getAppLocalPath();
  // const deletePath = ['blob_storage', 'Cache', 'Code Cache', 'Session Storage'];
  // const deleteDirectoryRecursive = async (dir: string) => {
  //   try {
  //     const statInfo = await stat(dir);
  //     if (statInfo.isDirectory()) {
  //       const files = await readdir(dir);
  //       for (const filePath of files) {
  //         deleteDirectoryRecursive(path.join(dir, filePath));
  //       }
  //     } else {
  //       await unlink(dir);
  //     }
  //   } catch (error) {
  //     // 会有部分文件删不掉报错
  //     LOG.info('LINKFLOW_CLEARCACHE error', error);
  //   }
  // };

  // for (const item of deletePath) {
  //   const childPath = path.join(paths, item);
  //   await deleteDirectoryRecursive(childPath);
  // }
  // 重载简富（note：reloadIgnoringCache有可能会自行删除文件缓存，如果是则上面“删除文件缓存的代码不需要”，需验证）
  mainIPC.getMainWindowWebContents().reloadIgnoringCache();
};
