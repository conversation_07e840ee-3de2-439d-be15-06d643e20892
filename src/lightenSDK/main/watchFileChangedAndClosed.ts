import childProcess from 'child_process';
import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';
import { mainIPC } from '@htElectronSDK/main/ipc';
import LOG from '@htElectronSDK/main/log';
import {
  LINKFLOW_FILE_UTIL_IS_FILE_CHANGED_WHEN_CLOSED,
  LINKFLOW_FILE_UTIL_IS_FILE_CHANGED_WHEN_CLOSED_CALLBACK,
} from '@lightenSDK/constant/global';
import { getErrorMsg } from '@utils/stringUtils';
import { stat, rename } from 'node:fs/promises';

/**
 * (macos平台)判断文件是否已在进程中打开
 * @param path
 * @note1 摘取自@ronomon/opened依赖包中的代码，原本的实现方式：
 * 1. 获取打开文件的进程
 * 2. 判断进程的name和path是否精准匹配
 * 修改为当前实现方式：只执行第一步，判断是否存在打开文件的进程，不进行精准匹配
 * 修改原因：打包后execFile命令输出的结果中文被编译为16进制，导致无法匹配
 * @todo 第一次启动office软件很慢，导致开始检测时文件还未打开
 * @todo child_process是否存在隐患 & 结果检查是否覆盖全面
 */
const isFileOpenedWithDarwin: (path: string) => Promise<boolean> = (path) => {
  return new Promise(async (resolve, reject) => {
    // lsof 是 List Open File 的缩写, 它主要用来获取被进程打开文件的信息
    const command = 'lsof';
    const args = ['-F', 'n', '--', path];
    const options = {
      encoding: 'utf-8',
      maxBuffer: 2 * 1024 * 1024,
    };
    childProcess.execFile(command, args, options, (error, stdout, stderr) => {
      // lsof returns an error and a status code of 1 if a file is not open:
      // 文件已经关闭
      if (error && error.code === 1 && stderr.length === 0) {
        resolve(true);
      } else if (error) {
        // 报错，比如文件不存在等，也处理为文件已经关闭
        resolve(true);
      } else {
        // 获取成功，表明文件未关闭
        resolve(false);
      }
    });
  });
};

const isFileClosed = (path: string): Promise<boolean> => {
  // 此接口不准，ppt、exel、word之外都有可能不准
  return new Promise(async (resolve) => {
    if (process.platform === 'darwin') {
      const res = await isFileOpenedWithDarwin(path);
      resolve(res);
    } else {
      try {
        const tempName = `${path}tempName`;
        await rename(path, tempName);
        await rename(tempName, path);
        resolve(true);
      } catch (error) {
        if (error?.code === 'EBUSY') {
          // 正常报错不做处理
          resolve(false);
        } else {
          // 文件不存在等报错，则当作已关闭
          LOG.error(getErrorMsg(error));
          resolve(true);
        }
      }
    }
  });
};

const fileChangeDetect = async (filePath, mtimeMs) => {
  try {
    // 如果文件关闭状态，则检查一下文件是否被修改
    if (await isFileClosed(filePath)) {
      const fileStat = await stat(filePath);
      if (fileStat.mtimeMs !== mtimeMs) {
        // 文件有修改
        mainIPC.send({
          id: LINKFLOW_FILE_UTIL_IS_FILE_CHANGED_WHEN_CLOSED_CALLBACK,
          params: { filePath, changed: true },
        });
      } else {
        // 文件无修改
        mainIPC.send({
          id: LINKFLOW_FILE_UTIL_IS_FILE_CHANGED_WHEN_CLOSED_CALLBACK,
          params: { filePath, changed: false },
        });
      }
    } else {
      setTimeout(() => fileChangeDetect(filePath, mtimeMs), 3000);
    }
  } catch (error) {
    mainIPC.send({
      id: LINKFLOW_FILE_UTIL_IS_FILE_CHANGED_WHEN_CLOSED_CALLBACK,
      params: { filePath, changed: false, errMsg: getErrorMsg(error) },
    });
  }
};

mainIPC.addListener(
  LINKFLOW_FILE_UTIL_IS_FILE_CHANGED_WHEN_CLOSED,
  async ({ filePath, mtimeMs }) => {
    try {
      // 轮询;
      setTimeout(() => fileChangeDetect(filePath, mtimeMs), 10000);
    } catch (err) {
      LOG.error(getErrorMsg(err));
      mainIPC.send({
        id: LINKFLOW_FILE_UTIL_IS_FILE_CHANGED_WHEN_CLOSED_CALLBACK,
        params: { filePath, changed: false, errMsg: getErrorMsg(err) },
      });
    }
  },
  EXCLUSIVE_CON
);
