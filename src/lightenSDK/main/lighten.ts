/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
/**
 * 简富独有能力实现
 */

import { FAILED_CON, SUCCESS_CON } from '@common/constant';
import { isMaxFileSizeExceeded } from '@lightenModule/uploadManager/utils/checkFile';
import { getFilesForFilePath } from '@lightenModule/uploadManager/utils/chooseFilesOrFolders';
import {
  deleteFolder,
  ensureDir,
  getFileOrDirInfo,
  getFilePathWithoutRepeat,
} from '@utils/folderUtils';
import { app, shell } from 'electron';
import fse from 'fs-extra';
import { rm } from 'node:fs';
import ipModule from 'ip';
import path from 'path';
import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';
import {
  LINKFLOW_FILE_UTIL_CREATE_DIR,
  LINKFLOW_FILE_UTIL_DELETE_DIR_SYNC,
  LINKFLOW_FILE_UTIL_DELETE_UNTIL_SUCCESS,
  LINKFLOW_FILE_UTIL_DELETE_UNTIL_SUCCESS_CALLBACK,
  LINKFLOW_FILE_UTIL_IS_FILE_SIZE_EXCEEDED,
  LINKFLOW_FILE_UTIL_MKDir_Temp,
  LINKFLOW_FILE_UTIL_SAVE_FILE,
  LINKFLOW_GET_IP,
  LINKFLOW_GET_VERSION,
} from '@lightenSDK/constant/global';
import { getErrorMsg } from '@utils/stringUtils';
import { mainIPC } from '../../htElectronSDK/main/ipc';
import LOG from '../../htElectronSDK/main/log';
import packInfo from '../../../package.json';

mainIPC.addListener(
  LINKFLOW_FILE_UTIL_DELETE_DIR_SYNC,
  ({ dir }) => {
    const delRes = deleteFolder(dir);
    if (delRes.code === SUCCESS_CON) {
      return true;
    } else {
      LOG.error(delRes.errMsg);
      return false;
    }
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  LINKFLOW_FILE_UTIL_CREATE_DIR,
  ({ dir }) => {
    const creRes = ensureDir(dir);
    if (creRes.code === FAILED_CON) {
      LOG.error(creRes.errMsg);
    }
    return creRes.code === SUCCESS_CON;
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  LINKFLOW_FILE_UTIL_IS_FILE_SIZE_EXCEEDED,
  async ({ filePaths }) => {
    let res = false;
    const fileList = filePaths instanceof Array ? filePaths : [filePaths];
    for (const aPath of fileList) {
      // 如果是文件夹选择框，那filePaths是文件夹path的列表，获取所有文件夹下的文件信息列表，为后面文件校验做准备
      const curFileOrDirInfRes = await getFileOrDirInfo(aPath);
      if (curFileOrDirInfRes.code === SUCCESS_CON) {
        const resFiles = await getFilesForFilePath(
          curFileOrDirInfRes.file.isDirectory,
          aPath
        );
        if (resFiles.code === FAILED_CON) {
          res = true;
          break;
        }

        for (const aFile of resFiles.fileList) {
          const fileSizeExceeded = isMaxFileSizeExceeded(aFile);
          if (fileSizeExceeded.code === FAILED_CON) {
            res = true;
            break;
          }
        }
      } else {
        res = false;
      }
    }
    return res;
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  LINKFLOW_FILE_UTIL_DELETE_UNTIL_SUCCESS,
  ({ parentPath }) => {
    const removeUtilSuccess = (timeIn = 10000) => {
      setTimeout(() => {
        try {
          rm(path.join(parentPath), { recursive: true }, (err) => {
            if (err) {
              if (err.errno === -4082 && err.code === 'EBUSY') {
                // 文件没有关闭
                removeUtilSuccess();
              } else {
                // 记录错误，文件不存在或者已删除的错误
                LOG.error(
                  'deleteFileAndFolderUntilSucceed',
                  parentPath,
                  getErrorMsg(err)
                );
              }
            } else {
              // 删除成功
              mainIPC.send({
                id: LINKFLOW_FILE_UTIL_DELETE_UNTIL_SUCCESS_CALLBACK,
                params: { parentPath },
              });
            }
          });
        } catch (error) {
          mainIPC.send({
            id: LINKFLOW_FILE_UTIL_DELETE_UNTIL_SUCCESS_CALLBACK,
            params: { parentPath, error: getErrorMsg(error) },
          });
          LOG.error(
            'deleteFileAndFolderUntilSucceed',
            parentPath,
            getErrorMsg(error)
          );
        }
      }, timeIn);
    };
    // yan 一个小时后自动删除临时文件
    removeUtilSuccess(1000 * 60 * 60);
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  LINKFLOW_FILE_UTIL_SAVE_FILE,
  ({ buffer, name, ext, open, customSavePath }) => {
    const downloadPath = app.getPath('downloads');
    let file_name = name;
    if (name.includes('.')) {
      file_name = path.basename(name, `.${ext}`);
    }
    const filePath = getFilePathWithoutRepeat(
      customSavePath || downloadPath,
      file_name,
      `.${ext}`
    );
    fse.writeFileSync(filePath, buffer);
    if (open) {
      shell.showItemInFolder(filePath);
    }
  },
  EXCLUSIVE_CON
);

mainIPC.addListener(
  LINKFLOW_FILE_UTIL_MKDir_Temp,
  () => {
    const tempPath = app.getPath('downloads');
    const parentPath = path.join(tempPath, `简富本地打开临时目录${Date.now()}`);
    const creRes = ensureDir(parentPath);
    if (creRes.code === FAILED_CON) {
      LOG.error(creRes.errMsg);
    }
    return creRes.code === SUCCESS_CON ? parentPath : '';
  },
  EXCLUSIVE_CON
);

// 获取版本信息
mainIPC.addListener(
  LINKFLOW_GET_VERSION,
  () => {
    return packInfo.version;
  },
  EXCLUSIVE_CON
);

// 获取ip地址
mainIPC.addListener(
  LINKFLOW_GET_IP,
  () => {
    return ipModule.address();
  },
  EXCLUSIVE_CON
);
