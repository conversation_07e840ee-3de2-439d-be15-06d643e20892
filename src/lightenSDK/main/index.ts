// 简富相关引入
import LOG from '@htElectronSDK/main/log';
import { listenDownloadByURL } from '@lightenModule/downloadManager/downloadFileByUrl';
import { initLoginModule } from '@lightenModule/login';
import HttpAgent from '@utils/httpsAgent';
import './autoUpdater';
import './changeServer';
import './watchFileChangedAndClosed';
import './lighten';
import { initMenu } from './menu';
import './openEasyBord';
import './openDevTools';
import { initRendererReadyIPC } from './protocol';

export const initLightenMain = () => {
  LOG.init('lighten-log');
  initMenu();
  listenDownloadByURL();
  initLoginModule();
  initRendererReadyIPC();
  HttpAgent.initListener();
};
