import path from 'node:path';
import os from 'os';

export const getLibSuffix = () => {
  const { platform } = process;
  const arch = os.arch();
  if (platform === 'darwin') {
    return path.join(
      `mac_${arch === 'arm64' ? 'arm64' : 'x64'}`,
      'libopenimsdk.dylib'
    );
  }
  if (platform === 'win32') {
    return path.join(
      `win_${arch === 'ia32' ? 'ia32' : 'x64'}`,
      'libopenimsdk.dll'
    );
  }
  return path.join(
    `linux_${arch === 'arm64' ? 'arm64' : 'x64'}`,
    'libopenimsdk.so'
  );
};
