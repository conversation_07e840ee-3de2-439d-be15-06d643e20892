/**
 * undefined => 'undefined', null => 'null', object => stringify，其他 toString()
 * @param vIn 需要转的对象
 */
export const toStringSafely: (vIn: any) => string = (vIn) => {
  if (vIn === undefined) {
    return 'undefined';
  } else if (vIn === null) {
    return 'undefined';
  } else if (vIn.toString() === '[object Object]') {
    try {
      return JSON.stringify(vIn);
    } catch (error: any) {
      return error.stack || error.message;
    }
  } else {
    return vIn.toString();
  }
};

/**
 * 异常信息转string
 * @param error 异常信息
 * @returns
 */
export const getErrorMsg: (error: any) => string = (error) => {
  if (error.data?.message) {
    return error.data.message;
  } else {
    return (
      error.stack ||
      error.message ||
      error.data?.message ||
      toStringSafely(error)
    );
  }
};
