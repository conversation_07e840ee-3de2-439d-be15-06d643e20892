import { CANCELED_CON, FAILED_CON, SUCCESS_CON } from '@common/constant';
import Request from '@utils/request';
import { getErrorMsg } from '@utils/stringUtils';
import * as path from 'path';
// 文件ID
export type TFileNeid = number;
// 下载相关的文件模型，由于name和ext属性无法共用FileModel
export interface IFileDownloadModel {
  accessMode: number; // 权限
  creator: string; // 创建者
  creatorUid: string; // 创建者ID
  desc?: string; // 文件备注
  dir: boolean; // 是否是目录
  modified: string; // 最后修改时间
  neid: TFileNeid; // 文件id
  path: string; // 文件路径
  pathType?: TFilePathType; // 文件类型
  rev: string; // 文件版本
  size: string; // 文件可视化大小
  realSize: number; // 文件真实大小（byte）
  updater: string; // 修改者
  updaterUid: string; // 修改者ID
  name: string; // 文件名
  ext: string; // 文件后缀，文件夹时为空串
  version?: string; // 文件版本名称
}

// 获取文件详情
export const getFileModel = (
  id: TFileNeid
): Promise<ICommonFunSimpReturn & { fileModel?: IFileDownloadModel }> => {
  return new Promise(async (resolve) => {
    try {
      const url = `/pcfile/transfer/getFileInfo`;
      const resP = await Request({
        url,
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        params: {
          id,
        },
      });
      if (resP.code === SUCCESS_CON) {
        resolve({ code: SUCCESS_CON, fileModel: formatFileModel(resP.data.t) });
      } else {
        resolve({ code: FAILED_CON, errMsg: resP.errMsg });
      }
    } catch (error) {
      resolve({ code: FAILED_CON, errMsg: getErrorMsg(error) });
    }
  });
};

// 格式化文件信息
const formatFileModel = (data: any): IFileDownloadModel => {
  try {
    return {
      accessMode: Number(data.accessMode || -1),
      creator: data.accessMode,
      creatorUid: data.creatorUid,
      desc: data.desc,
      dir: Boolean(data.dir),
      modified: data.modified,
      neid: Number(data.neid),
      path: data.path,
      pathType: data.pathType,
      rev: data.rev,
      size: data.size,
      realSize: Number(data.realSize || -1),
      updater: data.updator,
      updaterUid: data.updatorUid,
      name: path.basename(data.path),
      ext: path.extname(data.path),
      version: data.version || '',
    };
  } catch (e) {
    return {
      accessMode: -1,
      creator: '',
      creatorUid: '',
      desc: '',
      dir: false,
      modified: '',
      neid: -1,
      path: '',
      pathType: 'self',
      rev: '',
      size: '',
      realSize: -1,
      updater: '',
      updaterUid: '',
      name: '',
      ext: '',
      version: '',
    };
  }
};

const formatSerachInfo = (data): IFileDownloadModel => {
  // search接口的返回值和getFileModel略有差异，需要重写format
  try {
    return {
      accessMode: Number(data.accessMode || -1),
      creator: data.createUserName,
      creatorUid: data.createUserId,
      // 167行，type: 1返回的一定是文件
      dir: false,
      modified: data.modified,
      neid: Number(data.id),
      path: data.path,
      rev: data.versions[0].versionId,
      size: data.size,
      realSize: data.versions[0].contentLength,
      updater: data.updateUserName,
      updaterUid: data.updateUserId,
      name: path.basename(data.path),
      ext: path.extname(data.path),
      version: data.version || '',
    };
  } catch (e) {
    return {
      accessMode: -1,
      creator: '',
      creatorUid: '',
      desc: '',
      dir: false,
      modified: '',
      neid: -1,
      path: '',
      pathType: 'self',
      rev: '',
      size: '',
      realSize: -1,
      updater: '',
      updaterUid: '',
      name: '',
      ext: '',
      version: '',
    };
  }
};

type TGetFilesFromNeidFunParams = {
  neid: number;
  pathType: TFilePathType;
  pageNum?: number;
  pageSize?: number;
};

// 获取neid下所有的文件，包括子孙目录
export const getFilesFromNeidCancelable = (
  params: TGetFilesFromNeidFunParams,
  curController?: AbortController
): Promise<
  ICommonFunReturn & { fileModels?: IFileDownloadModel[]; total?: number }
> => {
  return new Promise(async (resolve) => {
    try {
      const url = `/pcfile/search`;
      const resP = await Request<{ total: number; list: any[] }>({
        url,
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        params: {
          neid: params.neid,
          pathType: params.pathType,
          pageNum: params.pageNum || 1,
          pageSize: params.pageSize || 9999,
          type: 1, // type 0 文件夹， 1 文件
        },
        signal: curController ? curController.signal : undefined,
      });
      if (resP.code === SUCCESS_CON) {
        // 目录下有文件
        if (resP.data.t) {
          resolve({
            code: SUCCESS_CON,
            total: resP.data.t.total,
            fileModels: resP.data.t.list.map((item) => formatSerachInfo(item)),
          });
        } else {
          resolve({ code: SUCCESS_CON, total: 0, fileModels: [] });
        }
      } else if (resP.code === CANCELED_CON) {
        resolve({ code: CANCELED_CON, errMsg: resP.errMsg });
      } else {
        resolve({ code: FAILED_CON, errMsg: resP.errMsg });
      }
    } catch (error) {
      resolve({ code: FAILED_CON, errMsg: getErrorMsg(error) });
    }
  });
};
