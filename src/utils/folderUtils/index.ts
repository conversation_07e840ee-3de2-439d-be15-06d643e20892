// eslint-disable-next-line eslint-comments/disable-enable-pair
/* eslint-disable @typescript-eslint/await-thenable */
// 本文件为主进程，渲染进程公用工具库，不能引入主进程的API，比如ipcMain相关的功能等
import { FAILED_CON, SUCCESS_CON } from '@common/constant';
import { getErrorMsg } from '@utils/stringUtils';
import {
  accessSync,
  constants,
  existsSync,
  mkdirSync,
  PathLike,
  rmSync,
} from 'node:fs';
import { readdir, stat } from 'node:fs/promises';
import path from 'path';
import fs from 'fs';

/**
 * 创建多层文件夹的返回值
 */

/**
 * 递归创建文件夹，比如D://workspace//test, 从左到右创建到子节点
 * @param dir 文件夹路径
 * @returns
 */
export const ensureDir: (dir: string) => ICommonFunSimpReturn = (dir) => {
  try {
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true });
    }
    return { code: SUCCESS_CON };
  } catch (error) {
    return { code: FAILED_CON, errMsg: getErrorMsg(error) };
  }
};

/**
 * 创建空文件，保证操作之前文件一定存在，如果文件已经存在，则覆盖
 * @param filePath 文件路径
 * @returns
 */
export const ensureFile: (filePath: string) => ICommonFunSimpReturn = (
  filePath
) => {
  try {
    fs.closeSync(fs.openSync(filePath, 'w'));
    return { code: SUCCESS_CON };
  } catch (error) {
    return { code: FAILED_CON, errMsg: getErrorMsg(error) };
  }
};
/**
 * 文件信息
 */
export interface IFileOrDirInfo {
  isDirectory: boolean; // 是否是文件夹
  path: string; // 文件的绝对路径
  name: string; // 文件名称
  suffix: string; // 后缀名
  size: number; // 文件大小,
  mtimeMs: number; // 文件修改时间
}

/**
 * 获取文件信息函数的返回值
 */
interface IGetFileInfoRes {
  code?: TFunReturnSimpFlag; // 是否获取成功
  errorMsg?: string; // 错误信息
  file?: IFileOrDirInfo; // 成功的话，文件信息
}

/**
 * 获取文件或文件夹信息
 * @param filePath 文件路径
 * @returns
 */
export const getFileOrDirInfo: (
  filePath: string
) => Promise<IGetFileInfoRes> = (filePath) => {
  return new Promise<IGetFileInfoRes>(async (resolve) => {
    try {
      if (filePath && typeof filePath === 'string') {
        const info = await stat(filePath);
        resolve({
          code: SUCCESS_CON,
          file: {
            isDirectory: info.isDirectory(),
            path: filePath,
            name: path.basename(filePath),
            suffix: info.isDirectory() ? '' : path.extname(filePath),
            size: info.size,
            mtimeMs: info.mtimeMs,
          },
        });
      } else {
        resolve({ code: FAILED_CON, errorMsg: '路径必须为字符串且不能为空！' });
      }
    } catch (error: any) {
      resolve({ code: FAILED_CON, errorMsg: getErrorMsg(error) });
    }
  });
};

/**
 * 获取文件夹下的子目录和文件（不包括孙节点及以下）,返回列表中有错误信息，需要自己处理一下
 * @param filePath 文件夹路径
 * @returns
 */
export const getChildFilesFromDir: (
  filePath: string
) => Promise<IGetFileInfoRes[]> = (filePath) => {
  return new Promise<IGetFileInfoRes[]>(async (resolve) => {
    try {
      const filePaths = await readdir(filePath);
      const fileListRes = [];
      for (const cFilePath of filePaths) {
        const cfileRe = await getFileOrDirInfo(path.join(filePath, cFilePath));
        fileListRes.push(cfileRe);
      }
      resolve(fileListRes);
    } catch (error: any) {
      resolve([]);
    }
  });
};

/**
 * 递归获取文件夹下所有的文件信息
 * @param fileRes IGetFileInfoRes 文件信息
 */

const getChildFilesRecursion = async (
  fileRes: IGetFileInfoRes,
  vFileListRes: IGetFileInfoRes[]
) => {
  if (fileRes?.file?.isDirectory) {
    const resP = await getChildFilesFromDir(fileRes.file.path);
    for (const item of resP) {
      await getChildFilesRecursion(item, vFileListRes);
    }
  } else {
    vFileListRes.push(fileRes);
  }
};

/**
 * 获取全部文件列表函数的返回值
 */
export interface IGetChildFilesFromDirRes {
  code?: TFunReturnSimpFlag; // 是否获取成功
  errorMsg?: string[]; // 错误列表信息，每个文件或目录一个错误信息
  fileList?: (IFileOrDirInfo | undefined)[]; // 成功情况下，返回文件列表信息
}

/**
 * 获取全部文件列表函数, 如果入参为文件路径，则返回当前文件信息
 * @param dirPath 路径
 * @returns
 */
export const getAllFilesFromDir: (
  dirPath: string
) => Promise<IGetChildFilesFromDirRes> = (dirPath) => {
  return new Promise<IGetChildFilesFromDirRes>(async (resolve) => {
    if (!dirPath || typeof dirPath !== 'string') {
      resolve({
        code: FAILED_CON,
        errorMsg: ['dirPath 路径必须为字符串且不能为空！'],
      });
    }
    const fileRes: IGetFileInfoRes[] = [];
    const cfileOrDir = await getFileOrDirInfo(dirPath);
    try {
      await getChildFilesRecursion(cfileOrDir, fileRes);
      if (fileRes.some((item) => item.code === FAILED_CON)) {
        resolve({
          code: FAILED_CON,
          errorMsg: fileRes.map((item) => item.errorMsg || ''),
        });
      } else {
        // 获取文件时过滤macos的隐藏文件夹配置文件
        const realFileList = fileRes.filter(
          (item) => item.file?.name !== '.DS_Store'
        );
        resolve({
          code: SUCCESS_CON,
          fileList: realFileList.map((item) => item.file),
        });
      }
    } catch (error: any) {
      resolve({ code: FAILED_CON, errorMsg: [getErrorMsg(error)] });
    }
  });
};

/**
 * 文件或者文件夹是否可以获得
 * @param filePath 文件路径
 * @param mode 文件模式，读、写、读写等
 * @returns 成功、失败
 */
export const isFileExist: (
  filePath: PathLike,
  mode?: number
) => TFunReturnSimpFlag = (filePath, mode = constants.O_RDWR) => {
  try {
    accessSync(filePath, mode);
    return SUCCESS_CON;
  } catch (err) {
    return FAILED_CON;
  }
};

// 删除文件夹包括所有子文件和子文件夹
export const deleteFolder: (filePath: string) => ICommonFunReturn = (
  filePath
) => {
  try {
    rmSync(filePath, { recursive: true, force: true });
    return { code: SUCCESS_CON };
  } catch (error) {
    return { code: FAILED_CON, errMsg: error.message };
  }
};

/**
 * 入参：/download/xxx.pdf, 如果download下存在/download/xxx.pdf， 则返回/download/xxx(1).pdf，以此内推
 * @param vPath 路径
 * @param fileName 文件名,例如xxx.pdf, 文件名可传xxx或者xxx.pdf
 * @param suffix 文件后缀，带[.]比如[.pdf]
 * @param index 名称顺序
 * @returns 最终的文件路径
 */
export const getFilePathWithoutRepeat = (
  vPath: string,
  vFileName: string,
  suffix?: string
) => {
  let fileName = vFileName;
  if (fileName.endsWith(suffix)) {
    fileName = fileName.substring(0, fileName.length - suffix.length);
  }
  const filePath = path.join(`${vPath}`, `${fileName}${suffix ? suffix : ''}`);
  const result = existsSync(filePath);
  if (result) {
    return getFilePathWithoutRepeat(vPath, `${fileName}(1)`, suffix);
  } else {
    return filePath;
  }
};
