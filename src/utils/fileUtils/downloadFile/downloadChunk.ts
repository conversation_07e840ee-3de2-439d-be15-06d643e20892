import { CANCELED_CON, FAILED_CON, SUCCESS_CON } from '@common/constant';
import { mainIPC } from '@htElectronSDK/main/ipc';
import LOG from '@htElectronSDK/main/log';
import { LINKFLOW_ON_SHOW_MODAL } from '@lightenSDK/constant/global';
import { ensureFile, isFileExist } from '@utils/folderUtils';
import { getErrorMsg } from '@utils/stringUtils';
import { AxiosError } from 'axios';
import fs from 'fs';

/**
 * 下载文件或文件块的结果
 */
export interface IDownloadProcessResp {
  code: TFunReturnFlag;
  message?: string;
}

/**
 * 下载文件块的自定义函数的返回值
 */
export interface IDownloadChunkFunResp {
  status: TFunReturnFlag;
  data?: fs.ReadStream;
  errorMsg?: string;
}

/**
 * 下载文件的进度监听函数
 */
export type TDownloadProcessFun = (size: number) => void | undefined;

/**
 * 下载文件块的自定义函数
 */
export type TDownloadChunkDIYFun = (
  params: unknown
) => Promise<IDownloadChunkFunResp>;

type TDownloadChunkFun = (
  downLoadChunkFun: TDownloadChunkDIYFun,
  params: unknown,
  filePath: string,
  processFun?: TDownloadProcessFun
) => Promise<IDownloadProcessResp>;

/**
 * 下载文件块
 * @param downLoadChunkFun 下载文件块的自定义函数
 * @param params 下载文件块的自定义函数的入参
 * @param filePath 下载文件块的路径
 * @param processFun 下载的进度回调函数
 * @returns IDownloadProcessResp 下载文件块的结果
 */
export const downloadChunk: TDownloadChunkFun = (
  downLoadChunkFun,
  params,
  filePath,
  processFun
) => {
  return new Promise<IDownloadProcessResp>(async (resolve) => {
    try {
      // 如果文件存在，则说明当前任务是断点续传，不做处理
      if (isFileExist(filePath) === FAILED_CON) {
        // 如果文件不存在，则先创建，创建失败直接返回，为什么先创建，因为mac上文件创建失败（比如文件路径过长）直接抛出异常，并且后面的streamRe.data.on('error'或者fileWriter.on('error'不起任何作用
        const res = ensureFile(filePath);
        if (res.code === FAILED_CON) {
          LOG.error('downloadChunk ensureFile', res.errMsg);
          resolve({ code: FAILED_CON, message: res.errMsg });
          return;
        }
      }

      const fileWriter = fs.createWriteStream(filePath, { flags: 'a+' });
      const streamRe = await downLoadChunkFun(params);

      if (streamRe.status === SUCCESS_CON && streamRe.data) {
        streamRe.data.pipe(fileWriter);
        streamRe.data.on('data', (chunk: Buffer[]) => {
          processFun?.(chunk.length);
        });
        fileWriter.on('finish', () => {
          fileWriter.close();
          resolve({ code: SUCCESS_CON });
        });
        streamRe.data.on('error', (error: AxiosError | Error) => {
          // 已经有数据返回被取消，报错在这里捕获
          fileWriter.close();
          if (
            error instanceof AxiosError &&
            error?.code === AxiosError.ERR_CANCELED
          ) {
            resolve({ code: CANCELED_CON, message: getErrorMsg(error) });
          } else {
            resolve({
              code: FAILED_CON,
              message: `读取流异常:${getErrorMsg(error)}`,
            });
          }
        });
        fileWriter.on('error', (error) => {
          fileWriter.close();
          mainIPC.send({
            id: LINKFLOW_ON_SHOW_MODAL,
            params: {
              type: 'error',
              title: '下载报错',
              content:
                '温馨提示：简富读写文件的权限可能不够，可以尝试以管理员模式重新打开简富。',
              okText: '确定',
            },
          });
          resolve({
            code: FAILED_CON,
            message: `写入流异常:${getErrorMsg(error)}`,
          });
        });
      } else if (streamRe.status === CANCELED_CON) {
        // 服务没有返回被取消，报错在这里捕获
        fileWriter.close();
        resolve({ code: CANCELED_CON, message: streamRe.errorMsg });
      } else {
        fileWriter.close();
        resolve({
          code: FAILED_CON,
          message: `下载文件块服务返回异常:${streamRe.errorMsg}`,
        });
      }
    } catch (error: any) {
      resolve({ code: FAILED_CON, message: getErrorMsg(error) });
    }
  });
};
