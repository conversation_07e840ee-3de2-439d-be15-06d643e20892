import { FAILED_CON, SUCCESS_CON } from '@common/constant';
import { getErrorMsg } from '@utils/stringUtils';
import path from 'path';
import { ensureDir } from '../../folderUtils';
import {
  downloadChunk,
  IDownloadProcessResp,
  TDownloadChunkDIYFun,
  TDownloadProcessFun,
} from './downloadChunk';

/**
 * 下载文件任务的信息
 */
export interface IDownloadInfo<T> {
  downloadFilePath: string; // 下载文件的文件路径（包含文件名）（文件所在文件夹的路径不存在时会主动创建）
  start: number; // 已下载大小
  fileSize: number; // 文件大小
  DOWNLOAD_PIECE_SIZE: number; // 分片
  file: T; // 文件对象
}

export type TGetDownloadParamsFun<T> = (
  downloadInfo: IDownloadInfo<T>,
  start: number,
  end: number
) => Promise<unknown>;

type TDownloadByMultipartFun = <T>(
  downloadChunkFun: TDownloadChunkDIYFun,
  downloadInfo: IDownloadInfo<T>,
  getDownloadParams: TGetDownloadParamsFun<T>,
  processFun?: TDownloadProcessFun
) => Promise<IDownloadProcessResp>;

type TCheckDownloadInfoFun = <T>(
  downloadInfo: IDownloadInfo<T>
) => IDownloadProcessResp;

const checkDownloadInfo: TCheckDownloadInfoFun = (downloadInfo) => {
  // 检查父目录存在不存在，因为需要在父目录里新建文件
  const downloadDirPath = path.dirname(downloadInfo.downloadFilePath);
  const createDirStatus = ensureDir(downloadDirPath);
  if (createDirStatus.code === FAILED_CON) {
    return { code: FAILED_CON, message: createDirStatus.errMsg };
  }
  if (
    typeof downloadInfo.fileSize === 'number' &&
    !isNaN(downloadInfo.fileSize) &&
    downloadInfo.fileSize <= 0
  ) {
    return {
      code: FAILED_CON,
      message: `downloadInfo.fileSize不合法，其值为：${downloadInfo.fileSize}`,
    };
  }
  return {
    code: SUCCESS_CON,
  };
};

export const downloadByMultipart: TDownloadByMultipartFun = (
  downloadChunkFun,
  downloadInfo,
  getDownloadParams,
  processFun
) => {
  return new Promise<IDownloadProcessResp>(async (resolve) => {
    try {
      const checkRes = checkDownloadInfo(downloadInfo);
      if (checkRes.code !== SUCCESS_CON) {
        resolve(checkRes);
        return;
      }
      let resData: IDownloadProcessResp = {
        code: SUCCESS_CON,
      };
      try {
        const loopCount = Math.ceil(
          (downloadInfo.fileSize - downloadInfo.start) /
            downloadInfo.DOWNLOAD_PIECE_SIZE
        );
        for (let index = 0; index < loopCount; index++) {
          // 下载不包含start，包含end，左开右闭
          const start =
            downloadInfo.start + downloadInfo.DOWNLOAD_PIECE_SIZE * index;
          const end = Math.min(
            start + downloadInfo.DOWNLOAD_PIECE_SIZE,
            downloadInfo.fileSize
          );
          const params = await getDownloadParams(downloadInfo, start, end);
          const chunkRe = await downloadChunk(
            downloadChunkFun,
            params,
            downloadInfo.downloadFilePath,
            processFun
          );
          // 成功继续循环，不成功，代表文件下载失败
          if (chunkRe.code !== SUCCESS_CON) {
            resData = { ...chunkRe };
            break;
          }
        }
        resolve(resData);
      } catch (error: any) {
        resolve({ code: 'FAILED', message: getErrorMsg(error) });
      }
    } catch (error: any) {
      resolve({ code: 'FAILED', message: getErrorMsg(error) });
    }
  });
};
