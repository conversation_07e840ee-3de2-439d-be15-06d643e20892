import { CANCELED_CON, FAILED_CON, SUCCESS_CON } from '@common/constant';
import { getErrorMsg } from '@utils/stringUtils';

/**
 * 上传文件块的结果
 */
export interface IUploadChunkResp<T> {
  code: TFunReturnFlag;
  message?: string;
  t?: T;
}

/**
 * 上传文件块的自定义函数的返回值
 */
export interface IUploadChunkFunResp<T> {
  status: TFunReturnFlag;
  errorMsg?: string;
  t?: T;
}

/**
 * 上传文件块的自定义函数
 */
export type TUploadChunkDIYFun = (
  params: unknown,
  index: number
) => Promise<IUploadChunkFunResp<unknown>>;

type TUploadChunkFun = (
  uploadChunkFun: TUploadChunkDIYFun,
  params: unknown,
  index: number
) => Promise<IUploadChunkResp<unknown>>;

/**
 * 上传文件块
 * @param uploadChunkFun 上传文件块的自定义函数
 * @param params 上传文件块的自定义函数的入参
 * @param processFun 上传的进度回调函数
 * @returns IUploadProcessResp 上传文件块的结果
 */
export const uploadChunk: TUploadChunkFun = (uploadChunkFun, params, index) => {
  return new Promise<IUploadChunkResp<unknown>>(async (resolve) => {
    try {
      const streamRe = await uploadChunkFun(params, index);

      if (streamRe.status === SUCCESS_CON) {
        resolve({ code: SUCCESS_CON, t: streamRe });
      } else if (streamRe.status === CANCELED_CON) {
        // 服务没有返回被取消，报错在这里捕获
        resolve({ code: CANCELED_CON, message: streamRe.errorMsg });
      } else {
        resolve({
          code: FAILED_CON,
          message: `上传文件块服务返回异常${streamRe.errorMsg}`,
        });
      }
    } catch (error: any) {
      resolve({ code: FAILED_CON, message: getErrorMsg(error) });
    }
  });
};
