import { FAILED_CON, SUCCESS_CON } from '@common/constant';
import { getErrorMsg } from '@utils/stringUtils';
import { TUploadChunkDIYFun, uploadChunk } from './uploadChunk';

export interface IUploadByMultipartFunResp<T> {
  code: TFunReturnFlag;
  message?: string;
  t?: T[];
}

/**
 * 上传文件任务的信息
 */
export interface IUploadInfo<T> {
  start: number; // 从第几个块开始上传
  chunkTotalNumber: number; // 上传块的总个数
  file: T; // 文件对象
}

export type TUploadProcessFun<T> = (
  uploadItem: IUploadInfo<T>,
  chunkIndex: number,
  resInfo: unknown
) => void | undefined;

/**
 * 计算上传文件服务的参数
 */
export type TGetUploadParamsFun<T> = (
  uploadInfo: IUploadInfo<T>,
  chunkIndex: number
) => Promise<unknown>;

type TUploadByMultipartFun = <T>(
  uploadChunkFun: TUploadChunkDIYFun,
  uploadInfo: IUploadInfo<T>,
  getUploadParams: TGetUploadParamsFun<T>,
  uploadProcessFun: TUploadProcessFun<T>
) => Promise<IUploadByMultipartFunResp<unknown>>;

/**
 * 上传文件
 * @param uploadChunkFun 上传文件块的自定义函数
 * @param uploadInfo 上传文件的信息
 * @param processFun 上传文件的进度监听函数
 * @param getUploadParams 计算上传文件服务的参数
 * @returns IUploadByMultipartFunResp 上传文件的结果
 */
export const uploadByMultipart: TUploadByMultipartFun = (
  uploadChunkFun,
  uploadInfo,
  getUploadParams,
  uploadProcessFun
) => {
  return new Promise<IUploadByMultipartFunResp<unknown>>(async (resolve) => {
    try {
      const resData: IUploadByMultipartFunResp<unknown> = {
        code: SUCCESS_CON,
        t: [],
      };
      try {
        for (
          let index = uploadInfo.start;
          index < uploadInfo.chunkTotalNumber;
          index++
        ) {
          const params = await getUploadParams(uploadInfo, index);
          const chunkRe = await uploadChunk(uploadChunkFun, params, index);
          // 成功继续循环，不成功，代表文件下载失败
          if (chunkRe.code === SUCCESS_CON) {
            resData.t.push(chunkRe.t);
            uploadProcessFun(uploadInfo, index, chunkRe.t);
          } else {
            resData.code = chunkRe.code;
            resData.message = chunkRe.message;
            break;
          }
        }
        resolve(resData);
      } catch (error: any) {
        resolve({ code: FAILED_CON, message: getErrorMsg(error) });
      }
    } catch (error: any) {
      resolve({ code: FAILED_CON, message: getErrorMsg(error) });
    }
  });
};
