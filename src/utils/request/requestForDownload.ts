import { CANCELED_CON, FAILED_CON, SUCCESS_CON } from '@common/constant';
import { LightenEnv } from '@common/env';
import ioneUser from '@globalState/user';
import { mainIPC } from '@htElectronSDK/main/ipc';
import LOG from '@htElectronSDK/main/log';
import { LINKFLOW_ON_REDIRECT_LOGIN_PAGE } from '@lightenSDK/constant/global';
import HttpAgent from '@utils/httpsAgent';
import { getErrorMsg } from '@utils/stringUtils';
import axios, { AxiosError, AxiosRequestConfig, HttpStatusCode } from 'axios';

interface IRequestResp<T> {
  code: TFunReturnFlag;
  errMsg?: string;
  data?: T;
}

export const axiosInit = () => {
  axios.interceptors.response.use(
    (res) => {
      return res;
    },
    (error) => {
      // 取消请求，封装一下错误，需要和AxiosResponse保持一致，因为statusText类型和AxiosError.ERR_CANCELED匹配，所以用statusText这个属性
      if (error.code === AxiosError.ERR_CANCELED) {
        return { statusText: AxiosError.ERR_CANCELED };
      }
      // 直接返回错误，如果不在这里处理一下，则401，504的错误直接抛出异常
      return error.response || error;
    }
  );
};

/**
 * 通用的请求接口
 * @param config AxiosRequestConfig实例
 * @returns Response<T>
 */
const RequestForDownload: <T>(
  config: AxiosRequestConfig
) => Promise<IRequestResp<T>> = async (config) => {
  const curUser = ioneUser.getUser();

  // 优先使用config中的baseURL
  const requestConfig: AxiosRequestConfig = {
    baseURL: LightenEnv.baseUrl,
    ...config,
    headers: {
      ...config?.headers,
      ioneauthorization: curUser?.ioneauthorization,
    },
  };

  try {
    const response = await axios(HttpAgent.connectHttpsAgent(requestConfig));
    // 请求成功
    if (response.status === HttpStatusCode.Ok) {
      return {
        code: SUCCESS_CON,
        data: response.data,
      };
    } else if (response.status === HttpStatusCode.Unauthorized) {
      // 401
      mainIPC.send({ id: LINKFLOW_ON_REDIRECT_LOGIN_PAGE });
      return {
        code: FAILED_CON,
        errMsg: '登录状态失效，请重新登录！',
      };
    } else if (response.statusText === AxiosError.ERR_CANCELED) {
      return {
        code: CANCELED_CON,
        errMsg: response.status.toString(),
      };
    } else {
      // 504等
      return {
        code: FAILED_CON,
        errMsg: response.data?.message || response.status.toString(),
      };
    }
  } catch (error) {
    LOG.info(`request error: ${getErrorMsg(error)}`);
    if (error.code === 'ECONNRESET') {
      return {
        code: FAILED_CON,
        errMsg: '网络错误，请配置代理!',
      };
    }
    return {
      code: FAILED_CON,
      errMsg: getErrorMsg(error),
    };
  }
};

export default RequestForDownload;
