import { CANCELED_CON, FAILED_CON, SUCCESS_CON } from '@common/constant';
import { LightenEnv } from '@common/env';
import ioneUser from '@globalState/user';
import { mainIPC } from '@htElectronSDK/main/ipc';
import LOG from '@htElectronSDK/main/log';
import { LINKFLOW_ON_REDIRECT_LOGIN_PAGE } from '@lightenSDK/constant/global';
import HttpAgent from '@utils/httpsAgent';
import { getErrorMsg } from '@utils/stringUtils';
import axios, { AxiosError, AxiosRequestConfig, HttpStatusCode } from 'axios';

interface IRequestResp<T> {
  code: TFunReturnFlag;
  errMsg?: string;
  data?: {
    code: number;
    message: string;
    t?: T;
  };
}

export const axiosInit = () => {
  axios.interceptors.response.use(
    (res) => {
      return res;
    },
    (error) => {
      // 取消请求，封装一下错误，需要和AxiosResponse保持一致，因为statusText类型和AxiosError.ERR_CANCELED匹配，所以用statusText这个属性
      if (error.code === AxiosError.ERR_CANCELED) {
        return { statusText: AxiosError.ERR_CANCELED };
      }
      // 直接返回错误，如果不在这里处理一下，则401，504的错误直接抛出异常
      return error.response || error;
    }
  );
};

/**
 * 通用的请求接口
 * @param config AxiosRequestConfig实例
 * @returns Response<T>
 */
const Request: <T>(
  config: AxiosRequestConfig
) => Promise<IRequestResp<T>> = async (config) => {
  const curUser = ioneUser.getUser();

  // 优先使用config中的baseURL
  const requestConfig: AxiosRequestConfig = {
    baseURL: LightenEnv.baseUrl,
    ...config,
    headers: {
      ...config?.headers,
      ioneauthorization: curUser?.ioneauthorization,
    },
  };

  try {
    const response = await axios(HttpAgent.connectHttpsAgent(requestConfig));
    // 请求成功
    if (response.status === HttpStatusCode.Ok) {
      return {
        code: SUCCESS_CON,
        data: response.data,
      };
    } else if (response.status === HttpStatusCode.Unauthorized) {
      // 401
      mainIPC.send({ id: LINKFLOW_ON_REDIRECT_LOGIN_PAGE });
      return {
        code: FAILED_CON,
        errMsg: '登录状态失效，请重新登录！',
      };
    } else if (response.statusText === AxiosError.ERR_CANCELED) {
      return {
        code: CANCELED_CON,
        errMsg: response.status.toString(),
      };
    } else {
      // 504等
      LOG.error(
        'request error',
        `url: ${config.url}, error:`,
        response.data?.message || response.status.toString()
      );
      return {
        code: FAILED_CON,
        errMsg: response.data?.message || response.status.toString(),
      };
    }
  } catch (error) {
    if (error.code === AxiosError.ERR_CANCELED) {
      return {
        code: CANCELED_CON,
        errMsg: '请求被取消',
      };
    }
    if (error.code === 'ECONNRESET') {
      return {
        code: FAILED_CON,
        errMsg: '网络错误，请配置代理!',
      };
    }
    if (error.code === 'ECONNREFUSED') {
      return {
        code: FAILED_CON,
        errMsg: '网络错误，非云桌面环境不需要配置代理，请检查!',
      };
    }
    if (error.message?.startsWith('ENOENT: no such file or directory')) {
      return {
        code: FAILED_CON,
        errMsg: '文件不存在，或被删除！',
      };
    }
    LOG.error(
      'request error',
      `url: ${config.url}, error:`,
      getErrorMsg(error)
    );
    return {
      code: FAILED_CON,
      errMsg: getErrorMsg(error),
    };
  }
};

const flagRequest = async (
  options: AxiosRequestConfig
): Promise<{ flag: boolean; errMsg?: string }> => {
  const response = await Request<null>(options);
  const { code, data, errMsg } = response;
  if (code === SUCCESS_CON && data.code === 0) {
    return { flag: true };
  } else {
    return { flag: false, errMsg };
  }
};

export default Request;
export { flagRequest };
