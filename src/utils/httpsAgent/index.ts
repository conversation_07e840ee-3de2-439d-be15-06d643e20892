import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { LINKFLOW_SET_AGENT_CONFIG } from '@lightenSDK/constant/global';
import { AxiosRequestConfig } from 'axios';
import CreateAgent, { HttpsProxyAgent } from 'https-proxy-agent';

/**
 * 将代理添加到axios的请求配置中
 * @param req
 */
const HttpAgent: {
  _httpAgent?: HttpsProxyAgent;
  initListener: () => void;
  setAgent: (agent?: string) => void;
  connectHttpsAgent: (req: AxiosRequestConfig) => AxiosRequestConfig;
} = {
  _httpAgent: undefined,
  initListener: () => {
    mainIPC.addListener(
      LINKFLOW_SET_AGENT_CONFIG,
      (data?: { agent: string }) => {
        const { agent } = data || {};
        HttpAgent.setAgent(agent);
      },
      EXCLUSIVE_CON
    );
  },
  setAgent: (agent) => {
    HttpAgent._httpAgent =
      agent && typeof agent === 'string' ? CreateAgent(agent) : undefined;
  },
  connectHttpsAgent: (req) =>
    HttpAgent._httpAgent
      ? { ...req, proxy: false, httpsAgent: HttpAgent._httpAgent }
      : req,
};

export default HttpAgent;
